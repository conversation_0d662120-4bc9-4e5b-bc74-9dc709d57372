#!/usr/bin/env python
"""
Google Calendar Push Notification Renewal Script

This script renews the Google Calendar push notification watch request
to ensure we continue receiving notifications about calendar events.
Google Calendar watch requests expire after 7 days, so this script should
be run at least once every 6 days.

Usage:
    python scheduler/renew_push.py

Environment variables:
    GOOGLE_CLIENT_ID - Google OAuth client ID
    GOOGLE_CLIENT_SECRET - Google OAuth client secret
    GOOGLE_REFRESH_TOKEN - Google OAuth refresh token
    GOOGLE_PUSH_TOKEN - Secret token for Google push notifications
    WEBHOOK_URL - URL of the webhook endpoint (must be HTTPS)
"""

import os
import uuid
import logging
import asyncio
from datetime import datetime, timedelta

import httpx

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger("google-push-renewal")

# Configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
GOOGLE_REFRESH_TOKEN = os.getenv("GOOGLE_REFRESH_TOKEN")
GOOGLE_PUSH_TOKEN = os.getenv("GOOGLE_PUSH_TOKEN")
WEBHOOK_URL = os.getenv("WEBHOOK_URL")  # Should be https://your-domain/calendar/google/push


async def get_access_token():
    """Get a new access token using the refresh token."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "https://oauth2.googleapis.com/token",
            data={
                "client_id": GOOGLE_CLIENT_ID,
                "client_secret": GOOGLE_CLIENT_SECRET,
                "refresh_token": GOOGLE_REFRESH_TOKEN,
                "grant_type": "refresh_token"
            }
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to get access token: {response.text}")
            raise Exception(f"Failed to get access token: {response.status_code}")
        
        data = response.json()
        return data["access_token"]


async def create_watch_request(access_token, channel_id=None):
    """Create a new watch request for Google Calendar events."""
    if channel_id is None:
        channel_id = str(uuid.uuid4())
    
    # Calculate expiration time (7 days from now)
    expiration = int((datetime.utcnow() + timedelta(days=7)).timestamp() * 1000)
    
    watch_data = {
        "id": channel_id,
        "type": "web_hook",
        "address": WEBHOOK_URL,
        "token": GOOGLE_PUSH_TOKEN,
        "expiration": expiration
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "https://www.googleapis.com/calendar/v3/calendars/primary/events/watch",
            json=watch_data,
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to create watch: {response.text}")
            raise Exception(f"Failed to create watch: {response.status_code}")
        
        data = response.json()
        logger.info(f"Successfully created watch. Channel ID: {data.get('id')}, Expiration: {data.get('expiration')}")
        return data


async def stop_channel(access_token, channel_id, resource_id):
    """Stop an existing notification channel."""
    stop_data = {
        "id": channel_id,
        "resourceId": resource_id
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "https://www.googleapis.com/calendar/v3/channels/stop",
            json=stop_data,
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        if response.status_code != 204:
            logger.warning(f"Failed to stop channel: {response.status_code}, {response.text}")
            return False
        
        logger.info(f"Successfully stopped channel: {channel_id}")
        return True


async def renew_push_notifications():
    """Main function to renew push notifications."""
    # Validate required environment variables
    if not all([GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REFRESH_TOKEN, GOOGLE_PUSH_TOKEN, WEBHOOK_URL]):
        logger.error("Missing required environment variables")
        return False
    
    try:
        # Get a new access token
        access_token = await get_access_token()
        
        # Create a new watch request
        watch_data = await create_watch_request(access_token)
        
        logger.info(f"Push notification renewed until {datetime.fromtimestamp(watch_data['expiration']/1000)}")
        return True
    except Exception as e:
        logger.error(f"Error renewing push notifications: {str(e)}")
        return False


if __name__ == "__main__":
    result = asyncio.run(renew_push_notifications())
    exit(0 if result else 1)
