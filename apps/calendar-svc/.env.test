# Test environment variables
ENVIRONMENT=test

# Database
DATABASE_URL=sqlite+aiosqlite:///./test.db
DATABASE_ECHO=False

# Google OAuth
GOOGLE_CLIENT_ID=test_google_client_id
GOOGLE_CLIENT_SECRET=test_google_client_secret
GOOGLE_REDIRECT_URI=http://test/callback

# Calendly OAuth
CALENDLY_CLIENT_ID=test_calendly_client_id
CALENDLY_CLIENT_SECRET=test_calendly_client_secret
CALENDLY_REDIRECT_URI=http://test/callback
CALENDLY_WEBHOOK_SECRET=test_webhook_secret

# JWT
JWT_SECRET_KEY=test_secret_key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis
REDIS_URL=redis://localhost:6379/0

# API
API_V1_STR=/api/v1
PROJECT_NAME=AI Receptionist Test

# Security
SECRET_KEY=test_secret_key

# CORS
BACKEND_CORS_ORIGINS=["*"]
