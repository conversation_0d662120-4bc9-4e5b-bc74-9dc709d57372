import json
import hmac
import hashlib
from pathlib import Path

import pytest
from sqlalchemy import select

from packages.shared import models
from app.config import settings

FIXTURES = Path(__file__).parent / "fixtures" / "json"


def load_payload(name: str):
    with open(FIXTURES / name) as f:
        return json.load(f)


def sign(body: bytes) -> str:
    return hmac.new(settings.CALENDLY_WEBHOOK_SECRET.encode(), body, hashlib.sha256).hexdigest()


@pytest.mark.asyncio
async def test_invitee_created(client, db_session, setup_database):
    payload = load_payload("invitee_created.json")
    body = json.dumps(payload).encode()
    sig = sign(body)

    response = client.post(
        "/webhooks/calendly",
        data=body,
        headers={
            "Calendly-Webhook-Signature": sig,
            "Content-Type": "application/json",
        },
    )
    assert response.status_code == 200

    result = await db_session.execute(select(models.Booking))
    bookings = result.scalars().all()
    assert len(bookings) == 1
    assert bookings[0].provider == models.BookingProvider.CALENDLY
    assert bookings[0].metadata_["event"] == "invitee.created"


@pytest.mark.asyncio
async def test_invitee_created_with_ivr_header(client, db_session, setup_database):
    payload = load_payload("invitee_created.json")
    body = json.dumps(payload).encode()
    sig = sign(body)

    response = client.post(
        "/webhooks/calendly",
        data=body,
        headers={
            "Calendly-Webhook-Signature": sig,
            "Content-Type": "application/json",
            "X-IVR": "1",
        },
    )
    assert response.status_code == 200

    result = await db_session.execute(select(models.Booking))
    bookings = result.scalars().all()
    assert bookings[0].metadata_["source"] == "ivr"


@pytest.mark.asyncio
async def test_invalid_signature(client, db_session, setup_database):
    payload = load_payload("invitee_created.json")
    body = json.dumps(payload).encode()

    response = client.post(
        "/webhooks/calendly",
        data=body,
        headers={
            "Calendly-Webhook-Signature": "bad",
            "Content-Type": "application/json",
        },
    )
    assert response.status_code == 400

    result = await db_session.execute(select(models.Booking))
    assert result.scalars().all() == []
