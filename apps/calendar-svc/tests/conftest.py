"""Test configuration and fixtures."""
import os
import sys
from pathlib import Path
from typing import Generator, AsyncGenerator, Dict, Any
from unittest.mock import MagicMock, AsyncMock

import pytest
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Set up test environment variables before importing the app
os.environ.update({
    # App settings
    "ENVIRONMENT": "test",
    "APP_ENV": "development",  # Must be one of: development, testing, staging, production
    "DEBUG": "True",
    "APP_NAME": "AI Lex Receptionist Test",
    "API_PREFIX": "/api/v1",
    "BACKEND_CORS_ORIGINS": '["*"]',
    
    # Database settings
    "DATABASE_URL": "sqlite:///./test.db",
    "DATABASE_URL_ASYNC": "sqlite+aiosqlite:///./test.db",
    "POSTGRES_SERVER": "localhost",
    "POSTGRES_USER": "testuser",
    "POSTGRES_PASSWORD": "testpassword",
    "POSTGRES_DB": "testdb",
    "POSTGRES_PORT": "5432",
    
    # Redis settings
    "REDIS_HOST": "localhost",
    "REDIS_PORT": "6379",
    "REDIS_DB": "0",
    
    # Security
    "SECRET_KEY": "test_secret_key_1234567890",
    "JWT_SECRET_KEY": "test_jwt_secret_key_1234567890",
    "JWT_ALGORITHM": "HS256",
    "JWT_ACCESS_TOKEN_EXPIRE_MINUTES": "60",
    "JWT_REFRESH_TOKEN_EXPIRE_DAYS": "30",
    "ALGORITHM": "HS256",
    "ACCESS_TOKEN_EXPIRE_MINUTES": "60",
    "REFRESH_TOKEN_EXPIRE_DAYS": "30",
    
    # OAuth settings (note: using lowercase to match the model)
    "google_client_id": "test_google_client_id",
    "google_client_secret": "test_google_client_secret",
    "google_redirect_uri": "http://test/callback",
    "calendly_client_id": "test_calendly_client_id",
    "calendly_client_secret": "test_calendly_client_secret",
    "calendly_redirect_uri": "http://test/callback",
    
    # Other settings
    "LOG_LEVEL": "INFO",
    "LOG_FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
})

# Load any .env.test file if it exists
env_path = Path(__file__).parent.parent.parent / '.env.test'
if env_path.exists():
    load_dotenv(dotenv_path=env_path, override=True)

# Add the parent directory to the path so we can import app
sys.path.append(str(Path(__file__).parent.parent))

# Now import the app and other dependencies
from app.main import create_app
from app.db.base import Base
from app.db.session import get_db

# Create test database engine
# Force the use of aiosqlite for async SQLite operations
database_url = "sqlite+aiosqlite:///./test.db"
engine = create_async_engine(
    database_url,
    connect_args={"check_same_thread": False} if "sqlite" in database_url else {},
    echo=os.getenv("DATABASE_ECHO", "").lower() == "true"
)

# Create test session
TestingSessionLocal = sessionmaker(
    autocommit=False, 
    autoflush=False, 
    bind=engine, 
    class_=AsyncSession
)

# Create test app
@pytest.fixture
def app() -> FastAPI:
    """Create a test FastAPI application."""
    return create_app()

# Create test database tables
@pytest.fixture(scope="session")
async def setup_database():
    """Create test database tables."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
        from packages import shared as shared_pkg
        await conn.run_sync(shared_pkg.models.Base.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(shared_pkg.models.Base.metadata.drop_all)

# Test client with overridden database session
@pytest.fixture
def client(app: FastAPI) -> Generator:
    """Create a test client with overridden database session."""
    # Override the database dependency
    async def override_get_db() -> AsyncGenerator[AsyncSession, None]:
        async with TestingSessionLocal() as session:
            yield session
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()

# Database session fixture
@pytest.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a database session for testing."""
    async with TestingSessionLocal() as session:
        yield session
        await session.rollback()

# Mock current user
@pytest.fixture
def mock_current_user() -> Dict[str, Any]:
    """Mock the current user for testing."""
    return {
        "id": "test_user_123",
        "email": "<EMAIL>",
        "is_active": True,
        "is_superuser": False
    }

# Mock authentication
@pytest.fixture
def mock_authenticated_user(app: FastAPI, mock_current_user: Dict[str, Any]):
    """Mock authentication for testing."""
    from app.api.dependencies.auth import get_current_user
    
    async def override_get_current_user():
        return mock_current_user
    
    app.dependency_overrides[get_current_user] = override_get_current_user
    yield
    app.dependency_overrides.pop(get_current_user, None)

# Mock Calendly service
@pytest.fixture
def mock_calendly_service():
    """Mock the CalendlyService."""
    with patch('app.services.calendly_service.CalendlyService') as mock_service:
        yield mock_service

# Mock OAuth services
@pytest.fixture
def mock_oauth_services():
    """Mock the OAuth services dictionary."""
    with patch('app.api.v1.endpoints.calendar.oauth_services') as mock_services:
        mock_services.get.return_value = AsyncMock()
        yield mock_services
