"""Tests for Calendly OAuth integration.

These tests verify the OAuth flow and token refresh functionality.
"""
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
from fastapi import HTTPException, status
from fastapi.testclient import TestClient

from app.services.calendly_service import CalendlyService, CalendlyToken
from app.models.calendar_connection import CalendarConnection
from app.schemas.calendar import OAuthState
from app.utils.exceptions import OAuthError

# Test data
TEST_USER_ID = "test_user_123"

# Test data
TEST_TOKENS = {
    "access_token": "test_access_token",
    "refresh_token": "test_refresh_token",
    "expires_in": 3600,
    "token_type": "Bearer",
    "scope": "default",
    "organization": "https://api.calendly.com/organizations/test_org"
}

@pytest.fixture
def mock_httpx_client():
    """Mock HTTPX client for testing."""
    with patch('httpx.AsyncClient') as mock_client:
        yield mock_client

@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    with patch('app.utils.cache.redis.Redis') as mock_redis:
        yield mock_redis

@pytest.fixture
def calendly_service():
    """Create a CalendlyService instance for testing."""
    return CalendlyService(
        client_id="test_client_id",
        client_secret="test_client_secret",
        redirect_uri="http://test/callback"
    )

@pytest.mark.asyncio
async def test_get_authorization_url(calendly_service):
    """Test generating the authorization URL."""
    state = "test_state"
    auth_url = calendly_service.get_authorization_url(state=state)
    
    assert "calendly.com/oauth/authorize" in auth_url
    assert f"state={state}" in auth_url
    assert "client_id=test_client_id" in auth_url
    assert "response_type=code" in auth_url
    assert "scope=" in auth_url

@pytest.mark.asyncio
async def test_get_tokens_success(calendly_service, mock_httpx_client):
    """Test successful token exchange."""
    # Setup mock response
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = TEST_TOKENS
    
    # Setup mock client
    mock_client_instance = AsyncMock()
    mock_client_instance.post.return_value = mock_response
    mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
    
    # Call the method
    token = await calendly_service.get_tokens("test_code")
    
    # Verify the request
    mock_client_instance.post.assert_called_once()
    args, kwargs = mock_client_instance.post.call_args
    assert "calendly.com/oauth/token" in args[0]
    assert kwargs["data"]["code"] == "test_code"
    assert kwargs["data"]["grant_type"] == "authorization_code"
    assert kwargs["data"]["redirect_uri"] == "http://test/callback"
    
    # Verify the response
    assert isinstance(token, CalendlyToken)
    assert token.access_token == "test_access_token"
    assert token.refresh_token == "test_refresh_token"
    assert token.token_type == "Bearer"
    assert token.organization == "https://api.calendly.com/organizations/test_org"
    assert token.scope == "default"
    assert token.expires_at > datetime.utcnow()

@pytest.mark.asyncio
async def test_refresh_token_success(calendly_service, mock_httpx_client):
    """Test successful token refresh."""
    # Setup mock response
    new_tokens = {
        **TEST_TOKENS,
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token"
    }
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = new_tokens
    
    # Setup mock client
    mock_client_instance = AsyncMock()
    mock_client_instance.post.return_value = mock_response
    mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
    
    # Call the method
    token = await calendly_service.refresh_token("test_refresh_token")
    
    # Verify the request
    mock_client_instance.post.assert_called_once()
    args, kwargs = mock_client_instance.post.call_args
    assert "calendly.com/oauth/token" in args[0]
    assert kwargs["data"]["refresh_token"] == "test_refresh_token"
    assert kwargs["data"]["grant_type"] == "refresh_token"
    
    # Verify the response
    assert isinstance(token, CalendlyToken)
    assert token.access_token == "new_access_token"
    assert token.refresh_token == "new_refresh_token"

@pytest.mark.asyncio
async def test_refresh_token_failure(calendly_service, mock_httpx_client):
    """Test token refresh failure."""
    # Setup mock error response
    mock_response = MagicMock()
    mock_response.status_code = 400
    mock_response.json.return_value = {"error": "invalid_grant", "error_description": "Refresh token expired"}
    
    # Setup mock client
    mock_client_instance = AsyncMock()
    mock_client_instance.post.return_value = mock_response
    mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
    
    # Call the method and expect an OAuthError exception
    with pytest.raises(OAuthError) as exc_info:
        await calendly_service.refresh_token("expired_refresh_token")
    
    # Verify that the error message contains appropriate details
    assert "Token refresh failed" in str(exc_info.value)
    # Since this test mocks an 'invalid_grant' error, it should be treated as a client error case

@pytest.mark.asyncio
async def test_get_authenticated_client(calendly_service):
    """Test getting an authenticated HTTP client."""
    # Create a token that's about to expire
    expires_at = datetime.utcnow() + timedelta(minutes=4)  # Within the 5-minute buffer
    token = CalendlyToken(
        access_token="test_token",
        refresh_token="test_refresh",
        expires_at=expires_at,
        organization="test_org",
        scope="test_scope"
    )
    
    # Mock the refresh method
    with patch.object(calendly_service, 'refresh_token', new_callable=AsyncMock) as mock_refresh:
        mock_refresh.return_value = CalendlyToken(
            access_token="new_token",
            refresh_token="new_refresh",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            organization="test_org",
            scope="test_scope"
        )
        
        # Get the client
        async with await calendly_service.get_authenticated_client(token) as client:
            # Verify the client was created with the new token
            assert client.headers["Authorization"] == "Bearer new_token"
            
        # Verify refresh was called
        mock_refresh.assert_called_once_with("test_refresh")

@pytest.mark.asyncio
async def test_get_current_user(calendly_service, mock_httpx_client):
    """Test getting the current user's information."""
    # Setup mock response
    user_data = {
        "resource": {
            "uri": "https://api.calendly.com/users/test_user",
            "name": "Test User",
            "email": "<EMAIL>"
        }
    }
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = user_data
    
    # Setup mock client
    mock_client_instance = AsyncMock()
    mock_client_instance.get.return_value = mock_response
    mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
    
    # Create a test token
    token = CalendlyToken(
        access_token="test_token",
        refresh_token="test_refresh",
        expires_at=datetime.utcnow() + timedelta(hours=1),
        organization="test_org",
        scope="test_scope"
    )
    
    # Call the method
    result = await calendly_service.get_current_user(token)
    
    # Verify the request
    mock_client_instance.get.assert_called_once_with("/users/me")
    
    # Verify the response
    assert result == user_data

@pytest.mark.asyncio
async def test_get_event_types(calendly_service, mock_httpx_client):
    """Test getting event types."""
    # Setup mock response
    event_types = {
        "collection": [
            {"uri": "event_type_1", "name": "15 Minute Meeting"},
            {"uri": "event_type_2", "name": "30 Minute Meeting"}
        ]
    }
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = event_types
    
    # Setup mock client
    mock_client_instance = AsyncMock()
    mock_client_instance.get.return_value = mock_response
    mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
    
    # Create a test token
    token = CalendlyToken(
        access_token="test_token",
        refresh_token="test_refresh",
        expires_at=datetime.utcnow() + timedelta(hours=1),
        organization="test_org",
        scope="test_scope"
    )
    
    # Call the method
    result = await calendly_service.get_event_types(token)
    
    # Verify the request
    mock_client_instance.get.assert_called_once_with("/event_types", params={})
    
    # Verify the response
    assert result == event_types

@pytest.mark.asyncio
async def test_create_scheduling_link(calendly_service, mock_httpx_client):
    """Test creating a scheduling link."""
    # Setup mock response
    link_data = {
        "resource": {
            "booking_url": "https://calendly.com/test/15min",
            "owner": "test_owner",
            "owner_type": "EventType"
        }
    }
    
    mock_response = MagicMock()
    mock_response.status_code = 201
    mock_response.json.return_value = link_data
    
    # Setup mock client
    mock_client_instance = AsyncMock()
    mock_client_instance.post.return_value = mock_response
    mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
    
    # Create a test token
    token = CalendlyToken(
        access_token="test_token",
        refresh_token="test_refresh",
        expires_at=datetime.utcnow() + timedelta(hours=1),
        organization="test_org",
        scope="test_scope"
    )
    
    # Call the method
    event_type_uri = "https://api.calendly.com/event_types/123"
    result = await calendly_service.create_scheduling_link(token, event_type_uri)
    
    # Verify the request
    expected_data = {
        "scheduling_link": {
            "max_events": 1,
            "owner": "EventType123",
            "owner_type": "EventType"
        }
    }
    mock_client_instance.post.assert_called_once_with("/scheduling_links", json=expected_data)
    
    # Verify the response
    assert result == link_data
