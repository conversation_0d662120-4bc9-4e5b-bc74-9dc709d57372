"""Test settings for the application."""
from pydantic import PostgresDsn
from typing import Optional

class TestSettings:
    """Test settings for the application."""
    
    # Database
    DATABASE_URL: str = "sqlite+aiosqlite:///./test.db"
    DATABASE_ECHO: bool = False
    
    # Google OAuth
    GOOGLE_CLIENT_ID: str = "test_google_client_id"
    GOOGLE_CLIENT_SECRET: str = "test_google_client_secret"
    GOOGLE_REDIRECT_URI: str = "http://test/callback"
    
    # Calendly OAuth
    CALENDLY_CLIENT_ID: str = "test_calendly_client_id"
    CALENDLY_CLIENT_SECRET: str = "test_calendly_client_secret"
    CALENDLY_REDIRECT_URI: str = "http://test/callback"
    
    # JWT
    JWT_SECRET_KEY: str = "test_secret_key"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # API
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "AI Receptionist Test"
    
    # Security
    SECRET_KEY: str = "test_secret_key"
    
    # CORS
    BACKEND_CORS_ORIGINS: list[str] = ["*"]
    
    # Environment
    ENVIRONMENT: str = "test"
    
    class Config:
        case_sensitive = True
        env_file = ".env.test"

# Create an instance of the test settings
test_settings = TestSettings()
