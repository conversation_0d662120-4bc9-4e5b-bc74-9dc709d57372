"""Tests for Calendly OAuth API endpoints."""
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from app.models.calendar_connection import CalendarConnection
from app.schemas.calendar import OAuthState
from app.services.calendly_service import CalendlyToken

# Test data
TEST_USER_ID = "test_user_123"
TEST_CONNECTION_ID = "test_conn_123"
TEST_STATE = OAuthState(
    user_id=TEST_USER_ID,
    redirect_uri="http://test/redirect",
    timestamp=datetime.utcnow().isoformat()
).json()

# Mock token data
MOCK_TOKENS = {
    "access_token": "test_access_token",
    "refresh_token": "test_refresh_token",
    "expires_in": 3600,
    "token_type": "Bearer",
    "scope": "default",
    "organization": "https://api.calendly.com/organizations/test_org"
}

@pytest.fixture
def mock_calendly_service():
    """Mock the CalendlyService."""
    with patch('app.services.calendly_service.CalendlyService') as mock_service:
        mock_instance = mock_service.return_value
        mock_instance.get_authorization_url.return_value = "https://calendly.com/oauth/authorize?test=123"
        mock_instance.get_tokens.return_value = CalendlyToken(
            access_token="test_access_token",
            refresh_token="test_refresh_token",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            organization="test_org",
            scope="test_scope"
        )
        yield mock_instance

@pytest.fixture
def mock_oauth_services():
    """Mock the OAuth services dictionary."""
    with patch('app.api.v1.endpoints.calendar.oauth_services') as mock_services:
        mock_services.get.return_value = AsyncMock()
        yield mock_services

def test_oauth_authorize_calendly(client, mock_calendly_service):
    """Test initiating Calendly OAuth flow."""
    # Make request
    response = client.get(
        f"/api/v1/oauth/calendly/authorize?user_id={TEST_USER_ID}",
        allow_redirects=False
    )
    
    # Verify response
    assert response.status_code == 307  # Redirect
    assert "calendly.com/oauth/authorize" in response.headers["location"]
    
    # Verify service was called
    mock_calendly_service.get_authorization_url.assert_called_once()

@pytest.mark.asyncio
async def test_oauth_callback_calendly(client, mock_calendly_service, mock_oauth_services):
    """Test Calendly OAuth callback."""
    # Mock the repository
    mock_repo = AsyncMock()
    mock_repo.create_connection.return_value = CalendarConnection(
        id=TEST_CONNECTION_ID,
        user_id=TEST_USER_ID,
        provider="calendly",
        access_token="test_access_token",
        refresh_token="test_refresh_token",
        token_uri="https://calendly.com/oauth/token",
        client_id="test_client_id",
        scopes=["test_scope"],
        token_expiry=(datetime.utcnow() + timedelta(hours=1)).isoformat(),
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    with patch('app.api.v1.endpoints.calendar.get_repository', return_value=mock_repo):
        # Make request
        response = client.get(
            f"/api/v1/oauth/calendly/callback?code=test_code&state={TEST_STATE}",
            allow_redirects=False
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["provider"] == "calendly"
        
        # Verify service was called
        mock_calendly_service.get_tokens.assert_called_once_with("test_code")
        mock_repo.create_connection.assert_called_once()

@pytest.mark.asyncio
async def test_oauth_callback_invalid_provider(client):
    """Test OAuth callback with an invalid provider."""
    # Make request with invalid provider
    response = client.get(
        f"/api/v1/oauth/invalid_provider/callback?code=test_code&state={TEST_STATE}",
        allow_redirects=False
    )
    
    # Verify response
    assert response.status_code == 400
    assert "Unsupported OAuth provider" in response.text

@pytest.mark.asyncio
async def test_oauth_callback_error(client):
    """Test OAuth callback with an error."""
    # Make request with error
    response = client.get(
        "/api/v1/oauth/calendly/callback?error=access_denied&error_description=User+denied+access",
        allow_redirects=False
    )
    
    # Verify response
    assert response.status_code == 400
    assert "OAuth error: User denied access" in response.text

@pytest.mark.asyncio
async def test_oauth_callback_invalid_state(client):
    """Test OAuth callback with invalid state."""
    # Make request with invalid state
    response = client.get(
        "/api/v1/oauth/calendly/callback?code=test_code&state=invalid_state",
        allow_redirects=False
    )
    
    # Verify response
    assert response.status_code == 400
    assert "Invalid state parameter" in response.text
