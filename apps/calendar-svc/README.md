# 📅 Calendar Service

This service handles calendar integration and appointment scheduling for the AI Lex Receptionist application.

## ✨ Features

- **Multi-Platform Calendar Integration**: Connect with Google Calendar and other providers
- **Smart Scheduling**: Intelligent time slot finding and conflict resolution
- **Time Zone Support**: Automatic timezone handling for global availability
- **Recurring Events**: Support for recurring appointments and availability
- **Webhook Support**: Real-time updates via webhooks
- **Comprehensive API**: RESTful API for all calendar operations

## 🏗️ Project Structure

```
app/
├── api/                  # API routes and endpoints
├── core/                 # Core application logic
├── db/                   # Database models and migrations
├── models/               # Pydantic models and schemas
├── services/             # Business logic services
├── tasks/                # Background tasks
├── utils/                # Utility modules
│   ├── cache.py         # Caching system
│   ├── config.py        # Configuration management
│   ├── datetime_utils.py # Date/time utilities
│   ├── exceptions.py    # Custom exceptions
│   ├── file_utils.py    # File operations
│   ├── http_client.py   # HTTP client
│   ├── logging.py       # Logging configuration
│   ├── tasks.py         # Background task utilities
│   └── validation.py    # Data validation
└── main.py              # FastAPI application
```

## 🚀 Getting Started

### Prerequisites

- Python 3.10+
- PostgreSQL 14+
- Redis 6+
- Google Cloud Project with Calendar API enabled
- OAuth 2.0 credentials from Google Cloud Console

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/ailex-receptionist.git
   cd ailex-receptionist/apps/calendar-svc
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

5. Run database migrations:
   ```bash
   alembic upgrade head
   ```

6. Start the development server:
   ```bash
   uvicorn app.main:app --reload
   ```

   The API will be available at http://localhost:8000

## 🔧 Utility Modules

### HTTP Client (`utils/http_client.py`)
A robust async HTTP client with built-in retries, timeouts, and error handling.

**Features:**
- Async/await support with httpx
- Request retries with exponential backoff
- JSON request/response handling
- Authentication support (API key, Bearer token)
- Response validation with Pydantic models

**Example Usage:**
```python
from utils.http_client import get_http_client

async def fetch_data():
    async with get_http_client() as client:
        response = await client.get("https://api.example.com/data")
        return await response.json()
```

### Caching (`utils/cache.py`)
A flexible caching system with support for multiple backends.

**Features:**
- Redis and in-memory backends
- TTL (time-to-live) support
- Decorators for easy function result caching
- Thread-safe operations

**Example Usage:**
```python
from utils.cache import cache_memoize

@cache_memoize(ttl=300)  # Cache for 5 minutes
async def get_expensive_data():
    # Expensive operation here
    return data
```

### Error Handling (`utils/exceptions.py`)
A comprehensive set of custom exceptions for consistent error handling.

**Features:**
- Base `AppError` class with status codes and error details
- Common HTTP exceptions (400, 401, 403, 404, etc.)
- Application-specific exceptions
- Support for error details and error codes

**Example Usage:**
```python
from utils.exceptions import NotFoundError

async def get_user(user_id: int):
    user = await User.get_or_none(id=user_id)
    if not user:
        raise NotFoundError("User not found", resource="User", resource_id=user_id)
    return user
```

### Date/Time Utilities (`utils/datetime_utils.py`)
Timezone-aware datetime handling and utilities.

**Features:**
- Timezone conversion
- Date parsing and formatting
- Business day calculations
- Time range handling

**Example Usage:**
```python
from utils.datetime_utils import parse_datetime, format_datetime

# Parse a datetime string with timezone support
dt = parse_datetime("2023-01-01T12:00:00Z", timezone="America/New_York")

# Format a datetime with a specific format
formatted = format_datetime(dt, format="%Y-%m-%d %H:%M %Z")
```

## 📚 API Documentation

### Authentication

- `GET /api/auth/google` - Initiate Google OAuth flow
- `GET /api/auth/google/callback` - OAuth callback URL
- `POST /api/auth/refresh` - Refresh access token

### Calendars

- `GET /api/calendars` - List available calendars
- `GET /api/calendars/{calendar_id}` - Get calendar details
- `POST /api/calendars` - Create a new calendar

### Availability

- `GET /api/availability` - Get available time slots
- `POST /api/availability` - Set available time slots

### Appointments

- `GET /api/appointments` - List appointments
- `POST /api/appointments` - Book an appointment
- `GET /api/appointments/{appointment_id}` - Get appointment details
- `PATCH /api/appointments/{appointment_id}` - Update an appointment
- `DELETE /api/appointments/{appointment_id}` - Cancel an appointment

## 🧪 Testing

Run the test suite:

```bash
pytest
```

Run with coverage:

```bash
pytest --cov=app --cov-report=term-missing
```

## 🚀 Deployment

The service is designed to be deployed in a containerized environment:

1. **Docker**
   ```bash
   docker-compose up --build
   ```

2. **Kubernetes**
   ```yaml
   # Example deployment YAML
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: calendar-service
   spec:
     replicas: 3
     template:
       spec:
         containers:
         - name: calendar-service
           image: your-registry/calendar-service:latest
           envFrom:
           - secretRef:
               name: calendar-secrets
   ```

## 📝 License

Proprietary - All rights reserved

---

Built with ❤️ by AI Lex Team
