from datetime import datetime
import hmac
import hashlib
import json
from typing import Any

from fastapi import APIRouter, Request, Header, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.db.base import get_async_db
from packages.shared import models

router = APIRouter()


def verify_signature(body: bytes, signature: str) -> bool:
    expected = hmac.new(
        settings.CALENDLY_WEBHOOK_SECRET.encode(),
        body,
        hashlib.sha256,
    ).hexdigest()
    return hmac.compare_digest(expected, signature)


@router.post("/webhooks/calendly")
async def calendly_webhook(
    request: Request,
    calendly_webhook_signature: str = Header(..., alias="Calendly-Webhook-Signature"),
    db: AsyncSession = Depends(get_async_db),
) -> Any:
    body = await request.body()

    if not verify_signature(body, calendly_webhook_signature):
        raise HTTPException(status_code=400, detail="Invalid signature")

    payload = json.loads(body.decode())
    event_type = payload.get("event")
    data = payload.get("payload", {})

    metadata = {"event": event_type, **data}
    if request.headers.get("X-IVR") is not None:
        metadata["source"] = "ivr"

    booking = models.Booking(
        firm_id=data.get("organization", "test_firm"),
        provider=models.BookingProvider.CALENDLY,
        external_id=data.get("event_uuid"),
        start_at=datetime.fromisoformat(data["event_start_time"]) if data.get("event_start_time") else None,
        booked_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
        provider_event_link=data.get("uri"),
        metadata_=metadata,
    )

    db.add(booking)
    await db.commit()
    return {"status": "ok"}
