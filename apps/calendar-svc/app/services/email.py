import logging
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from pathlib import Path

import resend
from jinja2 import Environment, FileSystemLoader, select_autoescape

from ...config import settings
from ...utils.jinja2_filters import register_filters

# Set up logging
logger = logging.getLogger(__name__)


class EmailService:
    def __init__(self):
        """Initialize the email service with Resend API key."""
        self.resend = resend
        self.resend.api_key = settings.RESEND_API_KEY
        
        # Set up Jinja2 environment for email templates
        self.env = Environment(
            loader=FileSystemLoader(
                Path(__file__).parent.parent / "templates" / "emails"
            ),
            autoescape=select_autoescape(["html", "xml"]),
        )
        
        # Register custom filters
        self.env = register_filters(self.env)
        
        # Add global variables to all templates
        self.env.globals.update({
            'now': datetime.utcnow,
            'project_name': settings.PROJECT_NAME,
            'support_email': settings.SUPPORT_EMAIL,
            'frontend_url': settings.FRONTEND_URL,
        })
    
    async def send_email(
        self,
        to: Union[str, List[str]],
        subject: str,
        html: str,
        from_email: Optional[str] = None,
        reply_to: Optional[str] = None,
        cc: Optional[Union[str, List[str]]] = None,
        bcc: Optional[Union[str, List[str]]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Send an email using Resend.
        
        Args:
            to: Email address or list of email addresses to send to
            subject: Email subject
            html: HTML content of the email
            from_email: Sender email address (defaults to settings.EMAILS_FROM_EMAIL)
            reply_to: Reply-to email address
            cc: CC email address or list of email addresses
            bcc: BCC email address or list of email addresses
            attachments: List of attachments
            
        Returns:
            Dict containing the API response
        """
        if not settings.EMAILS_ENABLED:
            logger.info("Email sending is disabled. Email would be sent to: %s", to)
            return {"status": "success", "message": "Email sending is disabled"}
        
        from_email = from_email or settings.EMAILS_FROM_EMAIL
        
        try:
            params = {
                "from": f"{settings.EMAILS_FROM_NAME} <{from_email}>",
                "to": [to] if isinstance(to, str) else to,
                "subject": subject,
                "html": html,
            }
            
            if reply_to:
                params["reply_to"] = reply_to
            if cc:
                params["cc"] = [cc] if isinstance(cc, str) else cc
            if bcc:
                params["bcc"] = [bcc] if isinstance(bcc, str) else bcc
            if attachments:
                params["attachments"] = attachments
            
            response = self.resend.Emails.send(params)
            logger.info("Email sent to %s with ID: %s", to, response.get("id"))
            return {"status": "success", "data": response}
            
        except Exception as e:
            logger.error("Failed to send email to %s: %s", to, str(e), exc_info=True)
            return {"status": "error", "message": str(e)}
    
    async def send_template_email(
        self,
        template_name: str,
        to: Union[str, List[str]],
        subject: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Send an email using a Jinja2 template.
        
        Args:
            template_name: Name of the template file (without .html extension)
            to: Email address or list of email addresses to send to
            subject: Email subject
            context: Dictionary of variables to pass to the template
            **kwargs: Additional arguments to pass to send_email
            
        Returns:
            Dict containing the API response
        """
        try:
            template = self.env.get_template(f"{template_name}.html")
            html_content = template.render(**(context or {}))
            
            return await self.send_email(
                to=to,
                subject=subject,
                html=html_content,
                **kwargs
            )
        except Exception as e:
            logger.error("Failed to render or send template email: %s", str(e), exc_info=True)
            return {"status": "error", "message": str(e)}


# Initialize the email service
email_service = EmailService()

# Convenience functions for common email types

async def send_reset_password_email(
    email_to: str,
    username: str,
    reset_url: str,
) -> None:
    """Send password reset email."""
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Password Reset"
    
    await email_service.send_template_email(
        template_name="reset_password",
        to=email_to,
        subject=subject,
        context={
            "project_name": project_name,
            "username": username,
            "reset_url": reset_url,
            "valid_hours": settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS,
        },
    )

async def send_new_account_email(
    email_to: str,
    username: str,
    verification_url: str,
) -> None:
    """Send new account email with verification link."""
    project_name = settings.PROJECT_NAME
    subject = f"Welcome to {project_name}!"
    
    await email_service.send_template_email(
        template_name="welcome",
        to=email_to,
        subject=subject,
        context={
            "project_name": project_name,
            "username": username,
            "verification_url": verification_url,
            "valid_hours": settings.EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS,
        },
    )

async def send_booking_confirmation_email(
    email_to: str,
    username: str,
    booking_details: Dict[str, Any],
) -> None:
    """Send booking confirmation email."""
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Booking Confirmation"
    
    await email_service.send_template_email(
        template_name="booking_confirmation",
        to=email_to,
        subject=subject,
        context={
            "project_name": project_name,
            "username": username,
            "booking": booking_details,
        },
    )

async def send_appointment_reminder_email(
    email_to: str,
    username: str,
    appointment_details: Dict[str, Any],
) -> None:
    """Send appointment reminder email."""
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Upcoming Appointment Reminder"
    
    await email_service.send_template_email(
        template_name="appointment_reminder",
        to=email_to,
        subject=subject,
        context={
            "project_name": project_name,
            "username": username,
            "appointment": appointment_details,
        },
    )
