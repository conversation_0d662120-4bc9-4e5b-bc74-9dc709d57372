"""
Calendly OAuth 2.0 service implementation.
Handles authentication, token management, and API interactions with <PERSON><PERSON><PERSON>.
"""
from typing import Dict, Optional, Any
import httpx
from datetime import datetime, timedelta
import json
import logging
from urllib.parse import urlencode

from fastapi import HTTPException, status
from pydantic import BaseModel, Field

from app.config import settings
from app.utils.cache import get_cache
from app.utils.exceptions import OAuthError

# Get the cache instance
cache = get_cache()

logger = logging.getLogger(__name__)

class CalendlyToken(BaseModel):
    """Model for Calendly OAuth tokens."""
    access_token: str
    refresh_token: str
    expires_at: datetime
    token_type: str = "Bearer"
    organization: str
    scope: str

    def is_expired(self) -> bool:
        """Check if the access token is expired or about to expire."""
        return datetime.utcnow() >= (self.expires_at - timedelta(minutes=5))

class CalendlyService:
    """Service for handling Calendly OAuth and API interactions."""
    
    # Calendly API endpoints
    AUTHORIZATION_URL = "https://auth.calendly.com/oauth/authorize"
    TOKEN_URL = "https://auth.calendly.com/oauth/token"
    API_BASE_URL = "https://api.calendly.com"
    
    # Default scopes for Calendly
    DEFAULT_SCOPES = [
        "event_types:read",
        "event_types:read:app_owned",
        "scheduling_links:read",
        "scheduling_links:read:app_owned",
        "scheduled_events:read",
        "scheduled_events:read:app_owned",
        "organization:read",
        "user:read"
    ]
    
    def __init__(self, client_id: str = None, client_secret: str = None, redirect_uri: str = None):
        """Initialize the Calendly service with OAuth credentials."""
        self.client_id = client_id or settings.CALENDLY_CLIENT_ID
        self.client_secret = client_secret or settings.CALENDLY_CLIENT_SECRET
        self.redirect_uri = redirect_uri or settings.CALENDLY_REDIRECT_URI
        
        if not all([self.client_id, self.client_secret, self.redirect_uri]):
            logger.warning("Calendly OAuth credentials not fully configured")
    
    def get_authorization_url(self, state: str = None) -> str:
        """Generate the authorization URL for Calendly OAuth flow."""
        if not self.client_id or not self.redirect_uri:
            raise OAuthError("Calendly OAuth not properly configured")
            
        params = {
            "client_id": self.client_id,
            "response_type": "code",
            "redirect_uri": self.redirect_uri,
            "scope": " ".join(self.DEFAULT_SCOPES)
        }
        
        if state:
            params["state"] = state
            
        return f"{self.AUTHORIZATION_URL}?{urlencode(params)}"
    
    async def get_tokens(self, code: str) -> CalendlyToken:
        """Exchange authorization code for access and refresh tokens."""
        if not self.client_id or not self.client_secret or not self.redirect_uri:
            raise OAuthError("Calendly OAuth not properly configured")
            
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    self.TOKEN_URL,
                    data=data,
                    headers=headers,
                    timeout=30.0
                )
                response.raise_for_status()
                token_data = response.json()
                
                # Calculate expiration time
                expires_in = token_data.get("expires_in", 3600)
                expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
                
                return CalendlyToken(
                    access_token=token_data["access_token"],
                    refresh_token=token_data["refresh_token"],
                    expires_at=expires_at,
                    token_type=token_data.get("token_type", "Bearer"),
                    organization=token_data.get("organization"),
                    scope=token_data.get("scope", "")
                )
                
            except httpx.HTTPStatusError as e:
                error_msg = f"Failed to get tokens: {e.response.text}"
                logger.error(error_msg)
                raise OAuthError("Failed to authenticate with Calendly") from e
            except Exception as e:
                logger.error(f"Unexpected error getting Calendly tokens: {str(e)}")
                raise OAuthError("Authentication failed due to an unexpected error") from e
    
    async def refresh_token(self, refresh_token: str) -> CalendlyToken:
        """Refresh an expired access token using the refresh token."""
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    self.TOKEN_URL,
                    data=data,
                    headers=headers,
                    timeout=30.0
                )
                response.raise_for_status()
                token_data = response.json()
                
                # Calculate new expiration time
                expires_in = token_data.get("expires_in", 3600)
                expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
                
                return CalendlyToken(
                    access_token=token_data["access_token"],
                    refresh_token=token_data.get("refresh_token", refresh_token),
                    expires_at=expires_at,
                    token_type=token_data.get("token_type", "Bearer"),
                    organization=token_data.get("organization", ""),
                    scope=token_data.get("scope", "")
                )
                
            except httpx.HTTPStatusError as e:
                logger.error(f"Failed to refresh Calendly token: {e.response.text}")
                raise OAuthError("Failed to refresh access token") from e
            except Exception as e:
                logger.error(f"Unexpected error refreshing Calendly token: {str(e)}")
                raise OAuthError("Token refresh failed due to an unexpected error") from e
    
    async def get_authenticated_client(self, token: CalendlyToken) -> httpx.AsyncClient:
        """Get an authenticated HTTP client with automatic token refresh."""
        if token.is_expired():
            token = await self.refresh_token(token.refresh_token)
            
        client = httpx.AsyncClient(
            base_url=self.API_BASE_URL,
            headers={
                "Authorization": f"{token.token_type} {token.access_token}",
                "Content-Type": "application/json"
            },
            timeout=30.0
        )
        return client
    
    async def get_current_user(self, token: CalendlyToken) -> Dict[str, Any]:
        """Get the current authenticated user's information."""
        async with await self.get_authenticated_client(token) as client:
            try:
                response = await client.get("/users/me")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"Failed to get Calendly user: {e.response.text}")
                raise OAuthError("Failed to retrieve user information") from e
    
    async def get_event_types(self, token: CalendlyToken, user_uri: str = None) -> Dict[str, Any]:
        """Get the event types for the authenticated user or organization."""
        params = {}
        if user_uri:
            params["user"] = user_uri
            
        async with await self.get_authenticated_client(token) as client:
            try:
                response = await client.get("/event_types", params=params)
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"Failed to get Calendly event types: {e.response.text}")
                raise OAuthError("Failed to retrieve event types") from e
    
    async def create_scheduling_link(
        self,
        token: CalendlyToken,
        event_type_uri: str,
        max_events: int = 1,
        owner: str = None,
        owner_type: str = "EventType"
    ) -> Dict[str, Any]:
        """Create a scheduling link for a specific event type."""
        data = {
            "max_events": max_events,
            "owner": owner or f"{owner_type}%s" % event_type_uri.split('/')[-1],
            "owner_type": owner_type
        }
        
        async with await self.get_authenticated_client(token) as client:
            try:
                response = await client.post("/scheduling_links", json={"scheduling_link": data})
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"Failed to create Calendly scheduling link: {e.response.text}")
                raise OAuthError("Failed to create scheduling link") from e

# Create a singleton instance of the Calendly service
calendly_service = CalendlyService()
