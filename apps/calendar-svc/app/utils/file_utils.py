import os
import shutil
import tempfile
import mimetypes
import hashlib
import gzip
import zipfile
import tarfile
from pathlib import Path
from typing import Union, BinaryIO, Tuple, Optional, List, Generator, Any, Dict, Callable
from contextlib import contextmanager
import aiofiles

from ...config import settings

# Default chunk size for file operations (64KB)
DEFAULT_CHUNK_SIZE = 64 * 1024


def get_file_hash(
    file_path: Union[str, Path],
    algorithm: str = "sha256",
    chunk_size: int = DEFAULT_CHUNK_SIZE
) -> str:
    """
    Calculate the hash of a file.
    
    Args:
        file_path: Path to the file
        algorithm: Hash algorithm to use (default: sha256)
        chunk_size: Size of chunks to read at a time
        
    Returns:
        The hexadecimal digest of the file's hash
    """
    hash_func = hashlib.new(algorithm)
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(chunk_size), b''):
            hash_func.update(chunk)
    
    return hash_func.hexdigest()


async def get_file_hash_async(
    file_path: Union[str, Path],
    algorithm: str = "sha256",
    chunk_size: int = DEFAULT_CHUNK_SIZE
) -> str:
    """
    Asynchronously calculate the hash of a file.
    
    Args:
        file_path: Path to the file
        algorithm: Hash algorithm to use (default: sha256)
        chunk_size: Size of chunks to read at a time
        
    Returns:
        The hexadecimal digest of the file's hash
    """
    hash_func = hashlib.new(algorithm)
    
    async with aiofiles.open(file_path, 'rb') as f:
        while True:
            chunk = await f.read(chunk_size)
            if not chunk:
                break
            hash_func.update(chunk)
    
    return hash_func.hexdigest()


def get_file_size(file_path: Union[str, Path]) -> int:
    """
    Get the size of a file in bytes.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Size of the file in bytes
    """
    return os.path.getsize(file_path)


def get_mime_type(file_path: Union[str, Path]) -> str:
    """
    Get the MIME type of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        The MIME type of the file
    """
    mime_type, _ = mimetypes.guess_type(file_path)
    return mime_type or 'application/octet-stream'


def get_file_extension(mime_type: str) -> str:
    """
    Get the default file extension for a MIME type.
    
    Args:
        mime_type: The MIME type
        
    Returns:
        The default file extension (including the dot), or an empty string if unknown
    """
    ext = mimetypes.guess_extension(mime_type)
    return ext or ''


def is_safe_path(base_path: Union[str, Path], path: Union[str, Path]) -> bool:
    """
    Check if a path is safe and within a base directory.
    
    Args:
        base_path: The base directory
        path: The path to check
        
    Returns:
        True if the path is safe, False otherwise
    """
    base_path = os.path.abspath(base_path)
    path = os.path.abspath(path)
    return os.path.commonpath([base_path, path]) == base_path


def ensure_directory_exists(directory: Union[str, Path]) -> Path:
    """
    Ensure that a directory exists, creating it if necessary.
    
    Args:
        directory: Path to the directory
        
    Returns:
        The path to the directory
    """
    path = Path(directory)
    path.mkdir(parents=True, exist_ok=True)
    return path


def remove_directory(directory: Union[str, Path], ignore_errors: bool = False) -> None:
    """
    Remove a directory and all its contents.
    
    Args:
        directory: Path to the directory
        ignore_errors: If True, ignore errors during removal
    """
    shutil.rmtree(directory, ignore_errors=ignore_errors)


@contextmanager
def temp_directory(suffix: str = '', prefix: str = 'tmp', base_dir: Optional[Union[str, Path]] = None):
    """
    Context manager that creates a temporary directory and removes it when done.
    
    Args:
        suffix: Directory name suffix
        prefix: Directory name prefix
        base_dir: Base directory for the temporary directory
        
    Yields:
        Path to the temporary directory
    """
    temp_dir = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=base_dir)
    try:
        yield Path(temp_dir)
    finally:
        remove_directory(temp_dir, ignore_errors=True)


def compress_file(
    input_path: Union[str, Path],
    output_path: Optional[Union[str, Path]] = None,
    compression: str = 'gz'
) -> Path:
    """
    Compress a file using gzip or another compression algorithm.
    
    Args:
        input_path: Path to the input file
        output_path: Path to the output file (default: input_path + .gz)
        compression: Compression algorithm ('gz' or 'bz2')
        
    Returns:
        Path to the compressed file
    """
    input_path = Path(input_path)
    
    if output_path is None:
        output_path = f"{input_path}.{compression}"
    
    if compression == 'gz':
        with open(input_path, 'rb') as f_in, gzip.open(output_path, 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
    elif compression == 'bz2':
        import bz2
        with open(input_path, 'rb') as f_in, bz2.open(output_path, 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
    else:
        raise ValueError(f"Unsupported compression: {compression}")
    
    return Path(output_path)


def decompress_file(
    input_path: Union[str, Path],
    output_path: Optional[Union[str, Path]] = None,
    compression: Optional[str] = None
) -> Path:
    """
    Decompress a file.
    
    Args:
        input_path: Path to the input file
        output_path: Path to the output file (default: input_path without compression extension)
        compression: Compression algorithm ('gz' or 'bz2'), or None to guess from extension
        
    Returns:
        Path to the decompressed file
    """
    input_path = Path(input_path)
    
    if compression is None:
        if input_path.suffix == '.gz':
            compression = 'gz'
        elif input_path.suffix == '.bz2':
            compression = 'bz2'
        else:
            raise ValueError("Could not determine compression type from file extension")
    
    if output_path is None:
        if compression == 'gz' and input_path.suffix == '.gz':
            output_path = input_path.with_suffix('')
        else:
            output_path = input_path.with_suffix('')
    
    if compression == 'gz':
        with gzip.open(input_path, 'rb') as f_in, open(output_path, 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
    elif compression == 'bz2':
        import bz2
        with bz2.open(input_path, 'rb') as f_in, open(output_path, 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
    else:
        raise ValueError(f"Unsupported compression: {compression}")
    
    return Path(output_path)


def create_zip_archive(
    source_path: Union[str, Path],
    output_path: Optional[Union[str, Path]] = None,
    include_hidden: bool = False
) -> Path:
    """
    Create a ZIP archive of a directory or file.
    
    Args:
        source_path: Path to the source directory or file
        output_path: Path to the output ZIP file (default: source_path + .zip)
        include_hidden: Whether to include hidden files (starting with '.')
        
    Returns:
        Path to the created ZIP file
    """
    source_path = Path(source_path)
    
    if output_path is None:
        output_path = f"{source_path}.zip"
    output_path = Path(output_path)
    
    def _filter_files(path: Path) -> bool:
        if not include_hidden and path.name.startswith('.'):
            return False
        return True
    
    with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        if source_path.is_file():
            if _filter_files(source_path):
                zipf.write(source_path, source_path.name)
        else:
            for file_path in source_path.rglob('*'):
                if _filter_files(file_path):
                    arcname = file_path.relative_to(source_path)
                    zipf.write(file_path, arcname)
    
    return output_path


extract_zip_archive = create_zip_archive  # Alias for backward compatibility


def create_tar_archive(
    source_path: Union[str, Path],
    output_path: Optional[Union[str, Path]] = None,
    compression: str = 'gz',
    include_hidden: bool = False
) -> Path:
    """
    Create a TAR archive of a directory or file.
    
    Args:
        source_path: Path to the source directory or file
        output_path: Path to the output TAR file (default: source_path + .tar.gz)
        compression: Compression type ('gz', 'bz2', 'xz', or '' for no compression)
        include_hidden: Whether to include hidden files (starting with '.')
        
    Returns:
        Path to the created TAR file
    """
    source_path = Path(source_path)
    
    if output_path is None:
        ext = {
            '': '.tar',
            'gz': '.tar.gz',
            'bz2': '.tar.bz2',
            'xz': '.tar.xz'
        }.get(compression, '.tar.gz')
        output_path = f"{source_path}{ext}"
    output_path = Path(output_path)
    
    mode = f"w:{compression}" if compression else 'w'
    
    def _filter_files(tarinfo):
        if not include_hidden and '/' in tarinfo.name and tarinfo.name.split('/')[-1].startswith('.'):
            return None
        return tarinfo
    
    with tarfile.open(output_path, mode) as tar:
        if source_path.is_file():
            tar.add(source_path, arcname=source_path.name, filter=_filter_files)
        else:
            tar.add(source_path, arcname='.', filter=_filter_files)
    
    return output_path


extract_tar_archive = create_tar_archive  # Alias for backward compatibility


def find_files(
    directory: Union[str, Path],
    pattern: str = '*',
    recursive: bool = True,
    include_dirs: bool = False
) -> List[Path]:
    """
    Find files matching a pattern in a directory.
    
    Args:
        directory: Directory to search in
        pattern: File pattern to match (e.g., '*.txt')
        recursive: Whether to search recursively
        include_dirs: Whether to include directories in the results
        
    Returns:
        List of matching file paths
    """
    directory = Path(directory)
    
    if recursive:
        return [
            p for p in directory.rglob(pattern)
            if include_dirs or p.is_file()
        ]
    else:
        return [
            p for p in directory.glob(pattern)
            if include_dirs or p.is_file()
        ]


def count_lines(file_path: Union[str, Path]) -> int:
    """
    Count the number of lines in a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Number of lines in the file
    """
    with open(file_path, 'rb') as f:
        return sum(1 for _ in f)


async def count_lines_async(file_path: Union[str, Path]) -> int:
    """
    Asynchronously count the number of lines in a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Number of lines in the file
    """
    count = 0
    async with aiofiles.open(file_path, 'rb') as f:
        async for _ in f:
            count += 1
    return count


def file_chunks(
    file_path: Union[str, Path],
    chunk_size: int = DEFAULT_CHUNK_SIZE
) -> Generator[bytes, None, None]:
    """
    Read a file in chunks.
    
    Args:
        file_path: Path to the file
        chunk_size: Size of each chunk in bytes
        
    Yields:
        File chunks as bytes
    """
    with open(file_path, 'rb') as f:
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                break
            yield chunk


async def file_chunks_async(
    file_path: Union[str, Path],
    chunk_size: int = DEFAULT_CHUNK_SIZE
) -> Generator[bytes, None, None]:
    """
    Asynchronously read a file in chunks.
    
    Args:
        file_path: Path to the file
        chunk_size: Size of each chunk in bytes
        
    Yields:
        File chunks as bytes
    """
    async with aiofiles.open(file_path, 'rb') as f:
        while True:
            chunk = await f.read(chunk_size)
            if not chunk:
                break
            yield chunk
