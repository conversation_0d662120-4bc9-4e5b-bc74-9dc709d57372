import asyncio
import logging
import traceback
from typing import Any, Callable, Coroutine, Dict, List, Optional, TypeVar, Union
from datetime import datetime, timedelta
from enum import Enum
from uuid import uuid4, UUID

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar('T')
TaskFunc = Callable[..., Coroutine[Any, Any, T]]


class TaskStatus(str, Enum):
    """Status of a background task."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskResult(BaseModel):
    """Result of a background task."""
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    traceback: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    @property
    def duration(self) -> Optional[float]:
        """Get the duration of the task in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def is_done(self) -> bool:
        """Check if the task is done (completed, failed, or cancelled)."""
        return self.status in {TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED}


class BackgroundTask:
    """A background task that can be tracked and managed."""
    
    def __init__(
        self,
        func: TaskFunc,
        *args: Any,
        **kwargs: Any
    ):
        self.id = str(uuid4())
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.task: Optional[asyncio.Task] = None
        self.result = TaskResult()
        self._on_complete_callbacks: List[Callable[['BackgroundTask'], None]] = []
        self._on_failure_callbacks: List[Callable[['BackgroundTask'], None]] = []
        self._cancelled = False
    
    def start(self) -> 'BackgroundTask':
        """Start the background task."""
        if self.task is not None and not self.task.done():
            raise RuntimeError("Task is already running")
        
        self.result = TaskResult(
            status=TaskStatus.RUNNING,
            start_time=datetime.utcnow()
        )
        
        self.task = asyncio.create_task(self._run())
        return self
    
    async def _run(self) -> None:
        """Run the task and handle the result."""
        try:
            if self._cancelled:
                self.result.status = TaskStatus.CANCELLED
                self.result.end_time = datetime.utcnow()
                return
            
            # Execute the task
            result = await self.func(*self.args, **self.kwargs)
            
            # Update the result
            self.result.status = TaskStatus.COMPLETED
            self.result.result = result
            self.result.end_time = datetime.utcnow()
            
            # Call completion callbacks
            for callback in self._on_complete_callbacks:
                try:
                    callback(self)
                except Exception as e:
                    logger.error(f"Error in on_complete callback: {e}", exc_info=True)
        
        except asyncio.CancelledError:
            self.result.status = TaskStatus.CANCELLED
            self.result.end_time = datetime.utcnow()
            
            # Call failure callbacks
            for callback in self._on_failure_callbacks:
                try:
                    callback(self)
                except Exception as e:
                    logger.error(f"Error in on_failure callback: {e}", exc_info=True)
            raise
        
        except Exception as e:
            self.result.status = TaskStatus.FAILED
            self.result.error = str(e)
            self.result.traceback = traceback.format_exc()
            self.result.end_time = datetime.utcnow()
            
            logger.error(f"Background task failed: {e}", exc_info=True)
            
            # Call failure callbacks
            for callback in self._on_failure_callbacks:
                try:
                    callback(self)
                except Exception as e:
                    logger.error(f"Error in on_failure callback: {e}", exc_info=True)
    
    def cancel(self) -> bool:
        """Cancel the task if it's running."""
        if self.task is None:
            return False
        
        if not self.task.done():
            self._cancelled = True
            self.task.cancel()
            return True
        
        return False
    
    def on_complete(self, callback: Callable[['BackgroundTask'], None]) -> 'BackgroundTask':
        """Register a callback to be called when the task completes successfully."""
        self._on_complete_callbacks.append(callback)
        return self
    
    def on_failure(self, callback: Callable[['BackgroundTask'], None]) -> 'BackgroundTask':
        """Register a callback to be called when the task fails or is cancelled."""
        self._on_failure_callbacks.append(callback)
        return self
    
    async def wait(self, timeout: Optional[float] = None) -> TaskResult:
        """Wait for the task to complete."""
        if self.task is None:
            raise RuntimeError("Task has not been started")
        
        try:
            if timeout is not None:
                await asyncio.wait_for(self.task, timeout=timeout)
            else:
                await self.task
        except (asyncio.TimeoutError, asyncio.CancelledError):
            pass
        
        return self.result


class TaskScheduler:
    """A scheduler for running background tasks at specific times or intervals."""
    
    def __init__(self):
        self.tasks: Dict[str, BackgroundTask] = {}
        self.scheduled_tasks: Dict[str, asyncio.Task] = {}
    
    def create_task(
        self,
        func: TaskFunc,
        *args: Any,
        **kwargs: Any
    ) -> BackgroundTask:
        """Create and start a new background task."""
        task = BackgroundTask(func, *args, **kwargs)
        self.tasks[task.id] = task
        
        # Clean up completed tasks
        self._cleanup_tasks()
        
        return task.start()
    
    def schedule_task(
        self,
        when: Union[datetime, timedelta, float],
        func: TaskFunc,
        *args: Any,
        **kwargs: Any
    ) -> str:
        """Schedule a task to run at a specific time or after a delay."""
        if isinstance(when, (int, float)):
            when = datetime.utcnow() + timedelta(seconds=when)
        elif isinstance(when, timedelta):
            when = datetime.utcnow() + when
        
        task_id = str(uuid4())
        
        async def scheduled_task():
            try:
                # Calculate the delay until the scheduled time
                now = datetime.utcnow()
                delay = (when - now).total_seconds()
                
                if delay > 0:
                    await asyncio.sleep(delay)
                
                # Run the task
                await self.create_task(func, *args, **kwargs)
            except asyncio.CancelledError:
                pass
            finally:
                self.scheduled_tasks.pop(task_id, None)
        
        # Schedule the task
        self.scheduled_tasks[task_id] = asyncio.create_task(scheduled_task())
        return task_id
    
    def schedule_recurring(
        self,
        interval: Union[float, timedelta],
        func: TaskFunc,
        *args: Any,
        start_immediately: bool = False,
        **kwargs: Any
    ) -> str:
        """Schedule a task to run repeatedly at a fixed interval."""
        if isinstance(interval, timedelta):
            interval = interval.total_seconds()
        
        task_id = str(uuid4())
        
        async def recurring_task():
            try:
                if not start_immediately:
                    await asyncio.sleep(interval)
                
                while True:
                    start_time = asyncio.get_event_loop().time()
                    
                    try:
                        await self.create_task(func, *args, **kwargs)
                    except Exception as e:
                        logger.error(f"Error in recurring task: {e}", exc_info=True)
                    
                    # Calculate the time to sleep to maintain the interval
                    elapsed = asyncio.get_event_loop().time() - start_time
                    sleep_time = max(0, interval - elapsed)
                    
                    if sleep_time > 0:
                        await asyncio.sleep(sleep_time)
            except asyncio.CancelledError:
                pass
            finally:
                self.scheduled_tasks.pop(task_id, None)
        
        # Start the recurring task
        self.scheduled_tasks[task_id] = asyncio.create_task(recurring_task())
        return task_id
    
    def cancel_scheduled_task(self, task_id: str) -> bool:
        """Cancel a scheduled task."""
        task = self.scheduled_tasks.get(task_id)
        if task is not None and not task.done():
            task.cancel()
            return True
        return False
    
    def get_task(self, task_id: str) -> Optional[BackgroundTask]:
        """Get a background task by ID."""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[BackgroundTask]:
        """Get all background tasks."""
        return list(self.tasks.values())
    
    def get_active_tasks(self) -> List[BackgroundTask]:
        """Get all active (not completed) background tasks."""
        return [
            task for task in self.tasks.values()
            if task.task is not None and not task.task.done()
        ]
    
    def _cleanup_tasks(self, max_age: int = 3600) -> None:
        """Clean up old completed tasks."""
        now = datetime.utcnow()
        to_remove = []
        
        for task_id, task in self.tasks.items():
            if task.result.end_time is not None:
                age = (now - task.result.end_time).total_seconds()
                if age > max_age:
                    to_remove.append(task_id)
        
        for task_id in to_remove:
            self.tasks.pop(task_id, None)
    
    async def shutdown(self) -> None:
        """Shut down the scheduler and cancel all tasks."""
        # Cancel all scheduled tasks
        for task in self.scheduled_tasks.values():
            task.cancel()
        
        # Wait for all tasks to complete
        if self.scheduled_tasks:
            await asyncio.wait(
                [t for t in self.scheduled_tasks.values() if not t.done()],
                timeout=5.0,
                return_when=asyncio.ALL_COMPLETED
            )
        
        # Clean up
        self.scheduled_tasks.clear()
        self.tasks.clear()


# Global task scheduler instance
task_scheduler = TaskScheduler()


async def shutdown_background_tasks() -> None:
    """Shut down the global task scheduler."""
    await task_scheduler.shutdown()


def run_in_background(
    func: TaskFunc,
    *args: Any,
    **kwargs: Any
) -> BackgroundTask:
    """Run a function in the background."""
    return task_scheduler.create_task(func, *args, **kwargs)


def schedule_in(
    delay: Union[float, timedelta],
    func: TaskFunc,
    *args: Any,
    **kwargs: Any
) -> str:
    """Schedule a function to run after a delay."""
    return task_scheduler.schedule_task(delay, func, *args, **kwargs)


def schedule_at(
    when: datetime,
    func: TaskFunc,
    *args: Any,
    **kwargs: Any
) -> str:
    """Schedule a function to run at a specific time."""
    return task_scheduler.schedule_task(when, func, *args, **kwargs)


def schedule_recurring(
    interval: Union[float, timedelta],
    func: TaskFunc,
    *args: Any,
    start_immediately: bool = False,
    **kwargs: Any
) -> str:
    """Schedule a function to run repeatedly at a fixed interval."""
    return task_scheduler.schedule_recurring(
        interval,
        func,
        *args,
        start_immediately=start_immediately,
        **kwargs
    )
