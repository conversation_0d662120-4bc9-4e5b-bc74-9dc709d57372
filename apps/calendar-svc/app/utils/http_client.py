import json
import logging
import time
from functools import wraps
from typing import Any, Dict, List, Optional, Type, TypeVar, Union, Callable, Awaitable, cast
from urllib.parse import urljoin, urlencode, parse_qs, urlparse, urlunparse

import aiohttp
import httpx
from pydantic import BaseModel, ValidationError
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
    before_sleep_log,
    RetryCallState,
)

from ..core.config import settings
from .exceptions import (
    HTTPException,
    BadRequestError,
    UnauthorizedError,
    ForbiddenError,
    NotFoundError,
    ConflictError,
    RateLimitError,
    ServerError,
    ServiceUnavailableError,
    GatewayTimeoutError,
)

# Type variables
T = TypeVar('T', bound=BaseModel)
AsyncFunc = Callable[..., Awaitable[Any]]

# Configure logger
logger = logging.getLogger(__name__)

# Default timeout for HTTP requests (in seconds)
DEFAULT_TIMEOUT = 30.0
# Default maximum number of retries
DEFAULT_MAX_RETRIES = 3
# Default backoff factor for retries (in seconds)
DEFAULT_BACKOFF_FACTOR = 0.5
# Status codes that should be retried
RETRY_STATUS_CODES = {408, 429, 500, 502, 503, 504}
# Status codes that should raise specific exceptions
STATUS_CODE_EXCEPTIONS = {
    400: BadRequestError,
    401: UnauthorizedError,
    403: ForbiddenError,
    404: NotFoundError,
    409: ConflictError,
    422: BadRequestError,  # Unprocessable Entity (used for validation errors)
    429: RateLimitError,
    500: ServerError,
    502: BadGatewayError,
    503: ServiceUnavailableError,
    504: GatewayTimeoutError,
}

class HTTPClient:
    """
    A reusable HTTP client for making requests to external APIs.
    
    This client provides methods for making HTTP requests with built-in retry logic,
    request/response validation, and error handling.
    """
    
    def __init__(
        self,
        base_url: Optional[str] = None,
        api_key: Optional[str] = None,
        token: Optional[str] = None,
        timeout: float = DEFAULT_TIMEOUT,
        max_retries: int = DEFAULT_MAX_RETRIES,
        backoff_factor: float = DEFAULT_BACKOFF_FACTOR,
        default_headers: Optional[Dict[str, str]] = None,
        raise_for_status: bool = True,
        verify_ssl: bool = True,
        **kwargs
    ):
        """
        Initialize the HTTP client.
        
        Args:
            base_url: Base URL for all requests (e.g., 'https://api.example.com')
            api_key: API key for authentication
            token: Bearer token for authentication
            timeout: Default request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            backoff_factor: Backoff factor for retries
            default_headers: Default headers to include in all requests
            raise_for_status: Whether to raise exceptions for non-2xx status codes
            verify_ssl: Whether to verify SSL certificates
            **kwargs: Additional arguments to pass to the underlying HTTP client
        """
        self.base_url = base_url.rstrip('/') if base_url else None
        self.timeout = timeout
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.raise_for_status = raise_for_status
        self.verify_ssl = verify_ssl
        self.kwargs = kwargs
        
        # Set up default headers
        self.default_headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            **(default_headers or {})
        }
        
        # Set up authentication
        if api_key:
            self.default_headers['X-API-Key'] = api_key
        if token:
            self.default_headers['Authorization'] = f'Bearer {token}'
        
        # Create a session for connection pooling
        self._session: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def start(self) -> None:
        """Start the HTTP client session."""
        if self._session is None:
            self._session = httpx.AsyncClient(
                base_url=self.base_url or '',
                timeout=self.timeout,
                verify=self.verify_ssl,
                **self.kwargs
            )
    
    async def close(self) -> None:
        """Close the HTTP client session."""
        if self._session is not None:
            await self._session.aclose()
            self._session = None
    
    def get_session(self) -> httpx.AsyncClient:
        """Get the underlying HTTP session."""
        if self._session is None:
            raise RuntimeError("HTTP client is not started. Use 'async with' or call start() first.")
        return self._session
    
    def get_full_url(self, url: str) -> str:
        """Get the full URL by joining with the base URL if relative."""
        if not url.startswith(('http://', 'https://')) and self.base_url:
            return urljoin(f"{self.base_url}/", url.lstrip('/'))
        return url
    
    async def request(
        self,
        method: str,
        url: str,
        *,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Union[Dict[str, Any], bytes, str]] = None,
        json_data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None,
        max_retries: Optional[int] = None,
        backoff_factor: Optional[float] = None,
        raise_for_status: Optional[bool] = None,
        **kwargs
    ) -> httpx.Response:
        """
        Make an HTTP request with retry logic.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            url: URL to request (can be relative if base_url is set)
            params: Query parameters
            data: Request body (for form data or raw bytes/string)
            json_data: JSON-serializable data to send in the request body
            headers: Request headers
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            backoff_factor: Backoff factor for retries
            raise_for_status: Whether to raise exceptions for non-2xx status codes
            **kwargs: Additional arguments to pass to the underlying HTTP client
            
        Returns:
            The HTTP response
            
        Raises:
            HTTPException: If the request fails and raise_for_status is True
        """
        session = self.get_session()
        full_url = self.get_full_url(url)
        headers = {**self.default_headers, **(headers or {})}
        timeout = timeout or self.timeout
        max_retries = max_retries if max_retries is not None else self.max_retries
        backoff_factor = backoff_factor if backoff_factor is not None else self.backoff_factor
        raise_for_status = raise_for_status if raise_for_status is not None else self.raise_for_status
        
        # Prepare request data
        request_kwargs: Dict[str, Any] = {
            'params': params,
            'headers': headers,
            'timeout': timeout,
            **kwargs
        }
        
        if json_data is not None:
            request_kwargs['json'] = json_data
        elif data is not None:
            request_kwargs['data'] = data
        
        # Define the retry decorator
        retry_decorator = retry(
            retry=retry_if_exception_type((
                httpx.RequestError,
                httpx.HTTPStatusError,
            )),
            stop=stop_after_attempt(max_retries + 1),
            wait=wait_exponential(multiplier=backoff_factor),
            before_sleep=before_sleep_log(logger, logging.WARNING),
            reraise=True,
        )
        
        # Define the request function with retry
        @retry_decorator
        async def _make_request() -> httpx.Response:
            try:
                response = await session.request(method, full_url, **request_kwargs)
                
                # Log the request and response
                logger.debug(
                    "HTTP %s %s - Status: %d, Size: %d bytes",
                    method,
                    full_url,
                    response.status_code,
                    len(response.content or b''),
                )
                
                # Raise an exception for non-2xx status codes if requested
                if raise_for_status and response.status_code >= 400:
                    await self._handle_error_response(response)
                
                return response
            except httpx.HTTPStatusError as e:
                if e.response.status_code in RETRY_STATUS_CODES:
                    logger.warning(
                        "Retrying %s %s - Status: %d, Error: %s",
                        method,
                        full_url,
                        e.response.status_code,
                        str(e),
                    )
                raise
            except httpx.RequestError as e:
                logger.warning(
                    "Request failed: %s %s - %s",
                    method,
                    full_url,
                    str(e),
                    exc_info=True,
                )
                raise
        
        # Make the request
        try:
            return await _make_request()
        except Exception as e:
            if raise_for_status:
                if isinstance(e, HTTPException):
                    raise
                raise HTTPException(
                    status_code=getattr(e, 'status_code', 500),
                    detail=str(e) or "An unexpected error occurred",
                ) from e
            raise
    
    async def _handle_error_response(self, response: httpx.Response) -> None:
        """Handle error responses by raising appropriate exceptions."""
        status_code = response.status_code
        content_type = response.headers.get('content-type', '')
        
        # Try to parse error details from the response
        detail = None
        error_data = None
        
        if 'application/json' in content_type:
            try:
                error_data = response.json()
                if isinstance(error_data, dict):
                    detail = error_data.get('detail') or error_data.get('message') or error_data.get('error')
                elif isinstance(error_data, str):
                    detail = error_data
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass
        
        if detail is None:
            try:
                detail = response.text[:500]  # Limit detail length
            except Exception:
                detail = f"HTTP {status_code} {response.reason_phrase or 'Unknown Error'}"
        
        # Raise the appropriate exception
        exception_class = STATUS_CODE_EXCEPTIONS.get(status_code, HTTPException)
        raise exception_class(
            status_code=status_code,
            detail=detail,
            error_data=error_data,
            headers=dict(response.headers),
        )
    
    # Convenience methods for common HTTP methods
    
    async def get(
        self,
        url: str,
        *,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> httpx.Response:
        """Send a GET request."""
        return await self.request('GET', url, params=params, headers=headers, **kwargs)
    
    async def post(
        self,
        url: str,
        *,
        data: Optional[Union[Dict[str, Any], bytes, str]] = None,
        json_data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> httpx.Response:
        """Send a POST request."""
        return await self.request(
            'POST',
            url,
            data=data,
            json_data=json_data,
            headers=headers,
            **kwargs
        )
    
    async def put(
        self,
        url: str,
        *,
        data: Optional[Union[Dict[str, Any], bytes, str]] = None,
        json_data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> httpx.Response:
        """Send a PUT request."""
        return await self.request(
            'PUT',
            url,
            data=data,
            json_data=json_data,
            headers=headers,
            **kwargs
        )
    
    async def patch(
        self,
        url: str,
        *,
        data: Optional[Union[Dict[str, Any], bytes, str]] = None,
        json_data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> httpx.Response:
        """Send a PATCH request."""
        return await self.request(
            'PATCH',
            url,
            data=data,
            json_data=json_data,
            headers=headers,
            **kwargs
        )
    
    async def delete(
        self,
        url: str,
        *,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> httpx.Response:
        """Send a DELETE request."""
        return await self.request(
            'DELETE',
            url,
            params=params,
            headers=headers,
            **kwargs
        )
    
    # Response handling utilities
    
    async def json(
        self,
        response: httpx.Response,
        model: Optional[Type[T]] = None,
        validate: bool = True
    ) -> Union[Dict[str, Any], List[Any], T]:
        """
        Parse the response body as JSON.
        
        Args:
            response: The HTTP response
            model: Optional Pydantic model to validate and parse the response
            validate: Whether to validate the response against the model
            
        Returns:
            The parsed JSON data, optionally validated against the model
        """
        try:
            data = response.json()
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            raise BadRequestError("Invalid JSON response") from e
        
        if model is not None and validate:
            try:
                if isinstance(data, list):
                    return [model.parse_obj(item) for item in data]
                return model.parse_obj(data)
            except ValidationError as e:
                raise BadRequestError("Response validation failed") from e
        
        return data
    
    async def text(self, response: httpx.Response) -> str:
        """Get the response body as text."""
        return response.text
    
    async def content(self, response: httpx.Response) -> bytes:
        """Get the response body as bytes."""
        return response.content
    
    async def stream(self, response: httpx.Response) -> bytes:
        """Stream the response content."""
        return response.aiter_bytes()
    
    # Utility methods for common patterns
    
    async def get_json(
        self,
        url: str,
        *,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        model: Optional[Type[T]] = None,
        **kwargs
    ) -> Union[Dict[str, Any], List[Any], T]:
        """Send a GET request and return the JSON response."""
        response = await self.get(url, params=params, headers=headers, **kwargs)
        return await self.json(response, model=model)
    
    async def post_json(
        self,
        url: str,
        *,
        json_data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        model: Optional[Type[T]] = None,
        **kwargs
    ) -> Union[Dict[str, Any], List[Any], T]:
        """Send a POST request and return the JSON response."""
        response = await self.post(
            url,
            json_data=json_data,
            headers=headers,
            **kwargs
        )
        return await self.json(response, model=model)
    
    async def put_json(
        self,
        url: str,
        *,
        json_data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        model: Optional[Type[T]] = None,
        **kwargs
    ) -> Union[Dict[str, Any], List[Any], T]:
        """Send a PUT request and return the JSON response."""
        response = await self.put(
            url,
            json_data=json_data,
            headers=headers,
            **kwargs
        )
        return await self.json(response, model=model)
    
    async def patch_json(
        self,
        url: str,
        *,
        json_data: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        model: Optional[Type[T]] = None,
        **kwargs
    ) -> Union[Dict[str, Any], List[Any], T]:
        """Send a PATCH request and return the JSON response."""
        response = await self.patch(
            url,
            json_data=json_data,
            headers=headers,
            **kwargs
        )
        return await self.json(response, model=model)
    
    async def delete_json(
        self,
        url: str,
        *,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        model: Optional[Type[T]] = None,
        **kwargs
    ) -> Union[Dict[str, Any], List[Any], T]:
        """Send a DELETE request and return the JSON response."""
        response = await self.delete(
            url,
            params=params,
            headers=headers,
            **kwargs
        )
        return await self.json(response, model=model)


# Default HTTP client instance
_http_client: Optional[HTTPClient] = None


def get_http_client() -> HTTPClient:
    """
    Get the default HTTP client instance.
    
    Returns:
        HTTPClient: The default HTTP client
    """
    global _http_client
    if _http_client is None:
        _http_client = HTTPClient()
    return _http_client


def set_http_client(client: HTTPClient) -> None:
    """
    Set the default HTTP client instance.
    
    Args:
        client: The HTTP client to set as default
    """
    global _http_client
    _http_client = client


async def close_http_client() -> None:
    """Close the default HTTP client session."""
    global _http_client
    if _http_client is not None:
        await _http_client.close()
        _http_client = None


# Context manager for async HTTP client
class AsyncHTTPClient:
    """Context manager for async HTTP client."""
    
    def __init__(self, **kwargs):
        self.client = HTTPClient(**kwargs)
    
    async def __aenter__(self) -> HTTPClient:
        await self.client.start()
        return self.client
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.close()


# Decorator for retrying HTTP requests
def retry_on_failure(
    max_retries: int = 3,
    backoff_factor: float = 0.5,
    retry_on: Optional[List[int]] = None,
):
    """
    Decorator for retrying HTTP requests on failure.
    
    Args:
        max_retries: Maximum number of retries
        backoff_factor: Backoff factor for exponential delay
        retry_on: List of status codes to retry on (default: [408, 429, 500, 502, 503, 504])
    """
    if retry_on is None:
        retry_on = [408, 429, 500, 502, 503, 504]
    
    def decorator(func: AsyncFunc) -> AsyncFunc:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except httpx.HTTPStatusError as e:
                    if e.response.status_code in retry_on and attempt < max_retries:
                        wait_time = backoff_factor * (2 ** attempt)
                        logger.warning(
                            "Retrying %s (attempt %d/%d) after %.2fs: %s",
                            func.__name__,
                            attempt + 1,
                            max_retries,
                            wait_time,
                            str(e),
                        )
                        await asyncio.sleep(wait_time)
                        last_exception = e
                        continue
                    raise
                except httpx.RequestError as e:
                    if attempt < max_retries:
                        wait_time = backoff_factor * (2 ** attempt)
                        logger.warning(
                            "Retrying %s (attempt %d/%d) after %.2fs: %s",
                            func.__name__,
                            attempt + 1,
                            max_retries,
                            wait_time,
                            str(e),
                        )
                        await asyncio.sleep(wait_time)
                        last_exception = e
                        continue
                    raise
            
            if last_exception is not None:
                raise last_exception
            
            raise RuntimeError("Max retries exceeded")
        
        return wrapper
    
    return decorator


# Helper functions for common HTTP operations

async def fetch_json(
    url: str,
    method: str = 'GET',
    params: Optional[Dict[str, Any]] = None,
    json_data: Optional[Any] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: float = DEFAULT_TIMEOUT,
    max_retries: int = DEFAULT_MAX_RETRIES,
    raise_for_status: bool = True,
) -> Any:
    """
    Fetch JSON data from a URL.
    
    Args:
        url: The URL to fetch
        method: HTTP method (GET, POST, etc.)
        params: Query parameters
        json_data: JSON data to send in the request body
        headers: Request headers
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries
        raise_for_status: Whether to raise an exception for non-2xx status codes
        
    Returns:
        The parsed JSON response
    """
    async with AsyncHTTPClient() as client:
        response = await client.request(
            method,
            url,
            params=params,
            json_data=json_data,
            headers=headers,
            timeout=timeout,
            max_retries=max_retries,
            raise_for_status=raise_for_status,
        )
        return await response.json()


async def download_file(
    url: str,
    dest_path: str,
    chunk_size: int = 8192,
    timeout: float = 300.0,
    max_retries: int = 3,
) -> str:
    """
    Download a file from a URL to a local path.
    
    Args:
        url: The URL to download from
        dest_path: The local path to save the file to
        chunk_size: Size of chunks to download at a time
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries
        
    Returns:
        The path to the downloaded file
    """
    async with AsyncHTTPClient() as client:
        async with client.stream('GET', url, timeout=timeout) as response:
            response.raise_for_status()
            
            # Ensure the destination directory exists
            dest_dir = os.path.dirname(dest_path)
            if dest_dir:
                os.makedirs(dest_dir, exist_ok=True)
            
            # Download the file in chunks
            with open(dest_path, 'wb') as f:
                async for chunk in response.aiter_bytes(chunk_size=chunk_size):
                    f.write(chunk)
    
    return dest_path


async def upload_file(
    url: str,
    file_path: str,
    field_name: str = 'file',
    extra_data: Optional[Dict[str, Any]] = None,
    timeout: float = 300.0,
    max_retries: int = 3,
) -> Any:
    """
    Upload a file to a URL using multipart/form-data.
    
    Args:
        url: The URL to upload to
        file_path: The local path to the file to upload
        field_name: The form field name for the file
        extra_data: Additional form data to include
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries
        
    Returns:
        The parsed JSON response
    """
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    
    files = {
        field_name: (os.path.basename(file_path), open(file_path, 'rb')),
    }
    
    data = extra_data or {}
    
    async with AsyncHTTPClient() as client:
        response = await client.post(
            url,
            data=data,
            files=files,
            timeout=timeout,
            max_retries=max_retries,
        )
        return await response.json()
