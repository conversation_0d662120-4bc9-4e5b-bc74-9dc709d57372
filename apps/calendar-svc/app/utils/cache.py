"""
Caching utilities for the application.

This module provides a simple caching interface that can be used to cache
function results, database queries, API responses, and other expensive operations.
"""
import asyncio
import functools
import hashlib
import inspect
import json
import logging
import pickle
import time
from datetime import datetime, timedelta
from typing import Any, Awaitable, Callable, Dict, List, Optional, Tuple, TypeVar, Union, cast

import aioredis
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel

from .config import settings
from .exceptions import ConfigurationError

# Type variables
T = TypeVar('T')
AsyncFunc = Callable[..., Awaitable[T]]
SyncFunc = Callable[..., T]

# Configure logger
logger = logging.getLogger(__name__)

# Default cache settings
DEFAULT_TTL = 300  # 5 minutes
MAX_CACHE_SIZE = 1000
CLEANUP_INTERVAL = 60  # seconds
REDIS_DISCONNECTED = object()  # Sentinel value for disconnected Redis

# Cache backends
class CacheBackend:
    """Base class for cache backends."""
    
    async def get(self, key: str) -> Any:
        """Get a value from the cache."""
        raise NotImplementedError
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set a value in the cache."""
        raise NotImplementedError
    
    async def delete(self, key: str) -> None:
        """Delete a value from the cache."""
        raise NotImplementedError
    
    async def exists(self, key: str) -> bool:
        """Check if a key exists in the cache."""
        raise NotImplementedError
    
    async def clear(self) -> None:
        """Clear all values from the cache."""
        raise NotImplementedError
    
    async def close(self) -> None:
        """Close the cache connection."""
        pass


class MemoryCacheBackend(CacheBackend):
    """In-memory cache backend."""
    
    def __init__(self, max_size: int = MAX_CACHE_SIZE, default_ttl: int = DEFAULT_TTL):
        """
        Initialize the in-memory cache.
        
        Args:
            max_size: Maximum number of items to store in the cache
            default_ttl: Default time-to-live in seconds for cache items
        """
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._lock = asyncio.Lock()
        self._last_cleanup = time.monotonic()
    
    async def get(self, key: str) -> Any:
        """Get a value from the cache."""
        async with self._lock:
            self._cleanup()
            if key in self._cache:
                value, expiry = self._cache[key]
                if expiry > time.monotonic():
                    return value
                del self._cache[key]
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set a value in the cache."""
        async with self._lock:
            self._cleanup()
            if len(self._cache) >= self._max_size:
                # Remove the first item if the cache is full
                first_key = next(iter(self._cache), None)
                if first_key is not None:
                    del self._cache[first_key]
            
            expiry = time.monotonic() + (ttl if ttl is not None else self._default_ttl)
            self._cache[key] = (value, expiry)
    
    async def delete(self, key: str) -> None:
        """Delete a value from the cache."""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
    
    async def exists(self, key: str) -> bool:
        """Check if a key exists in the cache."""
        async with self._lock:
            self._cleanup()
            if key in self._cache:
                _, expiry = self._cache[key]
                if expiry > time.monotonic():
                    return True
                del self._cache[key]
            return False
    
    async def clear(self) -> None:
        """Clear all values from the cache."""
        async with self._lock:
            self._cache.clear()
    
    def _cleanup(self) -> None:
        """Remove expired items from the cache."""
        now = time.monotonic()
        
        # Only run cleanup every CLEANUP_INTERVAL seconds
        if now - self._last_cleanup < CLEANUP_INTERVAL:
            return
        
        expired_keys = [
            key for key, (_, expiry) in self._cache.items()
            if expiry <= now
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        self._last_cleanup = now


class RedisCacheBackend(CacheBackend):
    """Redis cache backend."""
    
    def __init__(
        self,
        url: str,
        prefix: str = "cache:",
        default_ttl: int = DEFAULT_TTL,
        **kwargs
    ):
        """
        Initialize the Redis cache.
        
        Args:
            url: Redis connection URL
            prefix: Prefix for all cache keys
            default_ttl: Default time-to-live in seconds for cache items
            **kwargs: Additional arguments to pass to aioredis.create_redis_pool
        """
        self._url = url
        self._prefix = prefix
        self._default_ttl = default_ttl
        self._redis: Optional[aioredis.Redis] = None
        self._redis_kwargs = kwargs
        self._lock = asyncio.Lock()
    
    async def _get_redis(self) -> aioredis.Redis:
        """Get a Redis connection, creating it if necessary."""
        if self._redis is None:
            try:
                self._redis = await aioredis.create_redis_pool(
                    self._url,
                    **self._redis_kwargs
                )
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                raise ConfigurationError("Failed to connect to Redis") from e
        return self._redis
    
    def _get_key(self, key: str) -> str:
        """Get the full cache key with prefix."""
        return f"{self._prefix}{key}"
    
    async def get(self, key: str) -> Any:
        """Get a value from the cache."""
        try:
            redis = await self._get_redis()
            data = await redis.get(self._get_key(key))
            if data is not None:
                try:
                    return pickle.loads(data)
                except (pickle.PickleError, TypeError) as e:
                    logger.warning(f"Failed to unpickle cached value: {e}")
                    await self.delete(key)
            return None
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set a value in the cache."""
        try:
            redis = await self._get_redis()
            data = pickle.dumps(value)
            ttl = ttl if ttl is not None else self._default_ttl
            await redis.set(
                self._get_key(key),
                data,
                expire=ttl
            )
        except Exception as e:
            logger.error(f"Redis set error: {e}")
    
    async def delete(self, key: str) -> None:
        """Delete a value from the cache."""
        try:
            redis = await self._get_redis()
            await redis.delete(self._get_key(key))
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
    
    async def exists(self, key: str) -> bool:
        """Check if a key exists in the cache."""
        try:
            redis = await self._get_redis()
            return await redis.exists(self._get_key(key)) > 0
        except Exception as e:
            logger.error(f"Redis exists error: {e}")
            return False
    
    async def clear(self) -> None:
        """Clear all values from the cache."""
        try:
            redis = await self._get_redis()
            keys = await redis.keys(f"{self._prefix}*")
            if keys:
                await redis.delete(*keys)
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
    
    async def close(self) -> None:
        """Close the Redis connection."""
        if self._redis is not None:
            self._redis.close()
            await self._redis.wait_closed()
            self._redis = None


class Cache:
    """
    A simple caching interface that can use different backends.
    
    This class provides a simple interface for caching function results,
    database queries, API responses, and other expensive operations.
    """
    
    def __init__(self, backend: CacheBackend):
        """
        Initialize the cache with the specified backend.
        
        Args:
            backend: The cache backend to use
        """
        self._backend = backend
    
    @classmethod
    def create_memory_cache(cls, max_size: int = MAX_CACHE_SIZE, default_ttl: int = DEFAULT_TTL) -> 'Cache':
        """
        Create a new in-memory cache.
        
        Args:
            max_size: Maximum number of items to store in the cache
            default_ttl: Default time-to-live in seconds for cache items
            
        Returns:
            A new Cache instance with an in-memory backend
        """
        return cls(MemoryCacheBackend(max_size=max_size, default_ttl=default_ttl))
    
    @classmethod
    def create_redis_cache(
        cls,
        url: str = "redis://localhost:6379/0",
        prefix: str = "cache:",
        default_ttl: int = DEFAULT_TTL,
        **kwargs
    ) -> 'Cache':
        """
        Create a new Redis cache.
        
        Args:
            url: Redis connection URL
            prefix: Prefix for all cache keys
            default_ttl: Default time-to-live in seconds for cache items
            **kwargs: Additional arguments to pass to aioredis.create_redis_pool
            
        Returns:
            A new Cache instance with a Redis backend
        """
        return cls(
            RedisCacheBackend(
                url=url,
                prefix=prefix,
                default_ttl=default_ttl,
                **kwargs
            )
        )
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the cache.
        
        Args:
            key: The cache key
            default: The default value to return if the key is not found
            
        Returns:
            The cached value, or the default value if the key is not found
        """
        value = await self._backend.get(key)
        return default if value is None else value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: The cache key
            value: The value to cache
            ttl: Time-to-live in seconds (default: None, use default TTL)
        """
        await self._backend.set(key, value, ttl=ttl)
    
    async def delete(self, key: str) -> None:
        """
        Delete a value from the cache.
        
        Args:
            key: The cache key to delete
        """
        await self._backend.delete(key)
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in the cache.
        
        Args:
            key: The cache key to check
            
        Returns:
            True if the key exists, False otherwise
        """
        return await self._backend.exists(key)
    
    async def clear(self) -> None:
        """Clear all values from the cache."""
        await self._backend.clear()
    
    async def close(self) -> None:
        """Close the cache connection."""
        await self._backend.close()
    
    def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    # Convenience methods for common patterns
    
    async def get_or_set(
        self,
        key: str,
        default: Union[Any, Callable[[], Awaitable[Any]]],
        ttl: Optional[int] = None,
    ) -> Any:
        """
        Get a value from the cache, or set it if it doesn't exist.
        
        Args:
            key: The cache key
            default: The default value or a callable that returns the default value
            ttl: Time-to-live in seconds (default: None, use default TTL)
            
        Returns:
            The cached value, or the default value if the key was not found
        """
        value = await self.get(key)
        if value is not None:
            return value
        
        if callable(default):
            if inspect.iscoroutinefunction(default):
                value = await default()
            else:
                value = default()
        else:
            value = default
        
        if value is not None:
            await self.set(key, value, ttl=ttl)
        
        return value
    
    async def memoize(
        self,
        key: Optional[str] = None,
        ttl: Optional[int] = None,
        key_fn: Optional[Callable[..., str]] = None,
        exclude_args: bool = False,
        exclude_kwargs: bool = False,
        exclude_self: bool = True,
    ) -> Callable[[AsyncFunc], AsyncFunc]:
        """
        Decorator to cache the result of an async function.
        
        Args:
            key: The cache key (default: function name)
            ttl: Time-to-live in seconds (default: None, use default TTL)
            key_fn: Function to generate the cache key from function arguments
            exclude_args: Whether to exclude positional arguments from the cache key
            exclude_kwargs: Whether to exclude keyword arguments from the cache key
            exclude_self: Whether to exclude 'self' from the cache key for methods
            
        Returns:
            A decorator that caches the function result
        """
        def decorator(func: AsyncFunc) -> AsyncFunc:
            cache_key_prefix = key or f"{func.__module__}:{func.__name__}"
            
            @functools.wraps(func)
            async def wrapper(*args, **kwargs) -> Any:
                # Generate the cache key
                if key_fn is not None:
                    cache_key = key_fn(*args, **kwargs)
                else:
                    # Default key generation
                    key_parts = [cache_key_prefix]
                    
                    if not exclude_args:
                        # Handle 'self' for methods
                        if exclude_self and args and hasattr(args[0], func.__name__):
                            args = args[1:]
                        
                        if args:
                            key_parts.append(_make_key(args, []))
                    
                    if not exclude_kwargs and kwargs:
                        key_parts.append(_make_key([], kwargs))
                    
                    cache_key = ":".join(str(part) for part in key_parts if part)
                
                # Try to get the cached value
                cached_value = await self.get(cache_key)
                if cached_value is not None:
                    return cached_value
                
                # Call the function and cache the result
                result = await func(*args, **kwargs)
                if result is not None:
                    await self.set(cache_key, result, ttl=ttl)
                
                return result
            
            return wrapper
        
        return decorator


def _make_key(args: list, kwargs: dict) -> str:
    """
    Create a string key from function arguments.
    
    Args:
        args: Positional arguments
        kwargs: Keyword arguments
        
    Returns:
        A string key
    """
    # Convert args and kwargs to a stable string representation
    def _convert(value: Any) -> str:
        if isinstance(value, (str, int, float, bool)) or value is None:
            return str(value)
        elif isinstance(value, (list, tuple, set)):
            return f"[{','.join(sorted(_convert(v) for v in value))}]"
        elif isinstance(value, dict):
            return f"{{{','.join(f'{k}:{_convert(v)}' for k, v in sorted(value.items()))}}}"
        elif hasattr(value, '__dict__'):
            return _convert(vars(value))
        else:
            return str(hash(str(value)))
    
    parts = []
    if args:
        parts.append(",".join(_convert(arg) for arg in args))
    if kwargs:
        parts.append(",".join(f"{k}={_convert(v)}" for k, v in sorted(kwargs.items())))
    
    return ":".join(parts)


# Default cache instance
_cache: Optional[Cache] = None


def get_cache() -> Cache:
    """
    Get the default cache instance.
    
    Returns:
        The default Cache instance
    """
    global _cache
    if _cache is None:
        # Try to get Redis URI from settings, defaulting to None if not found
        redis_uri = getattr(settings, 'REDIS_URI', None)
        
        if redis_uri:
            # Get app name or default to a generic name
            app_name = getattr(settings, 'APP_NAME', 'app')
            # Default cache TTL to 5 minutes if not specified
            cache_ttl = getattr(settings, 'CACHE_TTL', 300)
            
            _cache = Cache.create_redis_cache(
                url=str(redis_uri),  # Convert to string in case it's a DSN object
                prefix=f"{app_name}:cache:",
                default_ttl=cache_ttl,
                minsize=5,
                maxsize=20,
                encoding="utf-8",
                decode_responses=False,
            )
        else:
            _cache = Cache.create_memory_cache(
                max_size=getattr(settings, 'CACHE_MAX_SIZE', 1000),  # Default 1000 items
                default_ttl=getattr(settings, 'CACHE_TTL', 300),  # 5 minutes default
            )
    return _cache


def set_cache(cache: Cache) -> None:
    """
    Set the default cache instance.
    
    Args:
        cache: The Cache instance to set as default
    """
    global _cache
    _cache = cache


async def close_cache() -> None:
    """Close the default cache connection."""
    global _cache
    if _cache is not None:
        await _cache.close()
        _cache = None


# Shortcut functions for the default cache

async def cache_get(key: str, default: Any = None) -> Any:
    """Get a value from the default cache."""
    return await get_cache().get(key, default=default)


async def cache_set(key: str, value: Any, ttl: Optional[int] = None) -> None:
    """Set a value in the default cache."""
    await get_cache().set(key, value, ttl=ttl)


async def cache_delete(key: str) -> None:
    """Delete a value from the default cache."""
    await get_cache().delete(key)


async def cache_exists(key: str) -> bool:
    """Check if a key exists in the default cache."""
    return await get_cache().exists(key)


async def cache_clear() -> None:
    """Clear the default cache."""
    await get_cache().clear()


async def cache_get_or_set(
    key: str,
    default: Union[Any, Callable[[], Awaitable[Any]]],
    ttl: Optional[int] = None,
) -> Any:
    """
    Get a value from the default cache, or set it if it doesn't exist.
    
    Args:
        key: The cache key
        default: The default value or a callable that returns the default value
        ttl: Time-to-live in seconds (default: None, use default TTL)
        
    Returns:
        The cached value, or the default value if the key was not found
    """
    return await get_cache().get_or_set(key, default, ttl=ttl)


def cache_memoize(
    key: Optional[str] = None,
    ttl: Optional[int] = None,
    key_fn: Optional[Callable[..., str]] = None,
    exclude_args: bool = False,
    exclude_kwargs: bool = False,
    exclude_self: bool = True,
) -> Callable[[AsyncFunc], AsyncFunc]:
    """
    Decorator to cache the result of an async function using the default cache.
    
    Args:
        key: The cache key (default: function name)
        ttl: Time-to-live in seconds (default: None, use default TTL)
        key_fn: Function to generate the cache key from function arguments
        exclude_args: Whether to exclude positional arguments from the cache key
        exclude_kwargs: Whether to exclude keyword arguments from the cache key
        exclude_self: Whether to exclude 'self' from the cache key for methods
        
    Returns:
        A decorator that caches the function result
    """
    return get_cache().memoize(
        key=key,
        ttl=ttl,
        key_fn=key_fn,
        exclude_args=exclude_args,
        exclude_kwargs=exclude_kwargs,
        exclude_self=exclude_self,
    )
