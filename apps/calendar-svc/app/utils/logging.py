import logging
import logging.config
import logging.handlers
import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union, List, Tuple

from pythonjsonlogger import jsonlogger

from ...config import settings


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with additional fields and formatting."""
    
    def add_fields(
        self,
        log_record: Dict[str, Any],
        record: logging.LogRecord,
        message_dict: Dict[str, Any]
    ) -> None:
        """Add custom fields to the log record."""
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp in ISO 8601 format
        log_record["timestamp"] = datetime.utcnow().isoformat() + "Z"
        
        # Add log level
        log_record["level"] = record.levelname.lower()
        
        # Add process and thread information
        log_record["process"] = record.process
        log_record["processName"] = record.processName
        log_record["threadName"] = record.threadName
        
        # Add source file and line number
        log_record["file"] = f"{record.filename}:{record.lineno}"
        log_record["module"] = record.module
        log_record["funcName"] = record.funcName
        
        # Add exception information if present
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)
        
        # Add stack trace if available
        if record.stack_info:
            log_record["stack_trace"] = self.formatStack(record.stack_info)


class RequestIdFilter(logging.Filter):
    """Add request ID to log records."""
    
    def __init__(self, name: str = "", default_request_id: str = ""):
        super().__init__(name)
        self.default_request_id = default_request_id
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add request_id to the log record if not already present."""
        if not hasattr(record, 'request_id'):
            record.request_id = self.default_request_id
        return True


def configure_logging(
    log_level: Optional[Union[str, int]] = None,
    log_format: Optional[str] = None,
    log_file: Optional[Union[str, Path]] = None,
    json_format: bool = False,
    enable_console: bool = True,
    enable_file: bool = True,
    log_rotation: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Configure logging for the application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Log message format
        log_file: Path to the log file
        json_format: Whether to use JSON format for logs
        enable_console: Whether to enable console logging
        enable_file: Whether to enable file logging
        log_rotation: Log rotation configuration
    """
    if log_level is None:
        log_level = settings.LOG_LEVEL
    
    if log_format is None:
        log_format = settings.LOG_FORMAT
    
    if log_file is None:
        log_file = settings.LOG_FILE
    
    if log_rotation is None:
        log_rotation = {
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "encoding": "utf8",
        }
    
    # Convert log level to int if it's a string
    if isinstance(log_level, str):
        log_level = log_level.upper()
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Configure console handler
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        
        if json_format:
            formatter = CustomJsonFormatter(
                "%(timestamp)s %(level)s %(name)s %(message)s",
                timestamp=True
            )
        else:
            formatter = logging.Formatter(log_format)
        
        console_handler.setFormatter(formatter)
        console_handler.addFilter(RequestIdFilter())
        root_logger.addHandler(console_handler)
    
    # Configure file handler
    if enable_file and log_file:
        log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        if json_format:
            file_handler = logging.handlers.TimedRotatingFileHandler(
                filename=log_file,
                **log_rotation
            )
            formatter = CustomJsonFormatter(
                "%(timestamp)s %(level)s %(name)s %(message)s",
                timestamp=True
            )
        else:
            file_handler = logging.handlers.TimedRotatingFileHandler(
                filename=log_file,
                **log_rotation
            )
            formatter = logging.Formatter(log_format)
        
        file_handler.setFormatter(formatter)
        file_handler.addFilter(RequestIdFilter())
        root_logger.addHandler(file_handler)
    
    # Configure third-party loggers
    logging.getLogger("uvicorn").handlers = []
    logging.getLogger("uvicorn").propagate = True
    logging.getLogger("uvicorn.access").handlers = []
    logging.getLogger("uvicorn.access").propagate = True
    logging.getLogger("uvicorn.error").propagate = True
    
    # Configure SQLAlchemy logging
    if settings.SQL_ECHO:
        logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)
    else:
        logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    # Configure aiohttp logging
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("aiohttp.client").setLevel(logging.WARNING)
    
    # Configure requests logging
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """Get a logger with the given name."""
    logger = logging.getLogger(name or __name__)
    return logger


def log_request(
    logger: logging.Logger,
    request_id: str,
    method: str,
    url: str,
    status_code: int,
    duration: float,
    request_headers: Optional[Dict[str, str]] = None,
    response_headers: Optional[Dict[str, str]] = None,
    request_body: Optional[Union[str, bytes, Dict[str, Any]]] = None,
    response_body: Optional[Union[str, bytes, Dict[str, Any]]] = None,
    user_agent: Optional[str] = None,
    client_ip: Optional[str] = None,
    extra: Optional[Dict[str, Any]] = None,
    level: int = logging.INFO,
) -> None:
    """
    Log an HTTP request/response pair.
    
    Args:
        logger: Logger instance
        request_id: Unique request ID
        method: HTTP method
        url: Request URL
        status_code: HTTP status code
        duration: Request duration in seconds
        request_headers: Request headers
        response_headers: Response headers
        request_body: Request body
        response_body: Response body
        user_agent: User agent string
        client_ip: Client IP address
        extra: Additional fields to include in the log
        level: Log level
    """
    log_data = {
        "request_id": request_id,
        "method": method,
        "url": url,
        "status_code": status_code,
        "duration": duration,
        "user_agent": user_agent,
        "client_ip": client_ip,
    }
    
    # Add headers if provided
    if request_headers:
        log_data["request_headers"] = dict(request_headers)
    
    if response_headers:
        log_data["response_headers"] = dict(response_headers)
    
    # Add request/response bodies if they can be serialized to JSON
    if request_body is not None:
        if isinstance(request_body, (dict, list)):
            log_data["request_body"] = request_body
        elif isinstance(request_body, (str, bytes)):
            try:
                log_data["request_body"] = json.loads(request_body)
            except (json.JSONDecodeError, UnicodeDecodeError):
                log_data["request_body"] = str(request_body)[:1000]  # Truncate long bodies
    
    if response_body is not None:
        if isinstance(response_body, (dict, list)):
            log_data["response_body"] = response_body
        elif isinstance(response_body, (str, bytes)):
            try:
                log_data["response_body"] = json.loads(response_body)
            except (json.JSONDecodeError, UnicodeDecodeError):
                log_data["response_body"] = str(response_body)[:1000]  # Truncate long bodies
    
    # Add extra fields
    if extra:
        log_data.update(extra)
    
    # Log the message
    logger.log(level, "HTTP %s %s - %s", method, url, status_code, extra={"request_id": request_id, **log_data})


def log_exception(
    logger: logging.Logger,
    message: str,
    exc_info: Optional[BaseException] = None,
    extra: Optional[Dict[str, Any]] = None,
    level: int = logging.ERROR,
) -> None:
    """
    Log an exception with additional context.
    
    Args:
        logger: Logger instance
        message: Log message
        exc_info: Exception to log (if None, uses sys.exc_info())
        extra: Additional fields to include in the log
        level: Log level
    """
    if exc_info is None:
        exc_info = sys.exc_info()
    
    extra = extra or {}
    
    # Add exception information to the log
    if exc_info and exc_info[0] is not None:
        exc_type, exc_value, exc_traceback = exc_info
        extra.update({
            "exception_type": exc_type.__name__ if exc_type else None,
            "exception_message": str(exc_value) if exc_value else None,
        })
    
    logger.log(level, message, exc_info=exc_info, extra=extra)
