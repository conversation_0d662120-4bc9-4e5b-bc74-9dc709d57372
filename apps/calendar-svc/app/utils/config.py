import os
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, TypeVar, Union, Callable
from pydantic import Field, validator, PostgresDsn, HttpUrl, AnyUrl, RedisDsn
from pydantic.types import SecretStr
from pydantic_settings import BaseSettings

# Configure logger
logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    """Application settings."""

    # Application settings
    APP_NAME: str = "AI Lex Receptionist"
    APP_VERSION: str = "1.0.0"
    APP_ENV: str = "development"
    DEBUG: bool = False
    SECRET_KEY: SecretStr = Field(..., env="SECRET_KEY")
    API_PREFIX: str = "/api/v1"
    BACKEND_CORS_ORIGINS: List[str] = ["*"]
    
    # Security
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30
    ALGORITHM: str = "HS256"
    
    # Database
    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: SecretStr
    POSTGRES_DB: str
    POSTGRES_PORT: str = "5432"
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[SecretStr] = None
    REDIS_DB: int = 0
    REDIS_URI: Optional[RedisDsn] = None
    
    # Email
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[SecretStr] = None
    SMTP_USE_TLS: bool = True
    SMTP_FROM_EMAIL: str = "<EMAIL>"
    EMAILS_ENABLED: bool = False
    
    # Storage
    UPLOAD_DIR: str = "uploads"
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"]
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: Optional[str] = None
    
    # CORS
    CORS_ORIGINS: List[str] = ["*"]
    CORS_METHODS: List[str] = ["*"]
    CORS_HEADERS: List[str] = ["*"]
    
    # Rate limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_DEFAULT: str = "100/minute"
    
    # Sentry
    SENTRY_DSN: Optional[HttpUrl] = None
    SENTRY_ENVIRONMENT: str = "development"
    
    # Google OAuth
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[SecretStr] = None
    GOOGLE_REDIRECT_URI: Optional[str] = None
    
    # Microsoft OAuth
    MICROSOFT_CLIENT_ID: Optional[str] = None
    MICROSOFT_CLIENT_SECRET: Optional[SecretStr] = None
    MICROSOFT_REDIRECT_URI: Optional[str] = None
    
    # Twilio
    TWILIO_ACCOUNT_SID: Optional[str] = None
    TWILIO_AUTH_TOKEN: Optional[SecretStr] = None
    TWILIO_PHONE_NUMBER: Optional[str] = None
    
    # AWS
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[SecretStr] = None
    AWS_REGION: str = "us-east-1"
    AWS_S3_BUCKET: Optional[str] = None
    
    # JWT
    JWT_SECRET_KEY: SecretStr = Field(..., env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 30
    
    # Security headers
    SECURE_HEADERS: bool = True
    SECURE_CSP: str = "default-src 'self';"
    SECURE_HSTS_SECONDS: int = ********  # 1 year
    
    # API Keys
    API_KEYS: List[str] = []
    
    # Feature flags
    FEATURE_EMAIL_VERIFICATION: bool = False
    FEATURE_PHONE_VERIFICATION: bool = False
    FEATURE_2FA: bool = False
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        
    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        
        user = values.get("POSTGRES_USER")
        password = values.get("POSTGRES_PASSWORD")
        host = values.get("POSTGRES_SERVER")
        port = values.get("POSTGRES_PORT")
        db = values.get("POSTGRES_DB")
        
        if not all([user, host, port, db]):
            return None
            
        password_str = password.get_secret_value() if password else None
        
        # Build the DSN string manually
        parts = ["postgresql://"]
        if user:
            parts.append(user)
            if password_str:
                parts.append(f":{password_str}")
            parts.append("@")
        
        parts.append(host)
        if port:
            parts.append(f":{port}")
        
        path = f"/{db}" if db else ""
        parts.append(path)
        
        return "".join(parts)
    
    @validator("REDIS_URI", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
            
        host = values.get("REDIS_HOST", "localhost")
        port = values.get("REDIS_PORT", 6379)
        password = values.get("REDIS_PASSWORD")
        db = values.get("REDIS_DB", 0)
        
        password_str = password.get_secret_value() if password else None
        
        # Build the Redis DSN string manually
        parts = ["redis://"]
        
        if password_str:
            parts.append(f":{password_str}@")
            
        parts.append(host)
        
        if port:
            parts.append(f":{port}")
            
        if db is not None:
            parts.append(f"/{db}")
            
        return "".join(parts)
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("APP_ENV")
    def validate_app_env(cls, v: str) -> str:
        valid_envs = ["development", "testing", "staging", "production"]
        if v not in valid_envs:
            raise ValueError(f"APP_ENV must be one of {valid_envs}")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v: str) -> str:
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    @validator("UPLOAD_DIR")
    def validate_upload_dir(cls, v: str) -> str:
        # Create upload directory if it doesn't exist
        upload_dir = Path(v)
        upload_dir.mkdir(parents=True, exist_ok=True)
        return str(upload_dir.absolute())


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """
    Get the settings instance.
    
    Returns:
        Settings: The settings instance
    """
    return settings


def configure_logging() -> None:
    """
    Configure logging based on settings.
    """
    import logging.config
    import sys
    
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": settings.LOG_FORMAT,
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(levelname)s %(name)s %(message)s",
            },
        },
        "handlers": {
            "console": {
                "level": settings.LOG_LEVEL,
                "class": "logging.StreamHandler",
                "formatter": "json" if settings.APP_ENV == "production" else "standard",
                "stream": sys.stdout,
            },
        },
        "loggers": {
            "": {
                "handlers": ["console"],
                "level": settings.LOG_LEVEL,
                "propagate": True,
            },
            "uvicorn": {
                "handlers": ["console"],
                "level": settings.LOG_LEVEL,
                "propagate": False,
            },
            "sqlalchemy": {
                "handlers": ["console"],
                "level": "WARNING",
                "propagate": False,
            },
        },
    }
    
    if settings.LOG_FILE:
        log_config["handlers"]["file"] = {
            "level": settings.LOG_LEVEL,
            "class": "logging.handlers.RotatingFileHandler",
            "filename": settings.LOG_FILE,
            "maxBytes": 10 * 1024 * 1024,  # 10MB
            "backupCount": 5,
            "formatter": "json" if settings.APP_ENV == "production" else "standard",
            "encoding": "utf8",
        }
        log_config["loggers"][""]["handlers"].append("file")
    
    logging.config.dictConfig(log_config)
    
    # Set log level for all loggers
    logging.getLogger().setLevel(settings.LOG_LEVEL)
    
    # Disable noisy loggers
    logging.getLogger("botocore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("aiosignal").setLevel(logging.WARNING)
    logging.getLogger("frozenlist").setLevel(logging.WARNING)
    logging.getLogger("multipart").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("hpack").setLevel(logging.WARNING)
    logging.getLogger("h2").setLevel(logging.WARNING)
    logging.getLogger("hpack.table").setLevel(logging.WARNING)
    logging.getLogger("hpack.hpack").setLevel(logging.WARNING)
    logging.getLogger("hpack.huffman").setLevel(logging.WARNING)
    logging.getLogger("hpack.huffman_table").setLevel(logging.WARNING)
    logging.getLogger("hpack.huffman_table").setLevel(logging.WARNING)
    
    logger.info("Logging configured with level: %s", settings.LOG_LEVEL)
