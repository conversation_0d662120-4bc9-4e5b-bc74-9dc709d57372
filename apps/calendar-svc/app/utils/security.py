import os
import hmac
import hashlib
import base64
import uuid
import time
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, Union

import jwt
from jose import JWTError, jwt as jose_jwt
from passlib.context import CryptContext
from pydantic import BaseModel, ValidationError

from ...config import settings

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto"
)

# JWT settings
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7 days
REFRESH_TOKEN_EXPIRE_DAYS = 30


class TokenPayload(BaseModel):
    """JWT token payload."""
    sub: str  # Subject (user ID)
    exp: int  # Expiration time
    iat: int  # Issued at
    jti: str  # JWT ID
    token_type: str = "access"  # access or refresh
    scopes: list[str] = []
    
    class Config:
        extra = "allow"  # Allow extra fields in the token


class Token(BaseModel):
    """Token response model."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int = ACCESS_TOKEN_EXPIRE_MINUTES * 60  # in seconds


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash."""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.warning(f"Password verification failed: {e}")
        return False


def get_password_hash(password: str) -> str:
    """Generate a password hash."""
    return pwd_context.hash(password)


def generate_token(
    subject: Union[str, int],
    expires_delta: Optional[timedelta] = None,
    token_type: str = "access",
    **extra
) -> str:
    """
    Generate a JWT token.
    
    Args:
        subject: The subject (usually user ID)
        expires_delta: Optional timedelta for token expiration
        token_type: Type of token ("access" or "refresh")
        **extra: Additional claims to include in the token
        
    Returns:
        Encoded JWT token
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        if token_type == "refresh":
            expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        else:
            expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    # Generate a unique ID for the token
    jti = str(uuid.uuid4())
    
    # Create the token payload
    payload = {
        "sub": str(subject),
        "exp": expire,
        "iat": datetime.now(timezone.utc),
        "jti": jti,
        "token_type": token_type,
        **extra
    }
    
    # Encode the token
    encoded_jwt = jose_jwt.encode(
        payload,
        settings.SECRET_KEY,
        algorithm=ALGORITHM
    )
    
    return encoded_jwt


def create_access_token(
    subject: Union[str, int],
    expires_delta: Optional[timedelta] = None,
    **extra
) -> str:
    """Create an access token."""
    return generate_token(
        subject=subject,
        expires_delta=expires_delta,
        token_type="access",
        **extra
    )


def create_refresh_token(
    subject: Union[str, int],
    expires_delta: Optional[timedelta] = None,
    **extra
) -> str:
    """Create a refresh token."""
    return generate_token(
        subject=subject,
        expires_delta=expires_delta or timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS),
        token_type="refresh",
        **extra
    )


def create_tokens(
    subject: Union[str, int],
    **extra
) -> Token:
    """Create both access and refresh tokens."""
    access_token = create_access_token(subject, **extra)
    refresh_token = create_refresh_token(subject, **extra)
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


def verify_token(
    token: str,
    token_type: Optional[str] = None,
    leeway: int = 0
) -> Optional[TokenPayload]:
    """
    Verify a JWT token and return the payload if valid.
    
    Args:
        token: The JWT token to verify
        token_type: Expected token type ("access" or "refresh")
        leeway: Leeway in seconds for expiration time validation
        
    Returns:
        TokenPayload if valid, None otherwise
    """
    try:
        # Decode the token
        payload = jose_jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[ALGORITHM],
            options={"leeway": leeway}
        )
        
        # Validate the token type if specified
        if token_type and payload.get("token_type") != token_type:
            logger.warning(f"Invalid token type: {payload.get('token_type')} (expected {token_type})")
            return None
        
        # Convert to TokenPayload model
        return TokenPayload(**payload)
        
    except (JWTError, ValidationError) as e:
        logger.warning(f"Token validation failed: {e}")
        return None


def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode a JWT token without verification.
    Use this only when you trust the token source.
    """
    try:
        return jose_jwt.get_unverified_claims(token)
    except JWTError as e:
        logger.warning(f"Token decoding failed: {e}")
        return None


def generate_reset_token(email: str) -> str:
    """Generate a secure token for password reset."""
    # Create a timestamp to make the token expire
    timestamp = str(int(time.time()))
    
    # Create a message to sign
    message = f"{email}:{timestamp}"
    
    # Generate a signature
    signature = hmac.new(
        key=settings.SECRET_KEY.encode(),
        msg=message.encode(),
        digestmod=hashlib.sha256
    ).hexdigest()
    
    # Combine into a single token
    token = f"{email}:{timestamp}:{signature}"
    
    # Base64 encode for URL safety
    return base64.urlsafe_b64encode(token.encode()).decode()


def verify_reset_token(token: str, max_age: int = 3600) -> Optional[str]:
    """
    Verify a password reset token and return the email if valid.
    
    Args:
        token: The reset token to verify
        max_age: Maximum token age in seconds
        
    Returns:
        Email address if token is valid, None otherwise
    """
    try:
        # Decode the token
        token = base64.urlsafe_b64decode(token.encode()).decode()
        email, timestamp, signature = token.split(":")
        
        # Check if token has expired
        if int(time.time()) - int(timestamp) > max_age:
            return None
        
        # Verify the signature
        message = f"{email}:{timestamp}"
        expected_signature = hmac.new(
            key=settings.SECRET_KEY.encode(),
            msg=message.encode(),
            digestmod=hashlib.sha256
        ).hexdigest()
        
        if not hmac.compare_digest(signature, expected_signature):
            return None
        
        return email
        
    except (ValueError, binascii.Error) as e:
        logger.warning(f"Invalid reset token: {e}")
        return None


def generate_api_key(prefix: str = "sk_") -> str:
    """Generate a secure API key."""
    # Generate a random 32-byte key
    random_bytes = os.urandom(32)
    
    # Encode in URL-safe base64
    key = base64.urlsafe_b64encode(random_bytes).decode('ascii').rstrip('=')
    
    # Add prefix and return
    return f"{prefix}{key}"


def hash_api_key(key: str) -> str:
    """Hash an API key for secure storage."""
    # Use a different salt than password hashing
    salt = settings.SECRET_KEY.encode() + b"api_key_salt"
    
    # Generate a hash using HMAC-SHA256
    h = hmac.new(
        key=salt,
        msg=key.encode(),
        digestmod=hashlib.sha256
    )
    
    # Return the hex digest
    return h.hexdigest()


def verify_api_key(provided_key: str, stored_hash: str) -> bool:
    """Verify an API key against its stored hash."""
    if not provided_key or not stored_hash:
        return False
    
    # Hash the provided key using the same method
    computed_hash = hash_api_key(provided_key)
    
    # Compare hashes in constant time
    return hmac.compare_digest(computed_hash, stored_hash)
