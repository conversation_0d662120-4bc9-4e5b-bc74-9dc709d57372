import re
import html
from typing import Any, Dict, List, Optional, Type, TypeVar, Union, Callable
from datetime import datetime, date
from uuid import UUID
import email_validator
from pydantic import BaseModel, validator, ValidationError, Field, HttpUrl, EmailStr

T = TypeVar('T')

# Common regular expressions
PHONE_REGEX = re.compile(r'^\+?[1-9]\d{1,14}$')  # E.164 format
ZIP_CODE_REGEX = re.compile(r'^\d{5}(-\d{4})?$')
SSN_REGEX = re.compile(r'^\d{3}-\d{2}-\d{4}$')
CREDIT_CARD_REGEX = re.compile(r'^\d{4}[ -]?\d{4}[ -]?\d{4}[ -]?\d{4}$')


def validate_email(email: str) -> str:
    """
    Validate and normalize an email address.
    
    Args:
        email: The email address to validate
        
    Returns:
        The normalized email address
        
    Raises:
        ValueError: If the email is invalid
    """
    try:
        # Validate and normalize the email
        valid = email_validator.validate_email(email)
        return valid.email
    except email_validator.EmailNotValidError as e:
        raise ValueError(f"Invalid email address: {str(e)}")


def validate_phone(phone: str) -> str:
    """
    Validate and normalize a phone number.
    
    Args:
        phone: The phone number to validate (E.164 format)
        
    Returns:
        The normalized phone number
        
    Raises:
        ValueError: If the phone number is invalid
    """
    # Remove all non-digit characters except leading +
    normalized = re.sub(r'[^\d+]', '', phone)
    
    # Ensure the number starts with + and country code
    if not normalized.startswith('+'):
        normalized = f"+1{normalized}"  # Default to US/Canada
    
    # Validate against E.164 format
    if not PHONE_REGEX.match(normalized):
        raise ValueError("Phone number must be in E.164 format (e.g., +14155552671)")
    
    return normalized


def sanitize_input(
    value: Any,
    allowed_tags: Optional[List[str]] = None,
    allowed_attrs: Optional[Dict[str, List[str]]] = None
) -> str:
    """
    Sanitize user input to prevent XSS and other injection attacks.
    
    Args:
        value: The input value to sanitize
        allowed_tags: List of allowed HTML tags (default: no tags allowed)
        allowed_attrs: Dictionary mapping tags to allowed attributes
        
    Returns:
        The sanitized string
    """
    if value is None:
        return ""
    
    # Convert to string
    if not isinstance(value, str):
        value = str(value)
    
    # Default: escape all HTML
    if not allowed_tags:
        return html.escape(value)
    
    # Use a proper HTML sanitizer if tags are allowed
    try:
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(value, 'html.parser')
        
        # Remove all tags not in the allowed list
        for tag in soup.find_all(True):
            if tag.name not in allowed_tags:
                tag.unwrap()
            else:
                # Remove attributes not in the allowed list
                attrs = dict(tag.attrs)
                tag.attrs.clear()
                
                if allowed_attrs and tag.name in allowed_attrs:
                    for attr in allowed_attrs[tag.name]:
                        if attr in attrs:
                            tag[attr] = attrs[attr]
        
        return str(soup)
    except ImportError:
        # Fall back to escaping everything if BeautifulSoup is not available
        return html.escape(value)


def validate_url(url: str, allowed_domains: Optional[List[str]] = None) -> str:
    """
    Validate and normalize a URL.
    
    Args:
        url: The URL to validate
        allowed_domains: List of allowed domains (optional)
        
    Returns:
        The normalized URL
        
    Raises:
        ValueError: If the URL is invalid or not in the allowed domains
    """
    try:
        # Use Pydantic's HttpUrl for basic validation
        parsed = HttpUrl(url)
        
        # Check against allowed domains if specified
        if allowed_domains:
            domain = parsed.host
            if not any(domain == d or domain.endswith(f".{d}") for d in allowed_domains):
                raise ValueError(f"Domain not allowed: {domain}")
        
        return str(parsed)
    except (ValueError, ValidationError) as e:
        raise ValueError(f"Invalid URL: {str(e)}")


def validate_date_string(date_str: str, format: str = "%Y-%m-%d") -> str:
    """
    Validate a date string against a specific format.
    
    Args:
        date_str: The date string to validate
        format: The expected date format (default: YYYY-MM-DD)
        
    Returns:
        The normalized date string
        
    Raises:
        ValueError: If the date string is invalid
    """
    try:
        # Parse the date to validate it
        parsed_date = datetime.strptime(date_str, format).date()
        # Return in the same format for consistency
        return parsed_date.strftime(format)
    except ValueError as e:
        raise ValueError(f"Invalid date format. Expected {format}: {str(e)}")


def validate_json_string(json_str: str) -> bool:
    """
    Validate if a string is valid JSON.
    
    Args:
        json_str: The string to validate
        
    Returns:
        True if the string is valid JSON, False otherwise
    """
    import json
    try:
        json.loads(json_str)
        return True
    except (ValueError, TypeError):
        return False


def validate_credit_card(number: str) -> str:
    """
    Validate a credit card number using the Luhn algorithm.
    
    Args:
        number: The credit card number to validate
        
    Returns:
        The normalized credit card number (digits only)
        
    Raises:
        ValueError: If the credit card number is invalid
    """
    # Remove all non-digit characters
    digits = re.sub(r'\D', '', number)
    
    # Check if it matches the basic pattern
    if not CREDIT_CARD_REGEX.match(digits):
        raise ValueError("Invalid credit card number format")
    
    # Luhn algorithm
    total = 0
    for i, digit in enumerate(reversed(digits)):
        n = int(digit)
        if i % 2 == 1:
            n *= 2
            if n > 9:
                n = (n // 10) + (n % 10)
        total += n
    
    if total % 10 != 0:
        raise ValueError("Invalid credit card number (checksum failed)")
    
    return digits


def validate_ssn(ssn: str) -> str:
    """
    Validate a Social Security Number (SSN).
    
    Args:
        ssn: The SSN to validate (format: XXX-XX-XXXX)
        
    Returns:
        The normalized SSN
        
    Raises:
        ValueError: If the SSN is invalid
    """
    # Remove all non-digit characters
    digits = re.sub(r'\D', '', ssn)
    
    # Check length
    if len(digits) != 9:
        raise ValueError("SSN must be 9 digits")
    
    # Check for invalid prefixes
    area = int(digits[:3])
    if area == 666 or area == 0 or area >= 900:
        raise ValueError("Invalid SSN area number")
    
    # Check for all zeros in any group
    if digits[3:5] == '00' or digits[5:] == '0000':
        raise ValueError("Invalid SSN group or serial number")
    
    # Return in standard format
    return f"{digits[:3]}-{digits[3:5]}-{digits[5:]}"


def validate_postal_code(code: str, country: str = "US") -> str:
    """
    Validate a postal/zip code based on the country.
    
    Args:
        code: The postal code to validate
        country: The country code (default: "US")
        
    Returns:
        The normalized postal code
        
    Raises:
        ValueError: If the postal code is invalid for the specified country
    """
    code = code.strip().upper()
    
    if country.upper() == "US":
        if not ZIP_CODE_REGEX.match(code):
            raise ValueError("Invalid US ZIP code format (expected: 12345 or 12345-6789)")
    # Add more country-specific validations as needed
    
    return code


def validate_enum(value: Any, enum_class: Type) -> Any:
    """
    Validate that a value is a valid enum value.
    
    Args:
        value: The value to validate
        enum_class: The enum class to validate against
        
    Returns:
        The validated enum value
        
    Raises:
        ValueError: If the value is not a valid enum value
    """
    try:
        if isinstance(value, str):
            return enum_class[value.upper()]
        return enum_class(value)
    except (KeyError, ValueError):
        valid_values = [e.value for e in enum_class]
        raise ValueError(f"Invalid value. Must be one of: {', '.join(map(str, valid_values))}")


def validate_list_items(
    items: List[Any],
    validator: Callable[[Any], T],
    min_length: Optional[int] = None,
    max_length: Optional[int] = None,
    unique: bool = False
) -> List[T]:
    """
    Validate a list of items using a validator function.
    
    Args:
        items: The list of items to validate
        validator: A function that validates a single item
        min_length: Minimum number of items (optional)
        max_length: Maximum number of items (optional)
        unique: Whether all items must be unique
        
    Returns:
        The validated list of items
        
    Raises:
        ValueError: If validation fails
    """
    if not isinstance(items, list):
        raise ValueError("Expected a list")
    
    if min_length is not None and len(items) < min_length:
        raise ValueError(f"List must contain at least {min_length} items")
    
    if max_length is not None and len(items) > max_length:
        raise ValueError(f"List must contain at most {max_length} items")
    
    validated_items = []
    seen = set()
    
    for i, item in enumerate(items):
        try:
            validated = validator(item)
            
            if unique:
                # Convert to a hashable type for set operations
                if isinstance(validated, (dict, list)):
                    hashable = frozenset(validated.items()) if isinstance(validated, dict) else frozenset(validated)
                else:
                    hashable = validated
                
                if hashable in seen:
                    raise ValueError(f"Duplicate item at position {i}: {item}")
                seen.add(hashable)
            
            validated_items.append(validated)
        except Exception as e:
            raise ValueError(f"Invalid item at position {i}: {str(e)}")
    
    return validated_items
