"""
Custom exceptions for the application.

This module defines custom exceptions that can be raised throughout the application
for consistent error handling and reporting.
"""
from typing import Any, Dict, List, Optional, Union
from fastapi import status


class AppError(Exception):
    """Base class for all application exceptions."""
    
    def __init__(
        self,
        message: str = "An error occurred",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code: Optional[str] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            status_code: HTTP status code
            error_code: A machine-readable error code
            details: Additional error details
        """
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or f"{status_code:03d}"
        self.details = details
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the exception to a dictionary."""
        error_dict = {
            "message": self.message,
            "error_code": self.error_code,
        }
        
        if self.details is not None:
            error_dict["details"] = self.details
        
        return error_dict


class ValidationError(AppError):
    """Raised when input validation fails."""
    
    def __init__(
        self,
        message: str = "Validation error",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="validation_error",
            details=details,
        )


class AuthenticationError(AppError):
    """Raised when authentication fails."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code="authentication_error",
            details=details,
        )


class AuthorizationError(AppError):
    """Raised when authorization fails."""
    
    def __init__(
        self,
        message: str = "Permission denied",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            error_code="authorization_error",
            details=details,
        )


class NotFoundError(AppError):
    """Raised when a resource is not found."""
    
    def __init__(
        self,
        resource: str = "Resource",
        resource_id: Optional[Any] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            resource: The name of the resource that was not found
            resource_id: The ID of the resource that was not found
            details: Additional error details
        """
        message = f"{resource} not found"
        if resource_id is not None:
            message = f"{resource} with ID '{resource_id}' not found"
        
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            error_code="not_found",
            details=details,
        )


class ConflictError(AppError):
    """Raised when a resource conflict occurs."""
    
    def __init__(
        self,
        message: str = "Resource conflict",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            error_code="conflict",
            details=details,
        )


class RateLimitError(AppError):
    """Raised when a rate limit is exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            retry_after: Number of seconds to wait before retrying
            details: Additional error details
        """
        headers = {}
        if retry_after is not None:
            headers["Retry-After"] = str(retry_after)
        
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            error_code="rate_limit_exceeded",
            details=details,
        )
        
        self.headers = headers


class ServiceUnavailableError(AppError):
    """Raised when a service is temporarily unavailable."""
    
    def __init__(
        self,
        message: str = "Service temporarily unavailable",
        retry_after: Optional[int] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            retry_after: Number of seconds to wait before retrying
            details: Additional error details
        """
        headers = {}
        if retry_after is not None:
            headers["Retry-After"] = str(retry_after)
        
        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_code="service_unavailable",
            details=details,
        )
        
        self.headers = headers


class BadRequestError(AppError):
    """Raised when a bad request is made."""
    
    def __init__(
        self,
        message: str = "Bad request",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code="bad_request",
            details=details,
        )


class MethodNotAllowedError(AppError):
    """Raised when an HTTP method is not allowed."""
    
    def __init__(
        self,
        method: str,
        allowed_methods: Optional[List[str]] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            method: The HTTP method that was not allowed
            allowed_methods: List of allowed HTTP methods
            details: Additional error details
        """
        message = f"Method '{method}' not allowed"
        
        headers = {}
        if allowed_methods:
            headers["Allow"] = ", ".join(allowed_methods)
        
        super().__init__(
            message=message,
            status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
            error_code="method_not_allowed",
            details=details,
        )
        
        self.headers = headers


class NotAcceptableError(AppError):
    """Raised when the requested content type is not acceptable."""
    
    def __init__(
        self,
        message: str = "Requested content type not acceptable",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_406_NOT_ACCEPTABLE,
            error_code="not_acceptable",
            details=details,
        )


class UnsupportedMediaTypeError(AppError):
    """Raised when the request content type is not supported."""
    
    def __init__(
        self,
        content_type: Optional[str] = None,
        supported_types: Optional[List[str]] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            content_type: The unsupported content type
            supported_types: List of supported content types
            details: Additional error details
        """
        message = "Unsupported media type"
        if content_type:
            message = f"Unsupported media type: {content_type}"
        
        headers = {}
        if supported_types:
            headers["Accept"] = ", ".join(supported_types)
        
        super().__init__(
            message=message,
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            error_code="unsupported_media_type",
            details=details,
        )
        
        self.headers = headers


class RequestEntityTooLargeError(AppError):
    """Raised when the request entity is too large."""
    
    def __init__(
        self,
        message: str = "Request entity too large",
        max_size: Optional[int] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            max_size: Maximum allowed size in bytes
            details: Additional error details
        """
        if max_size is not None:
            message = f"Request entity too large. Maximum size is {max_size} bytes."
        
        super().__init__(
            message=message,
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            error_code="request_entity_too_large",
            details=details,
        )


class UnprocessableEntityError(AppError):
    """Raised when the request is well-formed but contains semantic errors."""
    
    def __init__(
        self,
        message: str = "Unprocessable entity",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="unprocessable_entity",
            details=details,
        )


class InternalServerError(AppError):
    """Raised when an internal server error occurs."""
    
    def __init__(
        self,
        message: str = "Internal server error",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="internal_server_error",
            details=details,
        )


class NotImplementedError(AppError):
    """Raised when a feature is not implemented."""
    
    def __init__(
        self,
        message: str = "Not implemented",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            error_code="not_implemented",
            details=details,
        )


class BadGatewayError(AppError):
    """Raised when a bad gateway error occurs."""
    
    def __init__(
        self,
        message: str = "Bad gateway",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_502_BAD_GATEWAY,
            error_code="bad_gateway",
            details=details,
        )


class GatewayTimeoutError(AppError):
    """Raised when a gateway timeout occurs."""
    
    def __init__(
        self,
        message: str = "Gateway timeout",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            error_code="gateway_timeout",
            details=details,
        )


class HTTPVersionNotSupportedError(AppError):
    """Raised when the HTTP version is not supported."""
    
    def __init__(
        self,
        message: str = "HTTP version not supported",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_505_HTTP_VERSION_NOT_SUPPORTED,
            error_code="http_version_not_supported",
            details=details,
        )


class DatabaseError(AppError):
    """Raised when a database error occurs."""
    
    def __init__(
        self,
        message: str = "Database error",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="database_error",
            details=details,
        )


class IntegrityError(DatabaseError):
    """Raised when a database integrity error occurs."""
    
    def __init__(
        self,
        message: str = "Database integrity error",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            details=details,
        )
        self.error_code = "integrity_error"


class TimeoutError(AppError):
    """Raised when an operation times out."""
    
    def __init__(
        self,
        message: str = "Operation timed out",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            error_code="timeout_error",
            details=details,
        )


class NetworkError(AppError):
    """Raised when a network error occurs."""
    
    def __init__(
        self,
        message: str = "Network error",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_code="network_error",
            details=details,
        )


class ExternalServiceError(AppError):
    """Raised when an external service returns an error."""
    
    def __init__(
        self,
        service_name: str,
        status_code: int,
        message: Optional[str] = None,
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            service_name: The name of the external service
            status_code: The HTTP status code returned by the service
            message: A human-readable error message
            details: Additional error details
        """
        if message is None:
            message = f"Error from {service_name} service"
        
        super().__init__(
            message=message,
            status_code=status_code,
            error_code=f"{service_name.lower()}_error",
            details=details,
        )
        
        self.service_name = service_name


class ConfigurationError(AppError):
    """Raised when there is a configuration error."""
    
    def __init__(
        self,
        message: str = "Configuration error",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="configuration_error",
            details=details,
        )


class OAuthError(AppError):
    """Raised when an OAuth error occurs."""
    
    def __init__(
        self,
        message: str = "OAuth error",
        status_code: int = status.HTTP_400_BAD_REQUEST,
        error_code: str = "oauth_error",
        details: Optional[Union[Dict[str, Any], List[Any], str]] = None,
    ) -> None:
        """
        Initialize the exception.
        
        Args:
            message: A human-readable error message
            status_code: HTTP status code
            error_code: A machine-readable error code
            details: Additional error details
        """
        super().__init__(
            message=message,
            status_code=status_code,
            error_code=error_code,
            details=details,
        )


# Backward compatibility
HTTPException = AppError
ValidationError = ValidationError
AuthenticationError = AuthenticationError
AuthorizationError = AuthorizationError
NotFoundError = NotFoundError
ConflictError = ConflictError
RateLimitError = RateLimitError
BadRequestError = BadRequestError
NotAcceptableError = NotAcceptableError
UnsupportedMediaTypeError = UnsupportedMediaTypeError
RequestEntityTooLargeError = RequestEntityTooLargeError
UnprocessableEntityError = UnprocessableEntityError
InternalServerError = InternalServerError
NotImplementedError = NotImplementedError
BadGatewayError = BadGatewayError
GatewayTimeoutError = GatewayTimeoutError
HTTPVersionNotSupportedError = HTTPVersionNotSupportedError
DatabaseError = DatabaseError
IntegrityError = IntegrityError
TimeoutError = TimeoutError
NetworkError = NetworkError
ExternalServiceError = ExternalServiceError
ConfigurationError = ConfigurationError
