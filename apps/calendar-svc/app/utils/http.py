import asyncio
import logging
from typing import Any, Dict, Optional, Union, List, Tuple, Callable, Awaitable
from urllib.parse import urlencode, urljoin, urlparse, parse_qs

import aiohttp
from aiohttp import ClientSession, ClientTimeout, ClientResponse, ClientError
from pydantic import BaseModel, HttpUrl, validator, ValidationError

from ...config import settings

logger = logging.getLogger(__name__)


class HTTPClientError(Exception):
    """Base exception for HTTP client errors."""
    pass


class HTTPRequestError(HTTPClientError):
    """Exception raised when an HTTP request fails."""
    def __init__(self, status_code: int, message: str, response: Optional[ClientResponse] = None):
        self.status_code = status_code
        self.response = response
        self.message = message
        super().__init__(f"HTTP {status_code}: {message}")


class HTTPTimeoutError(HTTPClientError):
    """Exception raised when an HTTP request times out."""
    pass


class HTTPClient:
    """A reusable HTTP client with retry logic and request/response validation."""
    
    def __init__(
        self,
        base_url: str = "",
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        backoff_factor: float = 2.0,
        default_headers: Optional[Dict[str, str]] = None,
        raise_for_status: bool = True,
        verify_ssl: bool = True,
    ):
        """
        Initialize the HTTP client.
        
        Args:
            base_url: Base URL for all requests
            timeout: Default timeout in seconds
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            backoff_factor: Multiplier for retry delay (exponential backoff)
            default_headers: Default headers to include in all requests
            raise_for_status: Whether to raise an exception for non-2xx responses
            verify_ssl: Whether to verify SSL certificates
        """
        self.base_url = base_url.rstrip('/') if base_url else ""
        self.timeout = ClientTimeout(total=timeout)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.backoff_factor = backoff_factor
        self.default_headers = default_headers or {}
        self.raise_for_status = raise_for_status
        self.verify_ssl = verify_ssl
        
        # Session will be created on first use
        self._session: Optional[ClientSession] = None
    
    @property
    def session(self) -> ClientSession:
        """Get or create a session."""
        if self._session is None or self._session.closed:
            self._session = ClientSession(
                timeout=self.timeout,
                headers=self.default_headers,
                raise_for_status=False,
                trust_env=True,
            )
        return self._session
    
    async def close(self) -> None:
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    def _get_full_url(self, url: str) -> str:
        """Get the full URL by combining base URL and path."""
        if not url.startswith(('http://', 'https://')):
            return urljoin(self.base_url, url.lstrip('/'))
        return url
    
    async def _make_request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> ClientResponse:
        """Make an HTTP request with retry logic."""
        full_url = self._get_full_url(url)
        
        # Set default headers if not provided
        headers = kwargs.pop('headers', {})
        headers = {**self.default_headers, **headers}
        
        # Set SSL verification
        ssl = None if self.verify_ssl else False
        
        last_exception = None
        retry_delay = self.retry_delay
        
        for attempt in range(self.max_retries + 1):
            try:
                async with self.session.request(
                    method=method,
                    url=full_url,
                    headers=headers,
                    ssl=ssl,
                    **kwargs
                ) as response:
                    if self.raise_for_status and response.status >= 400:
                        try:
                            error_data = await response.json()
                            error_msg = error_data.get('detail', error_data.get('message', response.reason))
                        except (ValueError, KeyError):
                            error_msg = await response.text() or response.reason
                        
                        raise HTTPRequestError(
                            status_code=response.status,
                            message=error_msg,
                            response=response
                        )
                    
                    return response
                    
            except asyncio.TimeoutError as e:
                last_exception = HTTPTimeoutError(f"Request to {full_url} timed out after {self.timeout} seconds")
                logger.warning(f"Request timeout (attempt {attempt + 1}/{self.max_retries + 1}): {e}")
                
            except ClientError as e:
                last_exception = HTTPClientError(f"Request failed: {str(e)}")
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.max_retries + 1}): {e}")
                
            except Exception as e:
                last_exception = HTTPClientError(f"Unexpected error: {str(e)}")
                logger.error(f"Unexpected error during request: {e}", exc_info=True)
                break
            
            # If we've reached max retries, break
            if attempt == self.max_retries - 1:
                break
                
            # Otherwise, wait before retrying
            await asyncio.sleep(retry_delay)
            retry_delay *= self.backoff_factor
        
        # If we get here, all retries failed
        if last_exception:
            raise last_exception
        else:
            raise HTTPClientError("Unknown error during HTTP request")
    
    async def request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        json: Any = None,
        data: Any = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> ClientResponse:
        """Make an HTTP request."""
        return await self._make_request(
            method=method.upper(),
            url=url,
            params=params,
            json=json,
            data=data,
            headers=headers,
            **kwargs
        )
    
    async def get(
        self,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> ClientResponse:
        """Make a GET request."""
        return await self.request('GET', url, params=params, headers=headers, **kwargs)
    
    async def post(
        self,
        url: str,
        json: Any = None,
        data: Any = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> ClientResponse:
        """Make a POST request."""
        return await self.request('POST', url, json=json, data=data, headers=headers, **kwargs)
    
    async def put(
        self,
        url: str,
        json: Any = None,
        data: Any = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> ClientResponse:
        """Make a PUT request."""
        return await self.request('PUT', url, json=json, data=data, headers=headers, **kwargs)
    
    async def patch(
        self,
        url: str,
        json: Any = None,
        data: Any = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> ClientResponse:
        """Make a PATCH request."""
        return await self.request('PATCH', url, json=json, data=data, headers=headers, **kwargs)
    
    async def delete(
        self,
        url: str,
        json: Any = None,
        data: Any = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> ClientResponse:
        """Make a DELETE request."""
        return await self.request('DELETE', url, json=json, data=data, headers=headers, **kwargs)
    
    async def get_json(
        self,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        model: Optional[type[BaseModel]] = None,
        **kwargs
    ) -> Any:
        """Make a GET request and parse the response as JSON."""
        response = await self.get(url, params=params, headers=headers, **kwargs)
        data = await response.json()
        
        if model is not None:
            try:
                if isinstance(data, list):
                    return [model.parse_obj(item) for item in data]
                return model.parse_obj(data)
            except ValidationError as e:
                logger.error(f"Failed to validate response data: {e}")
                raise HTTPClientError(f"Invalid response data: {e}")
        
        return data
    
    async def post_json(
        self,
        url: str,
        json: Any = None,
        data: Any = None,
        headers: Optional[Dict[str, str]] = None,
        model: Optional[type[BaseModel]] = None,
        **kwargs
    ) -> Any:
        """Make a POST request and parse the response as JSON."""
        response = await self.post(url, json=json, data=data, headers=headers, **kwargs)
        response_data = await response.json()
        
        if model is not None:
            try:
                if isinstance(response_data, list):
                    return [model.parse_obj(item) for item in response_data]
                return model.parse_obj(response_data)
            except ValidationError as e:
                logger.error(f"Failed to validate response data: {e}")
                raise HTTPClientError(f"Invalid response data: {e}")
        
        return response_data


# Global HTTP client instance
http_client = HTTPClient()


async def close_http_client():
    """Close the global HTTP client."""
    await http_client.close()
