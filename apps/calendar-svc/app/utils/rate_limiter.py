import time
import asyncio
from typing import Dict, Optional, Any, Callable, Awaitable
from functools import wraps
from datetime import datetime, timedelta
import hashlib
import hmac

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse

from ...config import settings

# In-memory rate limit store (replace with <PERSON><PERSON> in production)
_rate_limit_store = {}
_rate_limit_locks = {}


class RateLimitExceeded(HTTPException):
    """Exception raised when a rate limit is exceeded."""
    
    def __init__(
        self,
        detail: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
    ):
        if detail is None:
            detail = "Rate limit exceeded"
        
        if headers is None:
            headers = {}
        
        # Add rate limit headers
        headers.update({
            "X-RateLimit-Limit": str(headers.get("X-RateLimit-Limit", 0)),
            "X-RateLimit-Remaining": str(headers.get("X-RateLimit-Remaining", 0)),
            "X-RateLimit-Reset": str(headers.get("X-RateLimit-Reset", 0)),
            "Retry-After": str(headers.get("Retry-After", 60)),
        })
        
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            headers=headers
        )


class RateLimiter:
    """A rate limiter for API endpoints."""
    
    def __init__(
        self,
        limit: int = 100,
        window: int = 60,  # in seconds
        key_prefix: str = "rate_limit",
        identifier: Optional[Callable[[Request], str]] = None,
        error_message: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize the rate limiter.
        
        Args:
            limit: Maximum number of requests allowed in the time window
            window: Time window in seconds
            key_prefix: Prefix for rate limit keys
            identifier: Function to extract a unique identifier from the request
            error_message: Custom error message
            headers: Additional headers to include in the response
        """
        self.limit = limit
        self.window = window
        self.key_prefix = key_prefix
        self.identifier = identifier or self._default_identifier
        self.error_message = error_message or "Rate limit exceeded"
        self.headers = headers or {}
    
    def _default_identifier(self, request: Request) -> str:
        """Default identifier function using client IP."""
        # Get client IP
        if "x-forwarded-for" in request.headers:
            ip = request.headers["x-forwarded-for"].split(",")[0].strip()
        else:
            ip = request.client.host if request.client else "unknown"
        
        # Get the endpoint path
        path = request.url.path
        
        # Combine IP and path for the identifier
        return f"{ip}:{path}"
    
    def _get_key(self, identifier: str) -> str:
        """Get the rate limit key for an identifier."""
        # Create a hash of the identifier to ensure it's a valid key
        key = f"{self.key_prefix}:{identifier}"
        return hashlib.sha256(key.encode()).hexdigest()
    
    def _get_window_start(self, timestamp: float) -> int:
        """Get the start of the current time window."""
        return int((timestamp // self.window) * self.window)
    
    def _get_expiry(self) -> int:
        """Get the expiry time for the current window."""
        return self._get_window_start(time.time()) + self.window
    
    def _get_headers(
        self,
        remaining: int,
        reset: int,
        limit: Optional[int] = None
    ) -> Dict[str, str]:
        """Get rate limit headers."""
        if limit is None:
            limit = self.limit
            
        return {
            **self.headers,
            "X-RateLimit-Limit": str(limit),
            "X-RateLimit-Remaining": str(remaining),
            "X-RateLimit-Reset": str(reset),
            "Retry-After": str(max(1, reset - int(time.time()))),
        }
    
    async def _increment(self, key: str, current_time: float) -> Dict[str, Any]:
        """Increment the request count for a key."""
        window_start = self._get_window_start(current_time)
        expiry = window_start + self.window
        
        # Ensure thread safety
        if key not in _rate_limit_locks:
            _rate_limit_locks[key] = asyncio.Lock()
        
        async with _rate_limit_locks[key]:
            # Get the current count and window
            if key not in _rate_limit_store:
                _rate_limit_store[key] = {
                    "count": 0,
                    "window_start": window_start,
                    "expiry": expiry,
                }
            
            # Reset the counter if we're in a new window
            if _rate_limit_store[key]["window_start"] < window_start:
                _rate_limit_store[key] = {
                    "count": 0,
                    "window_start": window_start,
                    "expiry": expiry,
                }
            
            # Increment the count
            _rate_limit_store[key]["count"] += 1
            
            # Clean up old entries
            self._cleanup(current_time)
            
            return {
                "count": _rate_limit_store[key]["count"],
                "remaining": max(0, self.limit - _rate_limit_store[key]["count"]),
                "reset": _rate_limit_store[key]["expiry"],
                "limit": self.limit,
            }
    
    def _cleanup(self, current_time: float) -> None:
        """Clean up expired rate limit entries."""
        expired_keys = [
            key for key, data in _rate_limit_store.items()
            if data["expiry"] < current_time
        ]
        
        for key in expired_keys:
            _rate_limit_store.pop(key, None)
            _rate_limit_locks.pop(key, None)
    
    async def is_rate_limited(self, request: Request) -> bool:
        """Check if a request is rate limited."""
        # Skip rate limiting in development
        if settings.DEBUG:
            return False
        
        identifier = self.identifier(request)
        key = self._get_key(identifier)
        
        # Increment the counter
        result = await self._increment(key, time.time())
        
        # Check if the limit is exceeded
        if result["count"] > self.limit:
            # Get the reset time
            reset = result["reset"]
            retry_after = max(1, reset - int(time.time()))
            
            # Prepare headers
            headers = self._get_headers(
                remaining=0,
                reset=reset,
                limit=self.limit
            )
            
            raise RateLimitExceeded(
                detail=self.error_message,
                headers=headers
            )
        
        return False
    
    def __call__(self, func):
        """Decorator to apply rate limiting to a route."""
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            try:
                await self.is_rate_limited(request)
                return await func(request, *args, **kwargs)
            except RateLimitExceeded as e:
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={"detail": str(e)},
                    headers=e.headers
                )
        
        return wrapper


# Default rate limiter instance
default_rate_limiter = RateLimiter(
    limit=100,  # 100 requests
    window=60,  # per minute
    key_prefix="api_rate_limit"
)

# Strict rate limiter for sensitive endpoints
strict_rate_limiter = RateLimiter(
    limit=10,  # 10 requests
    window=60,  # per minute
    key_prefix="strict_rate_limit"
)

# Authentication rate limiter
auth_rate_limiter = RateLimiter(
    limit=5,  # 5 requests
    window=300,  # per 5 minutes
    key_prefix="auth_rate_limit",
    error_message="Too many login attempts. Please try again later."
)


def rate_limit(
    limit: int = 100,
    window: int = 60,
    key_prefix: str = "custom_rate_limit",
    identifier: Optional[Callable[[Request], str]] = None,
    error_message: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
):
    """Decorator factory for custom rate limiting."""
    def decorator(func):
        limiter = RateLimiter(
            limit=limit,
            window=window,
            key_prefix=key_prefix,
            identifier=identifier,
            error_message=error_message,
            headers=headers,
        )
        return limiter(func)
    return decorator
