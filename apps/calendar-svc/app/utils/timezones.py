from datetime import datetime, timedelta, timezone
from typing import Optional, Union, List, Dict, Any
import pytz
from dateutil import parser, tz
from pydantic import BaseModel

# Common timezones for easy reference
COMMON_TIMEZONES = {
    'UTC': 'UTC',
    'US/Eastern': 'America/New_York',
    'US/Central': 'America/Chicago',
    'US/Mountain': 'America/Denver',
    'US/Pacific': 'America/Los_Angeles',
    'Europe/London': 'Europe/London',
    'Europe/Paris': 'Europe/Paris',
    'Asia/Tokyo': 'Asia/Tokyo',
    'Australia/Sydney': 'Australia/Sydney',
}

class TimeRange(BaseModel):
    """A time range with start and end datetimes."""
    start: datetime
    end: datetime
    
    @property
    def duration(self) -> timedelta:
        """Get the duration of the time range."""
        return self.end - self.start
    
    def overlaps(self, other: 'TimeRange') -> bool:
        """Check if this time range overlaps with another."""
        return self.start < other.end and self.end > other.start
    
    def contains(self, dt: datetime) -> bool:
        """Check if a datetime is within this time range."""
        return self.start <= dt <= self.end


def now(tzinfo: Optional[timezone] = None) -> datetime:
    """Get the current datetime, optionally in the specified timezone."""
    dt = datetime.now(timezone.utc)
    if tzinfo is not None:
        dt = dt.astimezone(tzinfo)
    return dt


def parse_datetime(
    dt: Union[str, datetime, int, float],
    tzinfo: Optional[timezone] = None
) -> datetime:
    """Parse a datetime from various input types."""
    if isinstance(dt, datetime):
        parsed = dt
    elif isinstance(dt, (int, float)):
        # Assume it's a timestamp
        parsed = datetime.fromtimestamp(dt, timezone.utc)
    else:
        try:
            parsed = parser.parse(str(dt))
        except (ValueError, TypeError) as e:
            raise ValueError(f"Could not parse datetime from {dt}: {e}")
    
    # Ensure timezone awareness
    if parsed.tzinfo is None:
        parsed = parsed.replace(tzinfo=timezone.utc)
    
    # Convert to target timezone if specified
    if tzinfo is not None:
        parsed = parsed.astimezone(tzinfo)
    
    return parsed


def format_datetime(
    dt: datetime,
    format_str: str = "%Y-%m-%d %H:%M:%S %Z",
    tzinfo: Optional[timezone] = None
) -> str:
    """Format a datetime as a string, optionally converting timezones."""
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    if tzinfo is not None:
        dt = dt.astimezone(tzinfo)
    
    return dt.strftime(format_str)


def get_timezone_offset(tz_name: str) -> str:
    """Get the current UTC offset for a timezone (e.g., '+02:00')."""
    try:
        tz = pytz.timezone(tz_name)
        now = datetime.now(tz)
        offset = now.strftime('%z')
        return f"{offset[:3]}:{offset[3:5]}"  # Format as +HH:MM
    except pytz.exceptions.UnknownTimeZoneError:
        return "+00:00"


def get_available_timezones() -> List[Dict[str, str]]:
    """Get a list of available timezones with their current offsets."""
    timezones = []
    now = datetime.now(timezone.utc)
    
    for tz_name in pytz.common_timezones:
        try:
            tz = pytz.timezone(tz_name)
            offset = now.astimezone(tz).strftime('%z')
            offset_str = f"(UTC{offset[:3]}:{offset[3:5]})"
            
            # Get the current time in this timezone for display
            current_time = now.astimezone(tz).strftime('%H:%M')
            
            timezones.append({
                'name': tz_name,
                'offset': offset_str,
                'current_time': current_time,
                'display': f"{tz_name} {offset_str} {current_time}"
            })
        except Exception:
            continue
    
    # Sort by UTC offset
    timezones.sort(key=lambda x: x['offset'])
    return timezones


def get_working_hours(
    tz_name: str = 'UTC',
    work_start: str = '09:00',
    work_end: str = '17:00',
    work_days: List[int] = None
) -> List[Dict[str, Any]]:
    """
    Generate working hours for a given timezone and work schedule.
    
    Args:
        tz_name: Timezone name (e.g., 'America/New_York')
        work_start: Start time in 'HH:MM' format
        work_end: End time in 'HH:MM' format
        work_days: List of work days (0=Monday, 6=Sunday), defaults to weekdays
        
    Returns:
        List of working hour ranges for each work day
    """
    if work_days is None:
        work_days = [0, 1, 2, 3, 4]  # Monday to Friday by default
    
    tz = pytz.timezone(tz_name)
    now = datetime.now(tz)
    
    working_hours = []
    
    for day in work_days:
        # Get the next occurrence of this work day
        days_ahead = (day - now.weekday()) % 7
        if days_ahead == 0 and now.time() > datetime.strptime(work_end, '%H:%M').time():
            days_ahead = 7  # Move to next week if work hours have passed today
        
        target_date = (now + timedelta(days=days_ahead)).date()
        
        # Create datetime objects for start and end
        start_dt = tz.localize(
            datetime.combine(target_date, datetime.strptime(work_start, '%H:%M').time())
        )
        end_dt = tz.localize(
            datetime.combine(target_date, datetime.strptime(work_end, '%H:%M').time())
        )
        
        working_hours.append({
            'day_of_week': day,
            'day_name': target_date.strftime('%A'),
            'date': target_date,
            'start': start_dt,
            'end': end_dt,
            'timezone': tz_name,
            'is_today': days_ahead == 0
        })
    
    return working_hours


def get_available_slots(
    busy_slots: List[Dict[str, datetime]],
    start_time: datetime,
    end_time: datetime,
    duration: timedelta,
    buffer: timedelta = None
) -> List[Dict[str, datetime]]:
    """
    Find available time slots between busy periods.
    
    Args:
        busy_slots: List of busy time ranges with 'start' and 'end' datetimes
        start_time: Start of the time range to check
        end_time: End of the time range to check
        duration: Minimum duration of available slots to find
        buffer: Optional buffer time to add around busy slots
        
    Returns:
        List of available time slots
    """
    if buffer is None:
        buffer = timedelta(minutes=0)
    
    # Convert all datetimes to timezone-aware if they aren't already
    start_time = parse_datetime(start_time)
    end_time = parse_datetime(end_time)
    
    # Ensure busy slots are sorted by start time
    busy_slots = sorted(busy_slots, key=lambda x: x['start'])
    
    available_slots = []
    previous_end = start_time
    
    for busy in busy_slots:
        busy_start = parse_datetime(busy['start'])
        busy_end = parse_datetime(busy['end'])
        
        # Skip busy slots outside our time range
        if busy_end <= start_time or busy_start >= end_time:
            continue
        
        # Adjust busy times with buffer
        busy_start = busy_start - buffer
        busy_end = busy_end + buffer
        
        # Clip to our time range
        busy_start = max(busy_start, start_time)
        busy_end = min(busy_end, end_time)
        
        # Add available slot before this busy period if there's enough time
        if busy_start > previous_end and (busy_start - previous_end) >= duration:
            available_slots.append({
                'start': previous_end,
                'end': busy_start
            })
        
        previous_end = max(previous_end, busy_end)
    
    # Add remaining time after last busy slot
    if end_time > previous_end and (end_time - previous_end) >= duration:
        available_slots.append({
            'start': previous_end,
            'end': end_time
        })
    
    return available_slots
