import os
import uuid
import mimetypes
from pathlib import Path
from typing import I<PERSON>, Any, Dict, List, Optional, Tuple, Union
from urllib.parse import urlparse

from fastapi import UploadFile, HTTPException, status
from pydantic import BaseModel, HttpUrl, validator

from ...config import settings

# Configure allowed file types
ALLOWED_IMAGE_TYPES = {
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
}

ALLOWED_DOCUMENT_TYPES = {
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
}

# Maximum file size in bytes (default: 10MB)
MAX_FILE_SIZE = 10 * 1024 * 1024


class FileInfo(BaseModel):
    """Information about an uploaded file."""
    filename: str
    content_type: str
    size: int
    path: Optional[str] = None
    url: Optional[str] = None
    metadata: Dict[str, Any] = {}
    
    @property
    def extension(self) -> str:
        """Get the file extension."""
        return Path(self.filename).suffix.lower()
    
    @property
    def is_image(self) -> bool:
        """Check if the file is an image."""
        return self.content_type.startswith('image/')
    
    @property
    def is_document(self) -> bool:
        """Check if the file is a document."""
        return self.content_type in ALLOWED_DOCUMENT_TYPES


class StorageBackend:
    """Base class for storage backends."""
    
    async def save(self, file: Union[UploadFile, IO[bytes]], path: str) -> FileInfo:
        """Save a file to storage."""
        raise NotImplementedError
    
    async def get(self, path: str) -> bytes:
        """Retrieve a file from storage."""
        raise NotImplementedError
    
    async def delete(self, path: str) -> bool:
        """Delete a file from storage."""
        raise NotImplementedError
    
    def get_url(self, path: str) -> str:
        """Get a public URL for a file."""
        raise NotImplementedError


class LocalStorageBackend(StorageBackend):
    """Local filesystem storage backend."""
    
    def __init__(self, root_dir: str, base_url: str = '/'):
        self.root_dir = Path(root_dir).resolve()
        self.base_url = base_url.rstrip('/') + '/'
        
        # Create root directory if it doesn't exist
        self.root_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_path(self, path: str) -> Path:
        """Get the full local path for a file."""
        # Prevent directory traversal
        full_path = (self.root_dir / path).resolve()
        if not str(full_path).startswith(str(self.root_dir)):
            raise ValueError("Invalid file path")
        return full_path
    
    async def save(self, file: Union[UploadFile, IO[bytes]], path: str) -> FileInfo:
        """Save a file to the local filesystem."""
        full_path = self._get_path(path)
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        if isinstance(file, UploadFile):
            content = await file.read()
            content_type = file.content_type or 'application/octet-stream'
            filename = file.filename or str(uuid.uuid4())
            size = len(content)
        else:
            content = file.read()
            content_type = mimetypes.guess_type(path)[0] or 'application/octet-stream'
            filename = Path(path).name
            size = len(content)
        
        # Write file
        with open(full_path, 'wb') as f:
            f.write(content)
        
        return FileInfo(
            filename=filename,
            content_type=content_type,
            size=size,
            path=path,
            url=self.get_url(path)
        )
    
    async def get(self, path: str) -> bytes:
        """Retrieve a file from the local filesystem."""
        full_path = self._get_path(path)
        if not full_path.exists():
            raise FileNotFoundError(f"File not found: {path}")
        
        with open(full_path, 'rb') as f:
            return f.read()
    
    async def delete(self, path: str) -> bool:
        """Delete a file from the local filesystem."""
        full_path = self._get_path(path)
        if not full_path.exists():
            return False
        
        full_path.unlink()
        return True
    
    def get_url(self, path: str) -> str:
        """Get a public URL for a file."""
        return f"{self.base_url}{path.lstrip('/')}"


class S3StorageBackend(StorageBackend):
    """Amazon S3 storage backend."""
    
    def __init__(self, bucket_name: str, **kwargs):
        try:
            import boto3
            from botocore.config import Config
        except ImportError:
            raise ImportError("boto3 is required for S3 storage")
        
        self.bucket_name = bucket_name
        self.client = boto3.client('s3', **kwargs)
    
    async def save(self, file: Union[UploadFile, IO[bytes]], path: str) -> FileInfo:
        """Upload a file to S3."""
        if isinstance(file, UploadFile):
            content = await file.read()
            content_type = file.content_type or 'application/octet-stream'
            filename = file.filename or str(uuid.uuid4())
            size = len(content)
        else:
            content = file.read()
            content_type = mimetypes.guess_type(path)[0] or 'application/octet-stream'
            filename = Path(path).name
            size = len(content)
        
        # Upload to S3
        self.client.put_object(
            Bucket=self.bucket_name,
            Key=path,
            Body=content,
            ContentType=content_type,
            ACL='public-read',
        )
        
        return FileInfo(
            filename=filename,
            content_type=content_type,
            size=size,
            path=path,
            url=self.get_url(path)
        )
    
    async def get(self, path: str) -> bytes:
        """Retrieve a file from S3."""
        try:
            response = self.client.get_object(Bucket=self.bucket_name, Key=path)
            return response['Body'].read()
        except self.client.exceptions.NoSuchKey:
            raise FileNotFoundError(f"File not found: {path}")
    
    async def delete(self, path: str) -> bool:
        """Delete a file from S3."""
        try:
            self.client.delete_object(Bucket=self.bucket_name, Key=path)
            return True
        except Exception as e:
            print(f"Error deleting file from S3: {e}")
            return False
    
    def get_url(self, path: str) -> str:
        """Get a public URL for a file."""
        return f"https://{self.bucket_name}.s3.amazonaws.com/{path}"


class Storage:
    """Unified storage interface that can use different backends."""
    
    def __init__(self, backend: str = 'local', **kwargs):
        """Initialize the storage with the specified backend."""
        if backend == 'local':
            self.backend = LocalStorageBackend(
                root_dir=kwargs.get('root_dir', './uploads'),
                base_url=kwargs.get('base_url', '/uploads/')
            )
        elif backend == 's3':
            self.backend = S3StorageBackend(
                bucket_name=kwargs['bucket_name'],
                aws_access_key_id=kwargs.get('aws_access_key_id'),
                aws_secret_access_key=kwargs.get('aws_secret_access_key'),
                region_name=kwargs.get('region_name', 'us-east-1')
            )
        else:
            raise ValueError(f"Unsupported storage backend: {backend}")
    
    async def save_upload_file(
        self,
        file: UploadFile,
        directory: str = '',
        allowed_types: Optional[set] = None,
        max_size: int = MAX_FILE_SIZE
    ) -> FileInfo:
        """
        Save an uploaded file with validation.
        
        Args:
            file: The uploaded file
            directory: Directory to save the file in (within the storage root)
            allowed_types: Set of allowed MIME types (defaults to all allowed types)
            max_size: Maximum file size in bytes
            
        Returns:
            FileInfo with details about the saved file
            
        Raises:
            HTTPException: If the file is invalid
        """
        # Validate file size
        content = await file.read()
        await file.seek(0)  # Reset file pointer
        
        if len(content) > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large. Max size is {max_size / (1024 * 1024):.1f}MB"
            )
        
        # Validate file type
        content_type = file.content_type or 'application/octet-stream'
        if allowed_types is not None and content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {content_type} is not allowed"
            )
        
        # Generate a unique filename
        ext = Path(file.filename).suffix if file.filename else ''
        filename = f"{uuid.uuid4()}{ext}"
        path = str(Path(directory) / filename) if directory else filename
        
        # Save the file
        return await self.backend.save(file, path)
    
    async def delete_file(self, path: str) -> bool:
        """Delete a file from storage."""
        return await self.backend.delete(path)
    
    def get_file_url(self, path: str) -> str:
        """Get a public URL for a file."""
        return self.backend.get_url(path)
    
    async def save_image(
        self,
        file: UploadFile,
        directory: str = 'images',
        max_size: int = 5 * 1024 * 1024  # 5MB
    ) -> FileInfo:
        """Save an uploaded image with validation."""
        return await self.save_upload_file(
            file=file,
            directory=directory,
            allowed_types=ALLOWED_IMAGE_TYPES,
            max_size=max_size
        )
    
    async def save_document(
        self,
        file: UploadFile,
        directory: str = 'documents',
        max_size: int = 10 * 1024 * 1024  # 10MB
    ) -> FileInfo:
        """Save an uploaded document with validation."""
        return await self.save_upload_file(
            file=file,
            directory=directory,
            allowed_types=ALLOWED_DOCUMENT_TYPES,
            max_size=max_size
        )


# Initialize storage based on settings
def get_storage() -> Storage:
    """Get a storage instance based on settings."""
    if settings.STORAGE_BACKEND == 's3':
        return Storage(
            backend='s3',
            bucket_name=settings.S3_BUCKET_NAME,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
    else:
        return Storage(
            backend='local',
            root_dir=settings.UPLOAD_DIR,
            base_url=settings.MEDIA_URL
        )


# Global storage instance
storage = get_storage()
