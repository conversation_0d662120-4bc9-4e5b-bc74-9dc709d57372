import logging
import random
import string
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from uuid import UUID, uuid4

from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

# Type variables for generic functions
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


def generate_random_string(length: int = 8) -> str:
    """Generate a random string of fixed length."""
    letters = string.ascii_letters + string.digits
    return ''.join(random.choice(letters) for _ in range(length))


def generate_random_number(length: int = 6) -> str:
    """Generate a random numeric string of fixed length."""
    return ''.join(random.choice(string.digits) for _ in range(length))


def generate_uuid() -> str:
    """Generate a UUID4 string."""
    return str(uuid4())


def format_duration(seconds: int) -> str:
    """Format duration in seconds to a human-readable string."""
    minutes, seconds = divmod(seconds, 60)
    hours, minutes = divmod(minutes, 60)
    days, hours = divmod(hours, 24)
    
    parts = []
    if days > 0:
        parts.append(f"{days}d")
    if hours > 0:
        parts.append(f"{hours}h")
    if minutes > 0 and days == 0:  # Only show minutes if less than a day
        parts.append(f"{minutes}m")
    if seconds > 0 and hours == 0 and days == 0:  # Only show seconds if less than an hour
        parts.append(f"{seconds}s")
    
    return " ".join(parts) if parts else "0s"


def get_time_until(target_time: datetime) -> str:
    """Get a human-readable string representing time until target time."""
    now = datetime.utcnow()
    if target_time <= now:
        return "now"
    
    delta = target_time - now
    return format_duration(int(delta.total_seconds()))


async def get_or_create(
    db: AsyncSession,
    model: Type[ModelType],
    defaults: Optional[Dict[str, Any]] = None,
    **kwargs
) -> tuple[ModelType, bool]:
    """Get or create a database record."""
    query = db.query(model).filter_by(**kwargs)
    instance = await query.first()
    
    if instance:
        return instance, False
    
    # Create new instance
    params = {**kwargs, **(defaults or {})}
    instance = model(**params)
    
    db.add(instance)
    await db.commit()
    await db.refresh(instance)
    
    return instance, True


def exclude_none_values(data: Dict[str, Any]) -> Dict[str, Any]:
    """Exclude None values from a dictionary."""
    return {k: v for k, v in data.items() if v is not None}


def safe_getattr(obj: Any, attr: str, default: Any = None) -> Any:
    """Safely get an attribute from an object with a default value."""
    try:
        return getattr(obj, attr, default)
    except Exception:
        return default


def parse_bool(value: Any) -> bool:
    """Parse a value to boolean."""
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ('true', '1', 't', 'y', 'yes')
    return bool(value)


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split a list into chunks of the specified size."""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def deep_update(target: Dict[str, Any], source: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively update a dictionary."""
    for key, value in source.items():
        if isinstance(value, dict) and key in target and isinstance(target[key], dict):
            target[key] = deep_update(target[key], value)
        else:
            target[key] = value
    return target


class ExpiringCache:
    """A simple in-memory cache with TTL (time-to-live) support."""
    
    def __init__(self):
        self._cache = {}
    
    def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """Set a value in the cache with an optional TTL in seconds."""
        self._cache[key] = {
            'value': value,
            'expires_at': datetime.utcnow() + timedelta(seconds=ttl)
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a value from the cache if it exists and hasn't expired."""
        if key not in self._cache:
            return default
        
        item = self._cache[key]
        if datetime.utcnow() > item['expires_at']:
            del self._cache[key]
            return default
        
        return item['value']
    
    def delete(self, key: str) -> None:
        """Delete a key from the cache."""
        if key in self._cache:
            del self._cache[key]
    
    def clear(self) -> None:
        """Clear all items from the cache."""
        self._cache.clear()
    
    def cleanup(self) -> None:
        """Remove all expired items from the cache."""
        now = datetime.utcnow()
        expired_keys = [
            key for key, item in self._cache.items()
            if now > item['expires_at']
        ]
        for key in expired_keys:
            del self._cache[key]
