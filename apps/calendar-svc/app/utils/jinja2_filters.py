from datetime import datetime
from typing import Any, Optional

def datetimeformat(value: Any, format: str = "%Y-%m-%d %H:%M %Z") -> str:
    """Format a datetime object or ISO format string."""
    if value is None:
        return ""
    
    if not isinstance(value, datetime):
        try:
            value = datetime.fromisoformat(str(value))
        except (TypeError, ValueError):
            return str(value)
    
    return value.strftime(format)

def dateformat(value: Any, format: str = "%Y-%m-%d") -> str:
    """Format a date object or ISO format string."""
    return datetimeformat(value, format=format)

def timeformat(value: Any, format: str = "%H:%M") -> str:
    """Format a time object or ISO format string."""
    return datetimeformat(value, format=format)

def format_currency(amount: float, currency: str = "USD") -> str:
    """Format a number as currency."""
    try:
        amount = float(amount)
    except (TypeError, ValueError):
        return str(amount)
    
    if currency.upper() == "USD":
        return f"${amount:,.2f}"
    elif currency.upper() == "EUR":
        return f"€{amount:,.2f}"
    elif currency.upper() == "GBP":
        return f"£{amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency.upper()}"

def register_filters(env):
    """Register all custom Jinja2 filters."""
    env.filters["datetimeformat"] = datetimeformat
    env.filters["dateformat"] = dateformat
    env.filters["timeformat"] = timeformat
    env.filters["currency"] = format_currency
    return env
