{% extends "emails/base.html" %}

{% block title %}Upcoming Appointment - {{ project_name }}{% endblock %}

{% block content %}
    <h2>Appointment Reminder</h2>
    
    <p>Hello {{ username }},</p>
    
    <p>This is a friendly reminder about your upcoming appointment:</p>
    
    <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin: 15px 0;">
        <h3 style="margin-top: 0;">{{ appointment.title }}</h3>
        
        <p><strong>When:</strong> {{ appointment.start_time|datetimeformat }} - {{ appointment.end_time|datetimeformat }}</p>
        <p><strong>Time until appointment:</strong> {{ appointment.time_until }}</p>
        
        {% if appointment.location %}
        <p><strong>Where:</strong> {{ appointment.location }}</p>
        {% endif %}
        
        {% if appointment.description %}
        <p><strong>Description:</strong> {{ appointment.description }}</p>
        {% endif %}
    </div>
    
    <p>You can view or modify this appointment by clicking the button below:</p>
    
    <p class="text-center">
        <a href="{{ appointment_url }}" class="button">View Appointment</a>
    </p>
    
    <p>If you need to reschedule or cancel, please do so at least 24 hours in advance.</p>
    
    <p>Best regards,<br>The {{ project_name }} Team</p>
    
    <div class="pt-3" style="border-top: 1px solid #eeeeee; margin-top: 20px; padding-top: 15px;">
        <p class="text-muted">This is an automated reminder. Please do not reply to this email.</p>
    </div>
{% endblock %}
