import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

from fastapi import Fast<PERSON><PERSON>, Depends, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.exceptions import RequestValidationError
from starlette.middleware.sessions import SessionMiddleware
from sqlalchemy.orm import Session

from .config import settings
from .db.base import Base, engine, SessionLocal
from .api.v1.api import api_router
from ..routers.calendly_webhooks import router as calendly_webhook_router
from .core.security import get_password_hash
from .core.logging import setup_logging

# Set up logging
setup_logging()
logger = logging.getLogger(__name__)

# Create database tables
Base.metadata.create_all(bind=engine)

def create_app() -> FastAPI:
    """Create and configure the FastAPI application.
    
    This function is used for testing to create a fresh app instance.
    
    Returns:
        FastAPI: Configured FastAPI application
    """
    # Initialize FastAPI app with default values for missing settings
    fastapi_app = FastAPI(
        title=getattr(settings, 'PROJECT_NAME', 'AI Lex Receptionist - Calendar Service'),
        description="AI Lex Receptionist - Calendar Service",
        version=getattr(settings, 'VERSION', '0.1.0'),
        openapi_url=f"{getattr(settings, 'API_PREFIX', '/api/v1')}/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc",
    )
    
    # Set up CORS with default values for testing
    # Get CORS origins with default of ['*'] if not defined
    cors_origins = getattr(settings, 'BACKEND_CORS_ORIGINS', ['*'])
    fastapi_app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in cors_origins],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add session middleware with default values for testing
    fastapi_app.add_middleware(
        SessionMiddleware,
        secret_key=getattr(settings, 'SECRET_KEY', 'test_secret_key_for_session'),
        session_cookie=getattr(settings, 'SESSION_COOKIE_NAME', 'calendar_session'),
    )

    # Include API router with default API prefix path
    api_prefix = getattr(settings, 'API_PREFIX', '/api/v1')
    fastapi_app.include_router(api_router, prefix=api_prefix)
    fastapi_app.include_router(calendly_webhook_router)
    
    # Health check endpoint
    @fastapi_app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "ok",
            "timestamp": datetime.utcnow().isoformat(),
            "service": getattr(settings, 'PROJECT_NAME', 'AI Lex Receptionist - Calendar Service'),
            "version": getattr(settings, 'VERSION', '0.1.0')
        }
        
    # Root endpoint
    @fastapi_app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": f"Welcome to {getattr(settings, 'PROJECT_NAME', 'AI Lex Receptionist - Calendar Service')} API",
            "version": getattr(settings, 'VERSION', '0.1.0'),
            "docs": "/docs",
            "redoc": "/redoc"
        }
        
    return fastapi_app

# Create the main app instance
app = create_app()

# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors."""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": exc.errors()},
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Handle all other exceptions."""
    logger.exception("Unhandled exception")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "Internal server error"},
    )

# Database dependency
def get_db():
    """Dependency to get a database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create initial data
@app.on_event("startup")
async def startup_event():
    """Run startup events."""
    # Create initial admin user if it doesn't exist
    db = SessionLocal()
    try:
        from .models.user import User
        from .core.security import get_password_hash
        
        admin = db.query(User).filter(User.email == settings.FIRST_SUPERUSER).first()
        if not admin:
            admin = User(
                email=settings.FIRST_SUPERUSER,
                hashed_password=get_password_hash(settings.FIRST_SUPERUSER_PASSWORD),
                is_superuser=True,
                is_active=True,
                full_name="Admin",
            )
            db.add(admin)
            db.commit()
            logger.info("Created initial admin user")
    except Exception as e:
        logger.error(f"Error creating initial admin user: {e}")
        db.rollback()
    finally:
        db.close()
    
    logger.info("Application startup complete")
