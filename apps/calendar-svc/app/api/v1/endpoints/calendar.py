from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.responses import RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession

from ....config import settings
from ....db.base import get_async_db
from ....repositories.calendar_repository import CalendarRepository
from ....schemas.calendar import (
    CalendarConnectionCreate,
    CalendarConnectionResponse,
    Calendar,
    AvailabilityRequest,
    FreeBusyResponse,
    AppointmentCreate,
    AppointmentResponse,
    AppointmentUpdate,
    OAuthState,
    OAuthTokenResponse,
    AppointmentStatus,
)
from ....services.oauth_service import OAuthService
from ....services.calendly_service import CalendlyToken, calendly_service
from ...deps import get_current_user, get_repository, rate_limit

router = APIRouter()

# Initialize OAuth services
oauth_services = {
    "google": OAuthService(
        client_id=settings.GOOGLE_CLIENT_ID,
        client_secret=settings.GOOGLE_CLIENT_SECRET,
        redirect_uri=settings.GOOGLE_REDIRECT_URI
    ),
    "calendly": calendly_service
}

# Calendar Connection Endpoints

@router.post("/connections/", response_model=CalendarConnectionResponse)
async def create_calendar_connection(
    connection: CalendarConnectionCreate,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    Create a new calendar connection
    """
    # Set the user ID from the current user
    connection.user_id = current_user.id
    
    # Create the connection
    db_connection = await repo.create_connection(connection)
    return db_connection

@router.get("/connections/", response_model=List[CalendarConnectionResponse])
async def list_calendar_connections(
    skip: int = 0,
    limit: int = 100,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    List all calendar connections for the current user
    """
    # In a real implementation, you would filter by current_user.id
    connections = await repo.list_connections(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    return connections

@router.get("/connections/{connection_id}", response_model=CalendarConnectionResponse)
async def get_calendar_connection(
    connection_id: str,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    Get a specific calendar connection by ID
    """
    connection = await repo.get_connection(connection_id)
    if not connection:
        raise HTTPException(status_code=404, detail="Calendar connection not found")
    
    # Verify ownership
    if str(connection.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to access this resource")
    
    return connection

@router.delete("/connections/{connection_id}", status_code=status.HTTP_204_NO_CONTENT, response_model=None)
async def delete_calendar_connection(
    connection_id: str,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> None:
    """
    Delete a calendar connection
    """
    # Verify the connection exists and belongs to the user
    connection = await repo.get_connection(connection_id)
    if not connection:
        raise HTTPException(status_code=404, detail="Calendar connection not found")
    
    if str(connection.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to delete this resource")
    
    await repo.delete_connection(connection_id)
    # For 204 status code, we must return None (no content)
    return None

# Calendar Endpoints

@router.get("/calendars/", response_model=List[Calendar])
async def list_calendars(
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    List all calendars for the current user
    """
    # In a real implementation, you would fetch from the calendar provider
    # This is a mock implementation
    return [
        Calendar(
            id="primary",
            name=f"{current_user.email}",
            description="Primary calendar",
            timezone="UTC",
            primary=True,
            selected=True
        )
    ]

# Availability Endpoints

@router.post("/availability/", response_model=List[FreeBusyResponse])
async def get_availability(
    request: AvailabilityRequest,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    Get availability for the specified calendars
    """
    # In a real implementation, you would check the calendar provider's free/busy API
    # This is a mock implementation
    return [
        FreeBusyResponse(
            calendar_id=calendar_id,
            busy=[]  # No busy times in this mock
        )
        for calendar_id in request.calendar_ids
    ]

# Appointment Endpoints

@router.post("/appointments/", response_model=AppointmentResponse, status_code=status.HTTP_201_CREATED)
async def create_appointment(
    appointment: AppointmentCreate,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    Create a new appointment
    """
    # In a real implementation, you would create the event in the calendar provider
    # and then store the appointment in your database
    db_appointment = await repo.create_appointment(appointment.dict())
    return db_appointment

@router.get("/appointments/", response_model=List[AppointmentResponse])
async def list_appointments(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    status: Optional[AppointmentStatus] = None,
    skip: int = 0,
    limit: int = 100,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    List appointments for the current user
    """
    appointments = await repo.list_appointments(
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        status=status,
        skip=skip,
        limit=limit
    )
    return appointments

@router.get("/appointments/{appointment_id}", response_model=AppointmentResponse)
async def get_appointment(
    appointment_id: str,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    Get a specific appointment by ID
    """
    appointment = await repo.get_appointment(appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found")
    
    # Verify ownership
    connection = await repo.get_connection(appointment.calendar_connection_id)
    if not connection or str(connection.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to access this resource")
    
    return appointment

@router.patch("/appointments/{appointment_id}", response_model=AppointmentResponse)
async def update_appointment(
    appointment_id: str,
    appointment_update: AppointmentUpdate,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    Update an appointment
    """
    # Get the existing appointment
    appointment = await repo.get_appointment(appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found")
    
    # Verify ownership
    connection = await repo.get_connection(appointment.calendar_connection_id)
    if not connection or str(connection.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to update this resource")
    
    # Update the appointment
    updated_appointment = await repo.update_appointment(
        appointment_id=appointment_id,
        appointment_update=appointment_update.dict(exclude_unset=True)
    )
    
    return updated_appointment

@router.delete("/appointments/{appointment_id}", status_code=status.HTTP_204_NO_CONTENT, response_model=None)
async def delete_appointment(
    appointment_id: str,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
    current_user: dict = Depends(get_current_user),
) -> None:
    """
    Delete an appointment
    """
    # Get the existing appointment
    appointment = await repo.get_appointment(appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found")
    
    # Verify ownership
    connection = await repo.get_connection(appointment.calendar_connection_id)
    if not connection or str(connection.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to delete this resource")
    
    await repo.delete_appointment(appointment_id)
    # For 204 status code, we must return None (no content)
    return None

# OAuth Endpoints

@router.get("/oauth/{provider}/authorize")
async def oauth_authorize(
    provider: str,
    request: Request,
    redirect_uri: Optional[str] = None,
    state: Optional[str] = None,
    current_user: dict = Depends(get_current_user),
) -> Any:
    """
    Initiate OAuth flow with the specified provider
    """
    # Get the OAuth service for the provider
    service = oauth_services.get(provider)
    if not service:
        raise HTTPException(status_code=400, detail="Unsupported OAuth provider")
    
    # Generate state if not provided
    state_obj = OAuthState(
        user_id=current_user.id,
        redirect_uri=redirect_uri,
        timestamp=datetime.utcnow().isoformat()
    )
    
    # Get the authorization URL
    if provider == "calendly":
        auth_url = service.get_authorization_url(state=state_obj.json())
    else:
        auth_url = service.get_authorization_url(state=state_obj.json())
    
    # Redirect to the provider's authorization page
    return RedirectResponse(url=auth_url)

@router.get("/oauth/{provider}/callback", response_model=OAuthTokenResponse)
async def oauth_callback(
    provider: str,
    code: str,
    state: str,
    error: Optional[str] = None,
    error_description: Optional[str] = None,
    repo: CalendarRepository = Depends(get_repository(CalendarRepository)),
) -> Any:
    """
    OAuth callback endpoint
    """
    if error:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth error: {error_description or error}"
        )
    
    # Get the OAuth service for the provider
    service = oauth_services.get(provider)
    if not service:
        raise HTTPException(status_code=400, detail="Unsupported OAuth provider")
    
    try:
        # Parse the state
        state_obj = OAuthState.parse_raw(state)
        
        # Exchange the authorization code for tokens
        if provider == "google":
            credentials = service.get_credentials_from_code(code)
            
            # Save the connection
            connection = CalendarConnectionCreate(
                user_id=state_obj.user_id,
                provider=provider,
                access_token=credentials.token,
                refresh_token=credentials.refresh_token,
                token_uri=credentials.token_uri,
                client_id=credentials.client_id,
                client_secret=credentials.client_secret,
                scopes=credentials.scopes,
                token_expiry=credentials.expiry.isoformat() if credentials.expiry else None,
            )
            
            db_connection = await repo.create_connection(connection)
            
            # Create the response
            response = OAuthTokenResponse(
                access_token=credentials.token,
                token_type="Bearer",
                expires_in=int((credentials.expiry - datetime.utcnow()).total_seconds()) if credentials.expiry else 3600,
                refresh_token=credentials.refresh_token,
                provider=provider,
                connection_id=str(db_connection.id)
            )
            
        elif provider == "calendly":
            # Get tokens from Calendly
            token = await service.get_tokens(code)
            
            # Save the connection
            connection = CalendarConnectionCreate(
                user_id=state_obj.user_id,
                provider=provider,
                access_token=token.access_token,
                refresh_token=token.refresh_token,
                token_uri=service.TOKEN_URL,
                client_id=service.client_id,
                scopes=token.scope.split(),
                token_expiry=token.expires_at.isoformat(),
            )
            
            db_connection = await repo.create_connection(connection)
            
            # Create the response
            response = OAuthTokenResponse(
                access_token=token.access_token,
                token_type=token.token_type,
                expires_in=int((token.expires_at - datetime.utcnow()).total_seconds()),
                refresh_token=token.refresh_token,
                provider=provider,
                connection_id=str(db_connection.id)
            )
        
        # Redirect if specified in state
        if state_obj.redirect_uri:
            # Add the token to the redirect URL
            redirect_uri = f"{state_obj.redirect_uri}?{response.dict(exclude_none=True).urlencode()}"
            return RedirectResponse(url=redirect_uri)
            if response.id_token:
                params["id_token"] = response.id_token
            
            redirect_url = f"{state_obj.redirect_uri}?{urlencode(params)}"
            return RedirectResponse(url=redirect_url)
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing OAuth callback: {str(e)}")
