from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Optional

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status, Request, Response
from fastapi.security import OAuth2PasswordRequestForm
from jose import jwt
from pydantic import EmailStr
from sqlalchemy.ext.asyncio import AsyncSession

from ....core.security import (
    create_access_token,
    get_password_hash,
    verify_password,
    generate_password_reset_token,
    verify_password_reset_token,
    generate_email_verification_token,
    verify_email_verification_token,
)
from ....core.config import settings
from ....db.base import get_async_db
from ....models.user import User
from ....schemas.base import Response
from ....schemas.user import (
    UserCreate,
    UserResponse,
    Token,
    Msg,
    PasswordResetRequest,
    PasswordResetConfirm,
    EmailVerificationRequest,
)
from ....services.email import send_reset_password_email, send_new_account_email
from ....utils import generate_password_reset_html, generate_new_account_email
from ...deps import get_current_user

router = APIRouter()

@router.post("/login/access-token", response_model=Token)
async def login_access_token(
    response: Response,
    db: AsyncSession = Depends(get_async_db),
    form_data: OAuth2PasswordRequestForm = Depends(),
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    # Find user by email (username field in OAuth2 form data)
    user = await User.get_by_email(db, email=form_data.username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect email or password",
        )
    
    # Verify password
    if not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect email or password",
        )
    
    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user",
        )
    
    # Update last login
    await user.update_last_login(db)
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=str(user.id), expires_delta=access_token_expires
    )
    
    # Set secure HTTP-only cookie
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        expires=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        secure=not settings.DEBUG,
        samesite="lax",
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": int(access_token_expires.total_seconds()),
    }

@router.post("/login/test-token", response_model=UserResponse)
async def test_token(current_user: User = Depends(get_current_user)) -> Any:
    """
    Test access token
    """
    return current_user

@router.post("/password-recovery/{email}", response_model=Msg)
async def recover_password(email: EmailStr, db: AsyncSession = Depends(get_async_db)) -> Any:
    """
    Password Recovery
    """
    user = await User.get_by_email(db, email=email)
    
    if not user:
        # Don't reveal that the user doesn't exist
        return {"msg": "If this email is registered, you will receive a password reset link."}
    
    password_reset_token = generate_password_reset_token(email=email)
    
    # In a real app, you would send the email with the token
    reset_url = f"{settings.FRONTEND_URL}/reset-password?token={password_reset_token}"
    
    # Send email with password reset link
    await send_reset_password_email(
        email_to=user.email,
        username=user.email,
        reset_url=reset_url,
    )
    
    return {"msg": "Password recovery email sent"}

@router.post("/reset-password/", response_model=Msg)
async def reset_password(
    body: PasswordResetConfirm, db: AsyncSession = Depends(get_async_db)
) -> Any:
    """
    Reset password
    """
    email = verify_password_reset_token(token=body.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token",
        )
    
    user = await User.get_by_email(db, email=email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="The user with this email does not exist in the system.",
        )
    
    # Update password
    hashed_password = get_password_hash(body.new_password)
    user.hashed_password = hashed_password
    await user.save(db)
    
    return {"msg": "Password updated successfully"}

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_in: UserCreate, db: AsyncSession = Depends(get_async_db)
) -> Any:
    """
    Create new user.
    """
    # Check if user with this email already exists
    user = await User.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The user with this email already exists in the system.",
        )
    
    # Create new user
    user_data = user_in.dict(exclude={"password"})
    user_data["hashed_password"] = get_password_hash(user_in.password)
    user = User(**user_data)
    
    # Save user to database
    await user.save(db)
    
    # Send welcome email
    if settings.EMAILS_ENABLED and user_in.email:
        verification_token = generate_email_verification_token(email=user_in.email)
        verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
        
        await send_new_account_email(
            email_to=user_in.email,
            username=user_in.email,
            verification_url=verification_url,
        )
    
    return user

@router.post("/verify-email", response_model=Msg)
async def verify_email(
    body: EmailVerificationRequest, db: AsyncSession = Depends(get_async_db)
) -> Any:
    """
    Verify email
    """
    email = verify_email_verification_token(token=body.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token",
        )
    
    user = await User.get_by_email(db, email=email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    # Mark email as verified
    # In a real app, you would have an email_verified field in your User model
    # user.email_verified = True
    # await user.save(db)
    
    return {"msg": "Email verified successfully"}

@router.post("/logout")
async def logout(response: Response):
    """
    Logout - clears the authentication cookie
    """
    response.delete_cookie("access_token")
    return {"msg": "Successfully logged out"}
