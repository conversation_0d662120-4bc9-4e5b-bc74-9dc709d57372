from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from ....schemas.user import Token, UserResponse
from ...deps import get_current_user

# Create router for auth endpoints
router = APIRouter()

@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    # This is a stub implementation for testing
    return {
        "access_token": "test_access_token",
        "token_type": "bearer",
        "expires_in": 3600
    }

@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user = Depends(get_current_user)):
    """
    Get current user
    """
    return current_user
