from __future__ import annotations

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from packages.calendar_core import get_provider
from packages.shared import models as shared_models

from ...db.base import get_async_db
from ...api.deps import get_current_user
from ...utils.rate_limiter import RateLimiter, RateLimitExceeded
from ...utils.statsd import get_statsd_client
from ...schemas.calendar import (
    AvailabilityRequest,
    FreeBusyResponse,
    TimeRange,
)
from pydantic import BaseModel

router = APIRouter()

# Rate limiter for booking endpoint
booking_limiter = RateLimiter(limit=5, window=60)


async def get_firm_id(current_user=Depends(get_current_user)) -> str:
    # For tests, the user id acts as the firm id
    return current_user.id


def _merge_ranges(ranges: List[dict]) -> List[dict]:
    """Merge overlapping busy ranges."""
    if not ranges:
        return []
    sorted_ranges = sorted(ranges, key=lambda r: r["start"])
    merged = [sorted_ranges[0].copy()]
    for r in sorted_ranges[1:]:
        last = merged[-1]
        if r["start"] <= last["end"]:
            if r["end"] > last["end"]:
                last["end"] = r["end"]
        else:
            merged.append(r.copy())
    return merged


class BookingRequest(BaseModel):
    provider: shared_models.BookingProvider
    calendar_id: str
    summary: str
    start_at: datetime
    end_at: datetime


class BookingResponse(BaseModel):
    id: UUID
    provider: shared_models.BookingProvider
    external_id: Optional[str] = None
    start_at: datetime
    booked_at: datetime
    provider_event_link: Optional[str] = None


@router.post("/availability", response_model=List[FreeBusyResponse])
async def availability(
    req: AvailabilityRequest,
    provider: str,
    firm_id: str = Depends(get_firm_id),
):
    provider_client = get_provider(firm_id, provider)
    raw = await provider_client.get_availability(
        req.calendar_ids, req.time_min, req.time_max
    )
    responses: List[FreeBusyResponse] = []
    for item in raw:
        merged = _merge_ranges(item.get("busy", []))
        responses.append(
            FreeBusyResponse(
                calendar_id=item["calendar_id"],
                busy=[TimeRange(**b) if isinstance(b, dict) else b for b in merged],
            )
        )
    return responses


@router.post("/book", response_model=BookingResponse)
async def book(
    req: BookingRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    firm_id: str = Depends(get_firm_id),
):
    try:
        await booking_limiter.is_rate_limited(request)
    except RateLimitExceeded as e:
        get_statsd_client().incr("booking.rate_limited")
        raise HTTPException(status_code=429, detail="Rate limit exceeded") from e

    provider_client = get_provider(firm_id, req.provider.value)
    event = await provider_client.create_event(
        req.calendar_id,
        {
            "summary": req.summary,
            "start": req.start_at.isoformat(),
            "end": req.end_at.isoformat(),
        },
    )

    booking = shared_models.Booking(
        firm_id=firm_id,
        provider=req.provider,
        external_id=event.get("id"),
        start_at=req.start_at,
        booked_at=datetime.utcnow(),
        provider_event_link=event.get("htmlLink"),
        metadata_=event,
    )

    db.add(booking)
    await db.commit()
    await db.refresh(booking)

    return BookingResponse(
        id=booking.id,
        provider=booking.provider,
        external_id=booking.external_id,
        start_at=booking.start_at,
        booked_at=booking.booked_at,
        provider_event_link=booking.provider_event_link,
    )


@router.get("/events", response_model=List[BookingResponse])
async def events(
    provider: Optional[shared_models.BookingProvider] = None,
    db: AsyncSession = Depends(get_async_db),
    firm_id: str = Depends(get_firm_id),
):
    stmt = select(shared_models.Booking).where(
        shared_models.Booking.firm_id == firm_id
    )
    if provider:
        stmt = stmt.where(shared_models.Booking.provider == provider)

    result = await db.execute(stmt)
    bookings = result.scalars().all()
    return [
        BookingResponse(
            id=b.id,
            provider=b.provider,
            external_id=b.external_id,
            start_at=b.start_at,
            booked_at=b.booked_at,
            provider_event_link=b.provider_event_link,
        )
        for b in bookings
    ]

