import os
from typing import Generator, Optional, Type, TypeVar, Any
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from jose import JWTError, jwt
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession

from ..config import settings
from ..db.base import get_async_db, get_db, AsyncSessionLocal
from ..repositories.calendar_repository import CalendarRepository
from ..schemas.user import TokenData
from ..schemas.user import UserResponse as User

# Type variable for dependency injection
T = TypeVar('T')

# OAuth2 scheme for token authentication
# Use hardcoded URL for testing purposes, or use settings in production
tokenUrl = "/api/v1/auth/token"
try:
    # Try to get from settings if available
    if hasattr(settings, 'API_PREFIX'):
        tokenUrl = f"{settings.API_PREFIX}/auth/token"
    elif os.environ.get('API_PREFIX'):
        tokenUrl = f"{os.environ.get('API_PREFIX')}/auth/token"
except Exception as e:
    # Fallback to default
    pass

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=tokenUrl,
    auto_error=True
)

# Dependency to get a database session
async def get_db_session() -> Generator[AsyncSession, None, None]:
    """Dependency that provides a database session."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

# Dependency to get the current user from the token
async def get_current_user(
    request: Request,
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """Dependency to get the current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            options={"verify_aud": False}
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        token_data = TokenData(user_id=user_id)
    except (JWTError, ValidationError) as e:
        raise credentials_exception from e
        
    # In a real app, you would fetch the user from the database here
    # For now, we'll just return a mock user
    user = User(id=token_data.user_id, email=f"user_{token_data.user_id}@example.com")
    if user is None:
        raise credentials_exception
    
    # Store the user in the request state for use in other dependencies
    request.state.user = user
    return user

# Dependency to get the current active user
async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """Dependency to get the current active user."""
    # Here you could add checks for active status, etc.
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Factory function to create repository dependencies
def get_repository(repo_type: Type[T]) -> Generator[T, None, None]:
    """Dependency to get a repository instance.
    
    Args:
        repo_type: The repository class to instantiate
        
    Returns:
        An instance of the requested repository
    """
    async def _get_repo(
        db: AsyncSession = Depends(get_async_db)
    ) -> Generator[T, None, None]:
        yield repo_type(db)
    
    return _get_repo

# Common query parameters
class CommonQueryParams:
    """Common query parameters for list endpoints."""
    def __init__(
        self,
        skip: int = 0,
        limit: int = 100,
        sort: Optional[str] = None,
        order: str = "asc"
    ) -> None:
        self.skip = skip
        self.limit = limit
        self.sort = sort
        self.order = order

# Rate limiting dependency
async def rate_limit(
    request: Request,
    # Add Redis client here if you're using Redis for rate limiting
) -> None:
    """Dependency for rate limiting requests."""
    # Implement rate limiting logic here
    # For example, using Redis to track request counts per IP
    client_ip = request.client.host
    # Check rate limit and raise 429 if exceeded
    return
