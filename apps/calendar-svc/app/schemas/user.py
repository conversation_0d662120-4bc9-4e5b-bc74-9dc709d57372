from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, EmailStr, Field, validator
from enum import Enum

from .base import Response, ResponseList

# Base schemas
class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = True
    is_superuser: bool = False

# Properties to receive via API on creation
class UserCreate(UserBase):
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=100)
    full_name: str = Field(..., min_length=2, max_length=100)

# Properties to receive via API on update
class UserUpdate(UserBase):
    password: Optional[str] = Field(None, min_length=8, max_length=100)
    
    @validator('password')
    def password_required_if_present(cls, v):
        if v is not None and len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        return v

# Properties shared by models stored in DB
class UserInDBBase(UserBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    class Config:
        orm_mode = True

# Properties to return to client
class UserResponse(UserInDBBase):
    pass

# Properties stored in DB
class UserInDB(UserInDBBase):
    hashed_password: str

# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class TokenPayload(BaseModel):
    sub: Optional[int] = None
    exp: Optional[int] = None
    iat: Optional[int] = None
    nbf: Optional[int] = None

class TokenData(BaseModel):
    """Token data that is encoded in the JWT"""
    user_id: Optional[str] = None
    email: Optional[str] = None
    scopes: List[str] = Field(default_factory=list)

# Message response
class Msg(BaseModel):
    msg: str

# Password reset schemas
class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)

# Email verification
class EmailVerificationRequest(BaseModel):
    token: str

# Response models
class UserResponse(Response[UserInDB]):
    pass

class UserListResponse(ResponseList[UserInDB]):
    pass

# User preferences
class NotificationPreference(BaseModel):
    email: bool = True
    push: bool = True
    sms: bool = False

class UserPreferences(BaseModel):
    notifications: NotificationPreference = Field(
        default_factory=NotificationPreference
    )
    timezone: str = "UTC"
    locale: str = "en-US"
    theme: str = "light"
    
    class Config:
        schema_extra = {
            "example": {
                "notifications": {
                    "email": True,
                    "push": True,
                    "sms": False
                },
                "timezone": "UTC",
                "locale": "en-US",
                "theme": "light"
            }
        }

# User profile
class UserProfileBase(BaseModel):
    phone_number: Optional[str] = None
    timezone: Optional[str] = "UTC"
    avatar_url: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = {}

class UserProfileCreate(UserProfileBase):
    pass

class UserProfileUpdate(UserProfileBase):
    pass

class UserProfileInDB(UserProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class UserProfileResponse(Response[UserProfileInDB]):
    pass

# User with profile
class UserWithProfile(UserResponse):
    profile: Optional[UserProfileInDB] = None
