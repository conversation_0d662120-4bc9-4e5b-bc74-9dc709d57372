from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Union, TypeVar, Generic
from datetime import datetime
import uuid

# Type variable for generic response
T = TypeVar('T')

# Base schema with common fields
class BaseSchema(BaseModel):
    id: Optional[uuid.UUID] = Field(default_factory=uuid.uuid4)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            uuid.UUID: lambda v: str(v) if v else None
        }

# Pagination
class Pagination(BaseModel):
    total: int
    page: int
    size: int
    total_pages: int

# Generic response wrapper
class Response(Generic[T], BaseModel):
    success: bool = True
    data: Optional[T] = None
    error: Optional[str] = None
    pagination: Optional[Pagination] = None
    
    @classmethod
    def success_response(
        cls, 
        data: Optional[T] = None,
        pagination: Optional[Pagination] = None
    ) -> 'Response[T]':
        return cls(success=True, data=data, pagination=pagination)
    
    @classmethod
    def error_response(
        cls, 
        error: str, 
        status_code: int = 400
    ) -> 'Response[T]':
        return cls(success=False, error=error)


# Generic list response wrapper
class ResponseList(Generic[T], BaseModel):
    success: bool = True
    data: Optional[List[T]] = None
    error: Optional[str] = None
    pagination: Optional[Pagination] = None
    
    @classmethod
    def success_response(
        cls, 
        data: Optional[List[T]] = None,
        pagination: Optional[Pagination] = None
    ) -> 'ResponseList[T]':
        return cls(success=True, data=data, pagination=pagination)
    
    @classmethod
    def error_response(
        cls, 
        error: str, 
        status_code: int = 400
    ) -> 'ResponseList[T]':
        return cls(success=False, error=error)
