from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_

from ..db.models import CalendarConnection, Appointment, AppointmentStatus
from ..schemas.calendar import CalendarConnectionCreate, CalendarConnectionUpdate

class CalendarRepository:
    """Repository for calendar-related database operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    # Calendar Connection Methods
    
    async def get_connection(self, connection_id: uuid.UUID) -> Optional[CalendarConnection]:
        """Get a calendar connection by ID."""
        result = await self.db.execute(
            select(CalendarConnection)
            .filter(CalendarConnection.id == connection_id)
        )
        return result.scalar_one_or_none()
    
    async def get_connection_by_user_and_provider(
        self, 
        user_id: str, 
        provider: str
    ) -> Optional[CalendarConnection]:
        """Get a calendar connection by user ID and provider."""
        result = await self.db.execute(
            select(CalendarConnection)
            .filter(
                CalendarConnection.user_id == user_id,
                CalendarConnection.provider == provider,
                CalendarConnection.is_active == True  # noqa: E712
            )
        )
        return result.scalar_one_or_none()
    
    async def create_connection(
        self, 
        connection_data: CalendarConnectionCreate
    ) -> CalendarConnection:
        """Create a new calendar connection."""
        # Check if a connection already exists for this user and provider
        existing = await self.get_connection_by_user_and_provider(
            user_id=connection_data.user_id,
            provider=connection_data.provider
        )
        
        if existing:
            # Update existing connection
            for key, value in connection_data.dict(exclude_unset=True).items():
                setattr(existing, key, value)
            existing.updated_at = datetime.utcnow()
            self.db.add(existing)
            await self.db.commit()
            await self.db.refresh(existing)
            return existing
        else:
            # Create new connection
            db_connection = CalendarConnection(
                **connection_data.dict(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.add(db_connection)
            await self.db.commit()
            await self.db.refresh(db_connection)
            return db_connection
    
    async def update_connection(
        self, 
        connection_id: uuid.UUID, 
        connection_data: CalendarConnectionUpdate
    ) -> Optional[CalendarConnection]:
        """Update a calendar connection."""
        connection = await self.get_connection(connection_id)
        if not connection:
            return None
            
        update_data = connection_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(connection, key, value)
            
        connection.updated_at = datetime.utcnow()
        self.db.add(connection)
        await self.db.commit()
        await self.db.refresh(connection)
        return connection
    
    async def delete_connection(self, connection_id: uuid.UUID) -> bool:
        """Soft delete a calendar connection."""
        connection = await self.get_connection(connection_id)
        if not connection:
            return False
            
        connection.is_active = False
        connection.updated_at = datetime.utcnow()
        self.db.add(connection)
        await self.db.commit()
        return True
    
    # Appointment Methods
    
    async def get_appointment(self, appointment_id: uuid.UUID) -> Optional[Appointment]:
        """Get an appointment by ID."""
        result = await self.db.execute(
            select(Appointment)
            .filter(Appointment.id == appointment_id)
        )
        return result.scalar_one_or_none()
    
    async def get_appointment_by_event_id(self, event_id: str) -> Optional[Appointment]:
        """Get an appointment by the provider's event ID."""
        result = await self.db.execute(
            select(Appointment)
            .filter(Appointment.event_id == event_id)
        )
        return result.scalar_one_or_none()
    
    async def create_appointment(self, appointment_data: Dict[str, Any]) -> Appointment:
        """Create a new appointment."""
        db_appointment = Appointment(**appointment_data)
        self.db.add(db_appointment)
        await self.db.commit()
        await self.db.refresh(db_appointment)
        return db_appointment
    
    async def update_appointment(
        self, 
        appointment_id: uuid.UUID, 
        update_data: Dict[str, Any]
    ) -> Optional[Appointment]:
        """Update an appointment."""
        appointment = await self.get_appointment(appointment_id)
        if not appointment:
            return None
            
        for key, value in update_data.items():
            setattr(appointment, key, value)
            
        appointment.updated_at = datetime.utcnow()
        self.db.add(appointment)
        await self.db.commit()
        await self.db.refresh(appointment)
        return appointment
    
    async def delete_appointment(self, appointment_id: uuid.UUID) -> bool:
        """Delete an appointment."""
        appointment = await self.get_appointment(appointment_id)
        if not appointment:
            return False
            
        await self.db.delete(appointment)
        await self.db.commit()
        return True
    
    async def list_appointments(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        status: Optional[AppointmentStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Appointment]:
        """List appointments for a user with optional filters."""
        query = select(Appointment).join(
            CalendarConnection,
            Appointment.calendar_connection_id == CalendarConnection.id
        ).filter(
            CalendarConnection.user_id == user_id,
            CalendarConnection.is_active == True  # noqa: E712
        )
        
        if start_date:
            query = query.filter(Appointment.start_time >= start_date)
        if end_date:
            query = query.filter(Appointment.start_time <= end_date)
        if status:
            query = query.filter(Appointment.status == status)
            
        query = query.order_by(Appointment.start_time.asc())
        query = query.offset(offset).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def find_conflicting_appointments(
        self,
        calendar_connection_id: uuid.UUID,
        start_time: datetime,
        end_time: datetime,
        exclude_appointment_id: Optional[uuid.UUID] = None
    ) -> List[Appointment]:
        """Find appointments that conflict with the given time range."""
        query = select(Appointment).filter(
            Appointment.calendar_connection_id == calendar_connection_id,
            or_(
                # Starts during another appointment
                and_(
                    Appointment.start_time >= start_time,
                    Appointment.start_time < end_time,
                ),
                # Ends during another appointment
                and_(
                    Appointment.end_time > start_time,
                    Appointment.end_time <= end_time,
                ),
                # Wraps around another appointment
                and_(
                    Appointment.start_time <= start_time,
                    Appointment.end_time >= end_time,
                ),
            ),
            Appointment.status != AppointmentStatus.CANCELLED
        )
        
        if exclude_appointment_id:
            query = query.filter(Appointment.id != exclude_appointment_id)
            
        result = await self.db.execute(query)
        return result.scalars().all()
