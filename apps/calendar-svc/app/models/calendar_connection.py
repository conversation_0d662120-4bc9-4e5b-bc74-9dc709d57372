from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from typing import Optional
import json

class CalendarConnection(BaseModel):
    """Represents a user's connection to a calendar service."""
    id: str = Field(..., description="Unique identifier for the connection")
    user_id: str = Field(..., description="ID of the user who owns this connection")
    provider: str = Field(..., description="Calendar provider (e.g., 'google', 'calendly')")
    access_token: str = Field(..., description="OAuth access token")
    refresh_token: str = Field(..., description="OAuth refresh token")
    token_expiry: Optional[datetime] = Field(None, description="When the access token expires")
    calendar_id: Optional[str] = Field(None, description="ID of the primary calendar to use")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    metadata: dict = Field(default_factory=dict, description="Additional provider-specific data")

    def to_dict(self):
        """Convert the model to a dictionary, handling datetime objects."""
        data = self.dict()
        # Convert datetime objects to ISO format strings
        for field in ['token_expiry', 'created_at', 'updated_at']:
            if field in data and data[field] is not None:
                data[field] = data[field].isoformat()
        return data

    @classmethod
    def from_dict(cls, data: dict):
        """Create a model instance from a dictionary."""
        # Convert string dates back to datetime objects
        datetime_fields = ['token_expiry', 'created_at', 'updated_at']
        for field in datetime_fields:
            if field in data and isinstance(data[field], str):
                data[field] = datetime.fromisoformat(data[field])
        return cls(**data)

    def is_expired(self) -> bool:
        """Check if the access token has expired."""
        if not self.token_expiry:
            return False
        return datetime.utcnow() > self.token_expiry

    def needs_refresh(self, threshold_minutes: int = 5) -> bool:
        """Check if the token needs to be refreshed.
        
        Args:
            threshold_minutes: Number of minutes before expiry to consider for refresh
        """
        if not self.token_expiry:
            return False
        return datetime.utcnow() > (self.token_expiry - timedelta(minutes=threshold_minutes))
