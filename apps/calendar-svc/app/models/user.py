from datetime import datetime
from typing import Optional, List
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from ..db.base import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, index=True)
    is_active = Column(Boolean(), default=True)
    is_superuser = Column(Boolean(), default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # Relationships
    calendar_connections = relationship("CalendarConnection", back_populates="user")
    appointments = relationship("Appointment", back_populates="user")
    
    def __repr__(self):
        return f"<User {self.email}>"
    
    @property
    def is_authenticated(self) -> bool:
        return self.is_active
    
    def get_id(self) -> str:
        return str(self.id)
    
    @property
    def is_admin(self) -> bool:
        return self.is_superuser

class UserProfile(Base):
    __tablename__ = "user_profiles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, index=True)
    
    # Profile information
    phone_number = Column(String, nullable=True)
    timezone = Column(String, default="UTC")
    avatar_url = Column(String, nullable=True)
    
    # Preferences
    preferences = Column(JSON, default=dict)
    notification_preferences = Column(JSON, default=dict)
    
    # Relationships
    user = relationship("User", back_populates="profile")
    
    def __repr__(self):
        return f"<UserProfile {self.user_id}>"

# Add the backref to the User model
User.profile = relationship("UserProfile", back_populates="user", uselist=False)
