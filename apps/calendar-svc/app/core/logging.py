import logging
import logging.config
import os
from typing import Dict, Any

from pythonjsonlogger import jsonlogger
from app.config import settings

def setup_logging() -> None:
    """Setup logging configuration."""
    # Use getattr with default values to handle missing settings
    log_level = getattr(settings, 'LOG_LEVEL', 'INFO').upper()
    log_format = getattr(settings, 'LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    log_file = getattr(settings, 'LOG_FILE', None)
    
    # Create logs directory if it doesn't exist
    if log_file and not os.path.exists(os.path.dirname(log_file)):
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Define log formatters
    formatters = {
        "json": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(asctime)s %(levelname)s %(name)s %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "text": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    }
    
    # Define handlers
    handlers: Dict[str, Any] = {
        "console": {
            "class": "logging.StreamHandler",
            "level": log_level,
            "formatter": "text",  # Use the text formatter defined in formatters
            "stream": "ext://sys.stdout",
        }
    }
    
    if log_file:
        handlers["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": log_level,
            "formatter": "text",  # Use the text formatter defined in formatters
            "filename": log_file,
            "maxBytes": 10 * 1024 * 1024,  # 10MB
            "backupCount": 5,
            "encoding": "utf8",
        }
    
    # Configure root logger
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": formatters,
        "handlers": handlers,
        "root": {
            "level": log_level,
            "handlers": list(handlers.keys()),
        },
        "loggers": {
            # Configure specific loggers
            "uvicorn": {
                "level": log_level,
                "handlers": list(handlers.keys()),
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "WARNING",  # Set to INFO for SQL logging
                "handlers": list(handlers.keys()),
                "propagate": False,
            },
            "googleapiclient": {
                "level": "WARNING",
                "handlers": list(handlers.keys()),
                "propagate": False,
            },
        },
    }
    
    # Apply the configuration
    logging.config.dictConfig(logging_config)
    
    # Set log level for other loggers
    logging.getLogger("httpx").setLevel("WARNING")
    logging.getLogger("httpcore").setLevel("WARNING")
    logging.getLogger("urllib3").setLevel("WARNING")
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info("Logging configured with level %s", log_level)
