from sqlalchemy import Column, String, DateTime, JSON, Boolean, Foreign<PERSON>ey, Index, Text, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
import os
from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional, Dict, Any, List

from ..base import Base

# Use JSON type for SQLite (testing) and JSONB for PostgreSQL (production)
# This helps make the models compatible with both databases
is_test = os.environ.get('ENVIRONMENT') == 'test'
JsonType = JSON if is_test else JSONB

class AppointmentStatus(PyEnum):
    """Status of an appointment."""
    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    RESCHEDULED = "rescheduled"
    COMPLETED = "completed"
    NO_SHOW = "no_show"

class Appointment(Base):
    """Database model for appointments."""
    __tablename__ = "appointments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    calendar_connection_id = Column(UUID(as_uuid=True), ForeignKey("calendar_connections.id"), nullable=False, index=True)
    
    # Appointment details
    event_id = Column(String, nullable=True, index=True)  # ID from the calendar provider
    summary = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    location = Column(String, nullable=True)
    
    # Time-related fields
    start_time = Column(DateTime(timezone=True), nullable=False, index=True)
    end_time = Column(DateTime(timezone=True), nullable=False, index=True)
    timezone = Column(String(50), default="UTC")
    
    # Participants
    organizer_email = Column(String, nullable=False)
    attendee_emails = Column(JsonType, default=list)  # List of attendee emails
    
    # Status and metadata
    status = Column(Enum(AppointmentStatus), default=AppointmentStatus.SCHEDULED, nullable=False, index=True)
    status_note = Column(String, nullable=True)  # Reason for status change
    
    # Recurrence
    is_recurring = Column(Boolean, default=False, nullable=False)
    recurrence_rule = Column(String, nullable=True)  # iCal RRULE format
    master_event_id = Column(String, nullable=True, index=True)  # For recurring events
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    metadata_ = Column("metadata", JsonType, default=dict)  # Additional metadata
    
    # Indexes - conditionally create GIN index only for PostgreSQL
    if is_test:
        # Simple indexes for SQLite
        __table_args__ = (
            Index('idx_appointments_organizer', 'organizer_email'),
            Index('idx_appointments_time_range', 'start_time', 'end_time'),
        )
    else:
        # Full indexes including GIN for PostgreSQL
        __table_args__ = (
            Index('idx_appointments_organizer', 'organizer_email'),
            Index('idx_appointments_attendee_emails', 'attendee_emails', postgresql_using='gin'),
            Index('idx_appointments_time_range', 'start_time', 'end_time'),
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "calendar_connection_id": str(self.calendar_connection_id),
            "event_id": self.event_id,
            "summary": self.summary,
            "description": self.description,
            "location": self.location,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "timezone": self.timezone,
            "organizer_email": self.organizer_email,
            "attendee_emails": self.attendee_emails or [],
            "status": self.status.value if self.status else None,
            "status_note": self.status_note,
            "is_recurring": self.is_recurring,
            "recurrence_rule": self.recurrence_rule,
            "master_event_id": self.master_event_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "metadata": self.metadata_ or {}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create model instance from dictionary."""
        return cls(
            id=data.get("id") or uuid.uuid4(),
            calendar_connection_id=data["calendar_connection_id"],
            event_id=data.get("event_id"),
            summary=data["summary"],
            description=data.get("description"),
            location=data.get("location"),
            start_time=data["start_time"],
            end_time=data["end_time"],
            timezone=data.get("timezone", "UTC"),
            organizer_email=data["organizer_email"],
            attendee_emails=data.get("attendee_emails", []),
            status=AppointmentStatus(data["status"]) if data.get("status") else AppointmentStatus.SCHEDULED,
            status_note=data.get("status_note"),
            is_recurring=data.get("is_recurring", False),
            recurrence_rule=data.get("recurrence_rule"),
            master_event_id=data.get("master_event_id"),
            metadata_=data.get("metadata", {})
        )
