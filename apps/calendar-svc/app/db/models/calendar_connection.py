from sqlalchemy import Column, String, DateTime, JSON, Boolean, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime

from ..base import Base

class CalendarConnection(Base):
    """Database model for calendar connections."""
    __tablename__ = "calendar_connections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(String, nullable=False, index=True)
    provider = Column(String, nullable=False)  # e.g., 'google', 'microsoft', etc.
    access_token = Column(String, nullable=False)
    refresh_token = Column(String, nullable=True)  # Not all providers return refresh tokens
    token_expiry = Column(DateTime, nullable=True)
    calendar_id = Column(String, nullable=True)  # Primary calendar ID
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Provider-specific data
    provider_user_id = Column(String, nullable=True)
    provider_email = Column(String, nullable=True)
    scopes = Column(JSON, nullable=True)  # List of OAuth scopes
    metadata_ = Column("metadata", JSON, default=dict)  # Additional provider-specific data
    
    # Indexes
    __table_args__ = (
        Index('idx_calendar_connections_user_provider', 'user_id', 'provider', unique=True),
        Index('idx_calendar_connections_provider_user', 'provider', 'provider_user_id'),
    )
    
    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "user_id": self.user_id,
            "provider": self.provider,
            "provider_user_id": self.provider_user_id,
            "provider_email": self.provider_email,
            "calendar_id": self.calendar_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_active": self.is_active,
            "scopes": self.scopes,
            "metadata": self.metadata_
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        """Create model instance from dictionary."""
        return cls(
            id=data.get("id", uuid.uuid4()),
            user_id=data["user_id"],
            provider=data["provider"],
            access_token=data["access_token"],
            refresh_token=data.get("refresh_token"),
            token_expiry=data.get("token_expiry"),
            calendar_id=data.get("calendar_id"),
            provider_user_id=data.get("provider_user_id"),
            provider_email=data.get("provider_email"),
            scopes=data.get("scopes"),
            metadata_=data.get("metadata", {})
        )
