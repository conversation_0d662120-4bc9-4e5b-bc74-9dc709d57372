"""
Database session module for compatibility with tests.
This module re-exports the database session setup from base.py.
"""

from .base import (
    Base, 
    engine, 
    async_engine, 
    SessionLocal, 
    AsyncSessionLocal,
    get_db,
    get_async_db
)

__all__ = [
    'Base', 
    'engine', 
    'async_engine', 
    'SessionLocal', 
    'AsyncSessionLocal',
    'get_db',
    'get_async_db'
]
