# Spanish intake conversation flow for AI Lex Receptionist
version: "1.0"
metadata:
  name: "AI Lex Intake Flow - Spanish"
  description: "Handles initial intake conversation for law firm callers in Spanish"
  language: "es"

# System configuration
config:
  allow_interruptions: true
  enable_metrics: true
  enable_usage_metrics: true

# Initial system prompt
system_prompt: |
  Eres AILex, un recepcionista virtual profesional y amigable para un bufete de abogados.
  Tu objetivo es saludar al llamante, recopilar su información, entender su asunto legal,
  y evaluar la urgencia para determinar los próximos pasos apropiados.
  
  Sigue estas pautas:
  - Sé profesional, cortés y empático
  - Escucha atentamente las necesidades del llamante
  - Recopila información esencial de manera conversacional
  - No proporciones asesoramiento legal
  - Habla con naturalidad usando oraciones breves y claras
  - Tus respuestas se convertirán en audio, así que evita formateo especial

# Dialogue flow stages
flow:
  greeting:
    message: |
      <PERSON><PERSON>, gracias por llamar a las oficinas legales de Johnson & Associates. Soy AILex, 
      el recepcionista virtual. ¿En qué puedo ayudarle hoy?
    next: collect_name

  collect_name:
    listen:
      intent:
        greeting: acknowledge_greeting
        help_request: acknowledge_help
      fallback: ask_name
    
  acknowledge_greeting:
    message: |
      Es un placer conocerle. Antes de continuar, ¿podría decirme su nombre, por favor?
    next: ask_name
    
  acknowledge_help:
    message: |
      Estaré encantado de ayudarle. Para atenderle mejor, ¿podría decirme su nombre, por favor?
    next: ask_name
    
  ask_name:
    message: |
      Para poder ayudarle mejor, ¿podría decirme su nombre, por favor?
    next: process_name
    
  process_name:
    listen:
      entity:
        person_name: store_name
      fallback: clarify_name
      
  clarify_name:
    message: |
      Disculpe, no entendí su nombre. ¿Podría decirme su nombre completo, por favor?
    next: process_name
    
  store_name:
    action: |
      # Store name in the session data
      session.caller_name = entities.person_name
    next: collect_contact
    
  collect_contact:
    message: |
      Gracias, {{session.caller_name}}. En caso de que nos desconectemos, ¿podría proporcionarme su dirección de correo electrónico o número de teléfono?
    next: process_contact
    
  process_contact:
    listen:
      entity:
        email: store_email
        phone_number: store_phone
      intent:
        refuse: acknowledge_refusal
      fallback: clarify_contact
      
  clarify_contact:
    message: |
      Disculpe, no entendí eso. Necesito un correo electrónico o número de teléfono para contactarle si es necesario. ¿Cuál prefiere compartir?
    next: process_contact
    
  store_email:
    action: |
      session.contact_type = "correo electrónico"
      session.contact_info = entities.email
    next: confirm_contact
    
  store_phone:
    action: |
      session.contact_type = "teléfono"
      session.contact_info = entities.phone_number
    next: confirm_contact
    
  acknowledge_refusal:
    message: |
      Entiendo que prefiere no compartir su información de contacto en este momento. Podemos continuar, pero puede limitar nuestra capacidad de hacer seguimiento si es necesario.
    next: collect_matter_type
    
  confirm_contact:
    message: |
      Gracias. Tengo su {{session.contact_type}} como {{session.contact_info}}. Ahora, ¿podría decirme brevemente cuál es el asunto legal por el que llama hoy?
    next: process_matter
    
  collect_matter_type:
    message: |
      ¿Podría decirme brevemente cuál es el asunto legal por el que llama hoy?
    next: process_matter
    
  process_matter:
    listen:
      intent:
        family_law: store_family_law
        personal_injury: store_personal_injury
        criminal_defense: store_criminal_defense
        business_law: store_business_law
        estate_planning: store_estate_planning
      fallback: store_other_matter
      
  store_family_law:
    action: |
      session.matter_type = "Derecho de Familia"
    next: assess_urgency
    
  store_personal_injury:
    action: |
      session.matter_type = "Lesiones Personales"
    next: assess_urgency
    
  store_criminal_defense:
    action: |
      session.matter_type = "Defensa Criminal"
    next: assess_urgency
    
  store_business_law:
    action: |
      session.matter_type = "Derecho Empresarial"
    next: assess_urgency
    
  store_estate_planning:
    action: |
      session.matter_type = "Planificación Patrimonial"
    next: assess_urgency
    
  store_other_matter:
    action: |
      # Extract matter type from user input
      session.matter_type = "Otro: " + last_user_message
    next: assess_urgency
    
  assess_urgency:
    message: |
      Entiendo que llama por un asunto de {{session.matter_type}}. ¿Qué tan urgente es este asunto? ¿Necesita asistencia inmediata o prefiere programar una consulta?
    next: process_urgency
    
  process_urgency:
    listen:
      intent:
        urgent: handle_urgent
        non_urgent: offer_booking
      fallback: clarify_urgency
      
  clarify_urgency:
    message: |
      Disculpe, no entendí bien. ¿Es este un asunto urgente que requiere atención inmediata, o preferiría programar una consulta con uno de nuestros abogados?
    next: process_urgency
    
  handle_urgent:
    action: |
      session.urgency = "urgente"
    next: transfer_call
    
  transfer_call:
    message: |
      Debido a la urgencia de su asunto de {{session.matter_type}}, intentaré conectarle con un abogado disponible de inmediato. Por favor, espere mientras transfiero su llamada. Si no hay nadie disponible, puedo tomar un mensaje o ayudarle a programar una devolución de llamada a su conveniencia.
    next: end_conversation
    
  offer_booking:
    action: |
      session.urgency = "no urgente"
    next: propose_booking
    
  propose_booking:
    message: |
      Estaré encantado de ayudarle a programar una consulta sobre su asunto de {{session.matter_type}}. Nuestros abogados tienen varias disponibilidades en los próximos días. ¿Le gustaría que verifique la disponibilidad y le proponga algunos horarios?
    next: process_booking_response
    
  process_booking_response:
    listen:
      intent:
        affirmative: book_appointment
        negative: offer_alternative
      fallback: clarify_booking
      
  clarify_booking:
    message: |
      Disculpe, no entendí eso. ¿Le gustaría que verifique la disponibilidad de nuestros abogados y programe una consulta para usted?
    next: process_booking_response
    
  book_appointment:
    message: |
      ¡Excelente! Permítame verificar nuestro calendario para los próximos horarios disponibles. Un momento por favor...
    next: suggest_slots
    
  suggest_slots:
    action: |
      # This will be implemented to call the proposeSlot function
      session.available_slots = helper.proposeSlot()
    next: present_slots
    
  present_slots:
    message: |
      Tengo los siguientes horarios disponibles:
      
      {% for slot in session.available_slots %}
      - {{slot}}
      {% endfor %}
      
      ¿Qué horario le conviene mejor?
    next: process_slot_selection
    
  process_slot_selection:
    listen:
      entity:
        datetime: confirm_selected_slot
      intent:
        none_works: offer_more_slots
      fallback: clarify_slot_selection
      
  clarify_slot_selection:
    message: |
      Disculpe, no entendí qué horario prefiere. ¿Podría decirme cuál de los horarios propuestos le funciona mejor?
    next: process_slot_selection
    
  offer_more_slots:
    message: |
      Entiendo que ninguno de esos horarios le funciona. Permítame verificar si tenemos otra disponibilidad.
    next: suggest_alternative_slots
    
  suggest_alternative_slots:
    action: |
      # This would fetch additional slots
      session.available_slots = helper.proposeMoreSlots()
    next: present_slots
    
  confirm_selected_slot:
    action: |
      session.selected_slot = entities.datetime
      # This would call the confirmSlot function
      session.booking = helper.confirmSlot(session.selected_slot, session.caller_name, session.contact_info)
    next: booking_confirmed
    
  booking_confirmed:
    message: |
      ¡Excelente! He confirmado su cita para el {{session.selected_slot}}. Recibirá una confirmación en {{session.contact_info}}. ¿Hay algo más en lo que pueda ayudarle hoy?
    next: process_additional_help
    
  process_additional_help:
    listen:
      intent:
        affirmative: offer_additional_help
        negative: end_conversation
      fallback: clarify_additional_help
      
  clarify_additional_help:
    message: |
      Disculpe, no entendí eso. ¿Hay algo más en lo que pueda ayudarle hoy?
    next: process_additional_help
    
  offer_additional_help:
    message: |
      ¿En qué más puedo ayudarle hoy?
    next: process_new_request
    
  process_new_request:
    listen:
      intent:
        # Additional intents would be defined here
        booking_information: provide_booking_info
      fallback: unable_to_help
      
  provide_booking_info:
    message: |
      Su cita está programada para el {{session.selected_slot}} sobre {{session.matter_type}}. Se ha enviado una confirmación a {{session.contact_info}}. Uno de nuestros abogados estará preparado para asistirle en ese momento.
    next: end_conversation
    
  unable_to_help:
    message: |
      Lo siento, pero no puedo ayudarle con esa solicitud específica en este momento. Si tiene alguna pregunta sobre su cita o necesita hacer cambios, por favor háganoslo saber cuando venga a su cita, o puede llamar de nuevo durante nuestro horario regular de oficina.
    next: end_conversation
    
  offer_alternative:
    message: |
      Entiendo que no desea programar una cita en este momento. ¿Le gustaría que alguien le devuelva la llamada?
    next: process_callback
    
  process_callback:
    listen:
      intent:
        affirmative: schedule_callback
        negative: end_conversation
      fallback: clarify_callback
      
  clarify_callback:
    message: |
      Disculpe, no entendí. ¿Le gustaría que organice que alguien le llame sobre su asunto de {{session.matter_type}}?
    next: process_callback
    
  schedule_callback:
    message: |
      Organizaré que uno de nuestros abogados le devuelva la llamada sobre su asunto de {{session.matter_type}}. Se pondrán en contacto con usted en {{session.contact_info}} lo antes posible. ¿Hay algún horario específico que le convenga?
    next: process_callback_time
    
  process_callback_time:
    listen:
      entity:
        datetime: confirm_callback_time
      intent:
        anytime: flexible_callback
      fallback: clarify_callback_time
      
  clarify_callback_time:
    message: |
      Disculpe, no entendí eso. ¿Hay algún horario específico que prefiera para la devolución de llamada?
    next: process_callback_time
    
  confirm_callback_time:
    action: |
      session.callback_time = entities.datetime
      # This would create a callback task
      session.callback_task = helper.createCallbackTask(session.caller_name, session.contact_info, session.matter_type, session.callback_time)
    next: callback_confirmed
    
  flexible_callback:
    action: |
      session.callback_time = "flexible"
      # This would create a callback task with flexible timing
      session.callback_task = helper.createCallbackTask(session.caller_name, session.contact_info, session.matter_type, "flexible")
    next: callback_confirmed
    
  callback_confirmed:
    message: |
      ¡Perfecto! He programado una devolución de llamada {% if session.callback_time != "flexible" %}para el {{session.callback_time}}{% else %}a la mayor brevedad posible de nuestros abogados{% endif %}. Alguien se pondrá en contacto con usted en {{session.contact_info}} sobre su asunto de {{session.matter_type}}. ¿Hay algo más en lo que pueda ayudarle hoy?
    next: process_additional_help
    
  end_conversation:
    message: |
      Gracias por llamar a las oficinas legales de Johnson & Associates. Agradecemos su tiempo hoy y esperamos poder ayudarle. ¡Que tenga un maravilloso día!
    next: terminate
    
  terminate:
    action: |
      # End the call
      return end_call()
