# English intake conversation flow for AI Lex Receptionist
version: "1.0"
metadata:
  name: "AI Lex Intake Flow - English"
  description: "Handles initial intake conversation for law firm callers in English"
  language: "en"

# System configuration
config:
  allow_interruptions: true
  enable_metrics: true
  enable_usage_metrics: true

# Initial system prompt
system_prompt: |
  You are <PERSON><PERSON><PERSON>, a professional and friendly AI receptionist for a law firm.
  Your goal is to greet the caller, collect their information, understand their legal matter,
  and assess urgency to determine appropriate next steps.
  
  Follow these guidelines:
  - Be professional, courteous, and empathetic
  - Listen carefully to the caller's needs
  - Collect essential information in a conversational manner
  - Do not provide legal advice
  - Speak naturally with brief, clear sentences
  - Your responses will be converted to audio, so avoid special formatting

# Dialogue flow stages
flow:
  greeting:
    message: |
      Hello, thank you for calling the law offices of Johnson & Associates. This is <PERSON><PERSON><PERSON>, 
      the virtual receptionist. How may I help you today?
    next: collect_name

  collect_name:
    listen:
      intent:
        greeting: acknowledge_greeting
        help_request: acknowledge_help
      fallback: ask_name
    
  acknowledge_greeting:
    message: |
      It's nice to meet you. Before we continue, may I have your name please?
    next: ask_name
    
  acknowledge_help:
    message: |
      I'd be happy to help. To better assist you, may I have your name please?
    next: ask_name
    
  ask_name:
    message: |
      To help me assist you better, may I have your name please?
    next: process_name
    
  process_name:
    listen:
      entity:
        person_name: store_name
      fallback: clarify_name
      
  clarify_name:
    message: |
      I'm sorry, I didn't catch your name. Could you please tell me your full name?
    next: process_name
    
  store_name:
    action: |
      # Store name in the session data
      session.caller_name = entities.person_name
    next: collect_contact
    
  collect_contact:
    message: |
      Thank you, {{session.caller_name}}. In case we get disconnected, could you please provide your email address or phone number?
    next: process_contact
    
  process_contact:
    listen:
      entity:
        email: store_email
        phone_number: store_phone
      intent:
        refuse: acknowledge_refusal
      fallback: clarify_contact
      
  clarify_contact:
    message: |
      I'm sorry, I didn't catch that. I need either an email address or phone number to reach you if needed. Which would you prefer to share?
    next: process_contact
    
  store_email:
    action: |
      session.contact_type = "email"
      session.contact_info = entities.email
    next: confirm_contact
    
  store_phone:
    action: |
      session.contact_type = "phone"
      session.contact_info = entities.phone_number
    next: confirm_contact
    
  acknowledge_refusal:
    message: |
      I understand you prefer not to share your contact information right now. We can proceed, but it may limit our ability to follow up if needed.
    next: collect_matter_type
    
  confirm_contact:
    message: |
      Thank you. I have your {{session.contact_type}} as {{session.contact_info}}. Now, could you briefly tell me what legal matter you're calling about today?
    next: process_matter
    
  collect_matter_type:
    message: |
      Could you briefly tell me what legal matter you're calling about today?
    next: process_matter
    
  process_matter:
    listen:
      intent:
        family_law: store_family_law
        personal_injury: store_personal_injury
        criminal_defense: store_criminal_defense
        business_law: store_business_law
        estate_planning: store_estate_planning
      fallback: store_other_matter
      
  store_family_law:
    action: |
      session.matter_type = "Family Law"
    next: assess_urgency
    
  store_personal_injury:
    action: |
      session.matter_type = "Personal Injury"
    next: assess_urgency
    
  store_criminal_defense:
    action: |
      session.matter_type = "Criminal Defense"
    next: assess_urgency
    
  store_business_law:
    action: |
      session.matter_type = "Business Law"
    next: assess_urgency
    
  store_estate_planning:
    action: |
      session.matter_type = "Estate Planning"
    next: assess_urgency
    
  store_other_matter:
    action: |
      # Extract matter type from user input
      session.matter_type = "Other: " + last_user_message
    next: assess_urgency
    
  assess_urgency:
    message: |
      I understand you're calling about {{session.matter_type}}. How urgent is this matter? Do you need immediate assistance, or would you like to schedule a consultation?
    next: process_urgency
    
  process_urgency:
    listen:
      intent:
        urgent: handle_urgent
        non_urgent: offer_booking
      fallback: clarify_urgency
      
  clarify_urgency:
    message: |
      I'm sorry, I didn't quite understand. Is this an urgent matter that requires immediate attention, or would you prefer to schedule a consultation with one of our attorneys?
    next: process_urgency
    
  handle_urgent:
    action: |
      session.urgency = "urgent"
    next: transfer_call
    
  transfer_call:
    message: |
      Based on the urgency of your {{session.matter_type}} matter, I'll try to connect you with an available attorney right away. Please hold while I transfer your call. If no one is available, I can take a message or help you schedule a callback at your convenience.
    next: end_conversation
    
  offer_booking:
    action: |
      session.urgency = "non-urgent"
    next: propose_booking
    
  propose_booking:
    message: |
      I'd be happy to help you schedule a consultation regarding your {{session.matter_type}} matter. Our attorneys have several openings in the coming days. Would you like me to check availability and propose some times for you?
    next: process_booking_response
    
  process_booking_response:
    listen:
      intent:
        affirmative: book_appointment
        negative: offer_alternative
      fallback: clarify_booking
      
  clarify_booking:
    message: |
      I'm sorry, I didn't catch that. Would you like me to check our attorneys' availability and schedule a consultation for you?
    next: process_booking_response
    
  book_appointment:
    message: |
      Great! Let me check our calendar for the next available slots. One moment please...
    next: suggest_slots
    
  suggest_slots:
    action: |
      # This will be implemented to call the proposeSlot function
      session.available_slots = helper.proposeSlot()
    next: present_slots
    
  present_slots:
    message: |
      I have the following slots available:
      
      {% for slot in session.available_slots %}
      - {{slot}}
      {% endfor %}
      
      Which time works best for you?
    next: process_slot_selection
    
  process_slot_selection:
    listen:
      entity:
        datetime: confirm_selected_slot
      intent:
        none_works: offer_more_slots
      fallback: clarify_slot_selection
      
  clarify_slot_selection:
    message: |
      I'm sorry, I didn't catch which time you preferred. Could you tell me which of the proposed times works best for you?
    next: process_slot_selection
    
  offer_more_slots:
    message: |
      I understand none of those times work for you. Let me check if we have other availability.
    next: suggest_alternative_slots
    
  suggest_alternative_slots:
    action: |
      # This would fetch additional slots
      session.available_slots = helper.proposeMoreSlots()
    next: present_slots
    
  confirm_selected_slot:
    action: |
      session.selected_slot = entities.datetime
      # This would call the confirmSlot function
      session.booking = helper.confirmSlot(session.selected_slot, session.caller_name, session.contact_info)
    next: booking_confirmed
    
  booking_confirmed:
    message: |
      Excellent! I've confirmed your appointment for {{session.selected_slot}}. You'll receive a confirmation at {{session.contact_info}}. Is there anything else I can help you with today?
    next: process_additional_help
    
  process_additional_help:
    listen:
      intent:
        affirmative: offer_additional_help
        negative: end_conversation
      fallback: clarify_additional_help
      
  clarify_additional_help:
    message: |
      I'm sorry, I didn't catch that. Is there anything else I can assist you with today?
    next: process_additional_help
    
  offer_additional_help:
    message: |
      What else can I help you with today?
    next: process_new_request
    
  process_new_request:
    listen:
      intent:
        # Additional intents would be defined here
        booking_information: provide_booking_info
      fallback: unable_to_help
      
  provide_booking_info:
    message: |
      Your appointment is scheduled for {{session.selected_slot}} regarding {{session.matter_type}}. A confirmation has been sent to {{session.contact_info}}. One of our attorneys will be prepared to assist you at that time.
    next: end_conversation
    
  unable_to_help:
    message: |
      I'm sorry, but I'm not able to help with that specific request right now. If you have any questions about your booking or need to make changes, please let us know when you come in for your appointment, or you can call back during our regular business hours.
    next: end_conversation
    
  offer_alternative:
    message: |
      I understand you don't want to schedule an appointment right now. Would you like me to have someone call you back instead?
    next: process_callback
    
  process_callback:
    listen:
      intent:
        affirmative: schedule_callback
        negative: end_conversation
      fallback: clarify_callback
      
  clarify_callback:
    message: |
      I'm sorry, I didn't understand. Would you like me to arrange for someone to call you back about your {{session.matter_type}} matter?
    next: process_callback
    
  schedule_callback:
    message: |
      I'll arrange for one of our attorneys to call you back regarding your {{session.matter_type}} matter. They'll reach out to you at {{session.contact_info}} as soon as possible. Is there a specific time that works best for you?
    next: process_callback_time
    
  process_callback_time:
    listen:
      entity:
        datetime: confirm_callback_time
      intent:
        anytime: flexible_callback
      fallback: clarify_callback_time
      
  clarify_callback_time:
    message: |
      I'm sorry, I didn't catch that. Is there a specific time you would prefer for the callback?
    next: process_callback_time
    
  confirm_callback_time:
    action: |
      session.callback_time = entities.datetime
      # This would create a callback task
      session.callback_task = helper.createCallbackTask(session.caller_name, session.contact_info, session.matter_type, session.callback_time)
    next: callback_confirmed
    
  flexible_callback:
    action: |
      session.callback_time = "flexible"
      # This would create a callback task with flexible timing
      session.callback_task = helper.createCallbackTask(session.caller_name, session.contact_info, session.matter_type, "flexible")
    next: callback_confirmed
    
  callback_confirmed:
    message: |
      Perfect! I've scheduled a callback {% if session.callback_time != "flexible" %}for {{session.callback_time}}{% else %}at the earliest convenience of our attorneys{% endif %}. Someone will reach out to you at {{session.contact_info}} regarding your {{session.matter_type}} matter. Is there anything else I can help with today?
    next: process_additional_help
    
  end_conversation:
    message: |
      Thank you for calling the law offices of Johnson & Associates. We appreciate your time today and look forward to assisting you. Have a wonderful day!
    next: terminate
    
  terminate:
    action: |
      # End the call
      return end_call()
