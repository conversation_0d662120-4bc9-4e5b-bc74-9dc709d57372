"""
Bot runner module for voice agent pipeline.

This module provides the functionality to run the Pipecat pipeline
using the TelnyxTransport with proper configuration and error handling.
"""

import os
import sys
import asyncio
from pathlib import Path
from loguru import logger
from dotenv import load_dotenv

# Add project root to sys.path to ensure imports work correctly
project_root = str(Path(__file__).parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

# Import the existing bot functionality
from apps.voice_agent.bot import bot
from apps.voice_agent.transports.telnyx_transport import TelnyxTransport, TelnyxParams
from pipecatcloud.agent import DailySessionArguments

# Load environment variables
load_dotenv()

async def run_pipeline(call_control_id: str, telnyx_rtc_session_id: str, lang: str = "en"):
    """
    Run the voice agent pipeline with TelnyxTransport.
    
    Args:
        call_control_id: The Telnyx call control ID
        telnyx_rtc_session_id: The Telnyx RTC session ID for WebSocket connection
        lang: The language code for the conversation flow (default: "en")
    
    Returns:
        None
    """
    logger.info(
        f"Starting voice pipeline: call_control_id={call_control_id}, "
        f"session_id={telnyx_rtc_session_id}, lang={lang}"
    )
    
    try:
        # Create args that our bot function can use
        # We're adapting the DailySessionArguments to work with Telnyx
        # since our existing bot.py expects those arguments
        args = DailySessionArguments(
            room_url="",  # Not used with TelnyxTransport
            token="",     # Not used with TelnyxTransport
            body={
                "language_toggle": lang != "en",
                "translate_transcript": lang != "en",
                "language": lang,
                "call_control_id": call_control_id,
                "telnyx_rtc_session_id": telnyx_rtc_session_id
            },
            session_id=telnyx_rtc_session_id
        )
        
        # Run the bot with our arguments
        await bot(args)
        logger.info(f"Voice pipeline completed: session_id={telnyx_rtc_session_id}")
    
    except Exception as e:
        logger.exception(f"Error running voice pipeline: {str(e)}")
        # We don't re-raise the exception to prevent the background task from failing
        # but we log it for debugging
