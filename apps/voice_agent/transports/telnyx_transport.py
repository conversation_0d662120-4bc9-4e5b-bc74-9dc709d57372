"""
Minimal Telnyx transport that the tests expect.

It simply glues Telnyx's Media-Streams WebSocket to Pipecat's
WebsocketServerTransport + TelnyxFrameSerializer.
"""

from __future__ import annotations
from typing import Optional

from pydantic import BaseModel, Field

from pipecat.serializers.telnyx import TelnyxFrameSerializer
from pipecat.transports.network.websocket_server import WebsocketServerTransport


class TelnyxParams(BaseModel):
    # Values that come from the Telnyx webhook JSON
    stream_id: str = Field(..., description="`stream_id` from media.stream_started")
    call_control_id: Optional[str] = Field(
        None, description="`call_control_id` (needed if you auto-hang-up)"
    )
    api_key: Optional[str] = Field(
        None,
        description="Telnyx secret key – only required if you let <PERSON><PERSON><PERSON> hang up",
    )

    # Codec / sample-rate (override only if you know what you're doing)
    inbound_encoding: str = "PCMU"
    outbound_encoding: str = "PCMU"
    sample_rate: int = 8_000  # Telnyx always streams μ-law @ 8 kHz

    auto_hang_up: bool = True


class TelnyxTransport(WebsocketServerTransport):
    """
    Start a local WS server that speaks the Telnyx Media-Streams protocol.

    Usage
    -----
    params = TelnyxParams(**json_from_stream_started)
    transport = TelnyxTransport(params, host="0.0.0.0", port=8765)
    """

    def __init__(
        self,
        params: TelnyxParams,
        *,
        host: str = "0.0.0.0",
        port: int = 8765,
    ):
        serializer = TelnyxFrameSerializer(
            stream_id=params.stream_id,
            outbound_encoding=params.outbound_encoding,
            inbound_encoding=params.inbound_encoding,
            call_control_id=params.call_control_id,
            api_key=params.api_key,
            params=TelnyxFrameSerializer.InputParams(
                sample_rate=params.sample_rate,
                inbound_encoding=params.inbound_encoding,
                outbound_encoding=params.outbound_encoding,
                auto_hang_up=params.auto_hang_up,
            ),
        )
        super().__init__(serializer=serializer, host=host, port=port)
