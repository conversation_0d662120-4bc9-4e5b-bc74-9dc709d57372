#
# Copyright (c) 2025, Daily
#
# SPDX-License-Identifier: BSD 2-Clause License
#

import os
import yaml
from pathlib import Path
from typing import Optional, Dict, Any, List

import aiohttp
from dotenv import load_dotenv
from loguru import logger
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.frames.frames import LLMMessagesFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.cartesia.tts import CartesiaTTSService
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.deepgram.stt import DeepgramSTTService
from pipecat.transports.services.daily import DailyParams, DailyTransport
from pipecatcloud.agent import DailySessionArguments

# Check if we're in local development mode
LOCAL_RUN = os.getenv("LOCAL_RUN")
if LOCAL_RUN:
    import asyncio
    import webbrowser

    try:
        from local_runner import configure
    except ImportError:
        logger.error("Could not import local_runner module. Local development mode may not work.")

# Load environment variables
load_dotenv(override=True)


def load_flow_yaml(flow_lang: str = "en") -> Dict[str, Any]:
    """Load the YAML flow file for the specified language.
    
    Args:
        flow_lang: Language code ("en" or "es")
        
    Returns:
        Dict containing the parsed YAML flow
    """
    flow_dir = Path(__file__).parent / "flows"
    flow_path = flow_dir / f"intake_{flow_lang}.yaml"
    
    if not flow_path.exists():
        logger.warning(f"Flow file for language '{flow_lang}' not found at {flow_path}. Falling back to English.")
        flow_path = flow_dir / "intake_en.yaml"
    
    with open(flow_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)

async def main(room_url: str, token: str, flow_lang: str = "en", translate_transcript: bool = False):
    """Main pipeline setup and execution function.

    Args:
        room_url: The Daily room URL
        token: The Daily room token
        flow_lang: Language code for the dialogue flow (default: "en")
        translate_transcript: Whether to translate transcripts to English (default: False)
    """
    logger.debug("Starting bot in room: {}", room_url)

    # Configure transcription params based on language and translation flag
    transcription_params = {}
    if translate_transcript and flow_lang != "en":
        # Set Deepgram language to the flow language but request translation to English
        transcription_params = {
            "language": flow_lang,
            "translate": True,
            "target_language": "en"
        }
    elif flow_lang != "en":
        # Just set the language without translation
        transcription_params = {"language": flow_lang}
    
    # Load the appropriate flow based on language
    flow_data = load_flow_yaml(flow_lang)
    
    transport = DailyTransport(
        room_url,
        token,
        "bot",
        DailyParams(
            audio_out_enabled=True,
            transcription_enabled=True,
            vad_enabled=True,
            vad_analyzer=SileroVADAnalyzer(),
            transcription_params=transcription_params,
        ),
    )

    tts = CartesiaTTSService(
        api_key=os.getenv("CARTESIA_API_KEY"), voice_id="79a125e8-cd45-4c13-8a67-188112f4dd22"
    )

    llm = OpenAILLMService(api_key=os.getenv("OPENAI_API_KEY"), model="gpt-4o")

    # Use system prompt from the loaded flow
    system_prompt = flow_data.get("system_prompt", "")
    if not system_prompt:
        # Fallback system prompt if not found in the flow
        system_prompt = "You are a helpful AI receptionist in a WebRTC call. Your goal is to assist callers professionally."
    
    messages = [
        {
            "role": "system",
            "content": system_prompt,
        },
    ]

    context = OpenAILLMContext(messages)
    context_aggregator = llm.create_context_aggregator(context)
    
    # Create Deepgram STT service if needed for specific language handling
    stt = None
    if flow_lang != "en" or translate_transcript:
        stt = DeepgramSTTService(
            api_key=os.getenv("DEEPGRAM_API_KEY"),
            language=flow_lang,
            translate=translate_transcript,
        )
        
        pipeline_components = [
            transport.input(),
            stt,
            context_aggregator.user(),
            llm,
            tts,
            transport.output(),
            context_aggregator.assistant(),
        ]
    else:
        pipeline_components = [
            transport.input(),
            context_aggregator.user(),
            llm,
            tts,
            transport.output(),
            context_aggregator.assistant(),
        ]

    pipeline = Pipeline(pipeline_components)

    # Use config from the flow if available
    config = flow_data.get("config", {})
    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            allow_interruptions=config.get("allow_interruptions", True),
            enable_metrics=config.get("enable_metrics", True),
            enable_usage_metrics=config.get("enable_usage_metrics", True),
            report_only_initial_ttfb=config.get("report_only_initial_ttfb", True),
        ),
    )

    @transport.event_handler("on_first_participant_joined")
    async def on_first_participant_joined(transport, participant):
        logger.info("First participant joined: {}", participant["id"])
        await transport.capture_participant_transcription(participant["id"])
        
        # Get the greeting message from the flow
        greeting_message = ""
        if "flow" in flow_data and "greeting" in flow_data["flow"]:
            greeting_message = flow_data["flow"]["greeting"].get("message", "")
        
        # Fallback greeting if not found in flow
        if not greeting_message:
            if flow_lang == "es":
                greeting_message = "Hola, soy el asistente virtual. ¿Cómo puedo ayudarle hoy?"
            else:
                greeting_message = "Hello, I'm the virtual assistant. How can I help you today?"
        
        # Kick off the conversation with the greeting
        messages.append(
            {
                "role": "system",
                "content": f"Start the conversation with: '{greeting_message}'",
            }
        )
        await task.queue_frames([LLMMessagesFrame(messages)])

    @transport.event_handler("on_participant_left")
    async def on_participant_left(transport, participant, reason):
        logger.info("Participant left: {}", participant)
        await task.cancel()

    runner = PipelineRunner()

    await runner.run(task)


async def bot(args: DailySessionArguments):
    """Main bot entry point compatible with the FastAPI route handler.

    Args:
        args: The Daily session arguments including room_url, token, and config
    """
    logger.info(f"Bot process initialized {args.room_url} {args.token}")
    
    # Extract language and translation settings from config if provided
    flow_lang = "en"
    translate_transcript = False
    
    if args.body and isinstance(args.body, dict):
        flow_lang = args.body.get("flow_lang", "en")
        translate_transcript = args.body.get("translate_transcript", False)
    
    logger.info(f"Using flow language: {flow_lang}, translate_transcript: {translate_transcript}")

    try:
        await main(args.room_url, args.token, flow_lang, translate_transcript)
        logger.info("Bot process completed")
    except Exception as e:
        logger.exception(f"Error in bot process: {str(e)}")
        raise


# Local development functions
async def local_main():
    """Function for local development testing."""
    try:
        # Allow setting language via environment variables for local testing
        flow_lang = os.getenv("FLOW_LANG", "en")
        translate_transcript = os.getenv("TRANSLATE_TRANSCRIPT", "false").lower() == "true"
        
        logger.info(f"Local test using flow language: {flow_lang}, translate: {translate_transcript}")
        
        async with aiohttp.ClientSession() as session:
            (room_url, token) = await configure(session)
            logger.warning("_")
            logger.warning("_")
            logger.warning(f"Talk to your voice agent here: {room_url}")
            logger.warning(f"Language: {flow_lang.upper()}, Translation: {'ON' if translate_transcript else 'OFF'}")
            logger.warning("_")
            logger.warning("_")
            webbrowser.open(room_url)
            await main(room_url, token, flow_lang, translate_transcript)
    except Exception as e:
        logger.exception(f"Error in local development mode: {e}")


# Local development entry point
if LOCAL_RUN and __name__ == "__main__":
    try:
        asyncio.run(local_main())
    except Exception as e:
        logger.exception(f"Failed to run in local mode: {e}")
