"""
Booking Helpers for Voice Agent.

This module provides async helper functions for managing bookings in the voice agent,
enabling the agent to propose available time slots and confirm bookings.

The module includes:
- Slot: Class representing a time slot with start/end times and timezone
- Caller: Class representing a caller requesting a booking
- Booking: Class representing a confirmed booking with status information
- proposeSlot(): Function that returns available time slots
- confirmSlot(): Function that creates a booking and calendar event

Usage:
    slots = await proposeSlot(user_tz="America/Chicago")
    booking = await confirmSlot(slots[0], caller)

See Also:
    - Google Calendar API: https://developers.google.com/calendar/api
    - apps/calendar-svc: Calendar service implementation
    - docs/GOOGLE_CALENDAR_PUSH.md: Documentation for real-time calendar sync
"""
import datetime
from typing import List, Dict, Any, Optional
import httpx
import logging
from datetime import datetime, timedelta
import uuid
import pytz

logger = logging.getLogger(__name__)

# Type definitions as per the spec
class Slot:
    """Represents a time slot for booking.
    
    A Slot encapsulates a start time, end time, and timezone for a potential booking.
    Used by proposeSlot() to return available time slots and by confirmSlot() to create bookings.
    
    Attributes:
        start_time (datetime): Beginning time of the slot with timezone information
        end_time (datetime): Ending time of the slot with timezone information
        timezone (str): IANA timezone identifier (e.g., "America/Chicago")
    """
    def __init__(
        self, 
        start_time: datetime, 
        end_time: datetime, 
        timezone: str
    ):
        """Initialize a time slot.
        
        Args:
            start_time: Beginning time of the slot with timezone information
            end_time: Ending time of the slot with timezone information
            timezone: IANA timezone identifier (e.g., "America/Chicago")
        """
        self.start_time = start_time
        self.end_time = end_time
        self.timezone = timezone
    
    def __str__(self) -> str:
        """Format the slot as a human-readable string.
        
        Returns:
            A formatted string like "2025-05-21 10:00 - 10:30 America/Chicago"
        """
        return f"{self.start_time.strftime('%Y-%m-%d %H:%M')} - {self.end_time.strftime('%H:%M')} {self.timezone}"

class Caller:
    """Represents a caller requesting a booking.
    
    Stores information about the person making a booking, including their name,
    optional email, and optional phone number.
    
    Attributes:
        name (str): Full name of the caller
        email (Optional[str]): Email address of the caller, if available
        phone (Optional[str]): Phone number of the caller, if available
    """
    def __init__(
        self, 
        name: str, 
        email: Optional[str] = None, 
        phone: Optional[str] = None
    ):
        """Initialize a caller.
        
        Args:
            name: Full name of the caller
            email: Email address of the caller (optional)
            phone: Phone number of the caller (optional)
        """
        self.name = name
        self.email = email
        self.phone = phone

class Booking:
    """Represents a confirmed booking in the system.
    
    A Booking is created by the confirmSlot() function after a slot is selected
    and a calendar event is created. It includes information about the time slot,
    the caller, and tracking information like status and calendar event ID.
    
    Attributes:
        id (str): Unique identifier for the booking
        slot (Slot): The time slot for this booking
        caller (Caller): Information about the person making the booking
        calendar_event_id (Optional[str]): ID of the event in the calendar provider's system
        status (str): Current status of the booking (e.g., "pending", "confirmed", "error")
    """
    def __init__(
        self, 
        id: str,
        slot: Slot, 
        caller: Caller, 
        calendar_event_id: Optional[str] = None,
        status: str = "pending"
    ):
        """Initialize a booking record.
        
        Args:
            id: Unique identifier for the booking
            slot: The time slot for this booking
            caller: Information about the person making the booking
            calendar_event_id: ID of the event in the calendar provider's system (optional)
            status: Current status of the booking (defaults to "pending")
        """
        self.id = id
        self.slot = slot
        self.caller = caller
        self.calendar_event_id = calendar_event_id
        self.status = status

async def get_calendar_provider():
    """
    Retrieve the calendar provider that was implemented by Codex Group B.
    
    This function is a wrapper around the actual calendar provider implementation.
    In production, this would import and return the GoogleCalendarProvider that was
    implemented as part of PR #13 "Add Google calendar provider and booking columns".
    
    Returns:
        An object with the following methods:
            - checkAvailability(start_date, end_date, timezone) -> List[Dict[str, Any]]
            - createEvent(event_data) -> Dict[str, Any]
    """
    # In a real implementation, this would import and return the actual calendar provider
    # For now, we'll create a mock provider with the same interface
    
    class MockCalendarProvider:
        async def checkAvailability(self, start_date: datetime, end_date: datetime, timezone: str) -> List[Dict[str, Any]]:
            """Mock implementation of availability checking."""
            # Return an empty busy list, indicating all slots are free
            return []
        
        async def createEvent(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
            """Mock implementation of event creation."""
            # Return a mock event with an ID
            return {
                "id": f"event_{uuid.uuid4()}",
                "status": "confirmed",
                "htmlLink": "https://calendar.google.com/calendar/event?id=mock_event"
            }
    
    return MockCalendarProvider()

async def proposeSlot(user_tz: str = "America/Chicago") -> List[Slot]:
    """
    Return the first 3 free 30-min slots within the next 5 weekdays.
    
    This function retrieves the calendar provider, checks for busy times in the specified
    timezone, and then finds the first 3 available 30-minute slots during business hours
    (9 AM - 5 PM) on weekdays. It skips weekends and any times that are marked as busy
    in the calendar.
    
    Args:
        user_tz: IANA timezone identifier for the user (default: "America/Chicago")
    
    Returns:
        List of available Slot objects, with each slot being 30 minutes in duration.
        The list will contain up to 3 slots, but may contain fewer if fewer are available
        within the next 5 weekdays.
    
    Example:
        ```python
        slots = await proposeSlot("America/New_York")
        for slot in slots:
            print(slot)  # E.g., "2025-05-22 10:00 - 10:30 America/New_York"
        ```
    """
    logger.info(f"Finding available slots in timezone {user_tz}")
    
    # Get the calendar provider
    provider = await get_calendar_provider()
    
    # Get current time in user's timezone
    tz = pytz.timezone(user_tz)
    now = datetime.now(tz)
    
    # Start looking from the beginning of the next business hour
    start_hour = 9  # Business hours start at 9 AM
    end_hour = 17   # Business hours end at 5 PM
    
    # If it's already past business hours, start from tomorrow
    if now.hour >= end_hour:
        start_date = (now + timedelta(days=1)).replace(hour=start_hour, minute=0, second=0, microsecond=0)
    else:
        # Round up to the next hour if within business hours
        if now.hour < start_hour:
            start_date = now.replace(hour=start_hour, minute=0, second=0, microsecond=0)
        else:
            # Round to next half hour
            if now.minute < 30:
                start_date = now.replace(minute=30, second=0, microsecond=0)
            else:
                start_date = (now + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)
    
    # Define the end search date (5 weekdays from now)
    end_date = now + timedelta(days=7)  # Include weekends in search range
    
    # Get busy times from the calendar provider
    busy_times = await provider.checkAvailability(
        start_date=start_date, 
        end_date=end_date,
        timezone=user_tz
    )
    
    # Transform busy times to a more manageable format
    busy_ranges = []
    if busy_times:
        for busy in busy_times:
            # Handle either datetime objects or ISO format strings
            if isinstance(busy.get("start"), str):
                start = datetime.fromisoformat(busy.get("start"))
            else:
                start = busy.get("start")
                
            if isinstance(busy.get("end"), str):
                end = datetime.fromisoformat(busy.get("end"))
            else:
                end = busy.get("end")
                
            # Make sure the busy times have timezone info
            if start.tzinfo is None:
                start = pytz.timezone(user_tz).localize(start)
            if end.tzinfo is None:
                end = pytz.timezone(user_tz).localize(end)
                
            busy_ranges.append((start, end))
    
    # Find available slots (30-minute intervals)
    available_slots = []
    current_date = start_date
    slot_duration = timedelta(minutes=30)
    
    # Continue until we have 3 slots or reach the end date
    while current_date < end_date and len(available_slots) < 3:
        # Skip weekends
        if current_date.weekday() >= 5:  # 5 = Saturday, 6 = Sunday
            current_date = current_date + timedelta(days=1)
            current_date = current_date.replace(hour=start_hour, minute=0, second=0, microsecond=0)
            continue
        
        # Skip non-business hours
        if current_date.hour < start_hour or current_date.hour >= end_hour:
            if current_date.hour >= end_hour:
                # Move to the next day
                current_date = (current_date + timedelta(days=1)).replace(hour=start_hour, minute=0, second=0, microsecond=0)
            else:
                # Move to start of business hours
                current_date = current_date.replace(hour=start_hour, minute=0, second=0, microsecond=0)
            continue
        
        # Check if this slot conflicts with any busy time
        slot_end = current_date + slot_duration
        is_available = True
        
        for busy_start, busy_end in busy_ranges:
            # If there's any overlap, the slot is not available
            if (current_date < busy_end and slot_end > busy_start):
                is_available = False
                # Move the current_date to the end of this busy period
                current_date = busy_end
                break
        
        if is_available:
            # Create a new available slot
            slot = Slot(
                start_time=current_date,
                end_time=slot_end,
                timezone=user_tz
            )
            available_slots.append(slot)
        
        # Move to the next slot
        current_date = current_date + slot_duration
    
    logger.info(f"Found {len(available_slots)} available slots")
    return available_slots

async def confirmSlot(slot: Slot, caller: Caller) -> Booking:
    """
    Confirm a booking by writing to DB and creating a calendar event.
    
    This function takes a selected time slot and caller information, creates a calendar
    event using the calendar provider, and then records the booking in the database. If
    the event creation fails, it will still return a booking object with an "error" status.
    
    Args:
        slot: The time slot to book
        caller: Information about the person making the booking
    
    Returns:
        A Booking object with status information. The status will be "confirmed" if the
        booking was successful, or "error" if there was a problem creating the event.
        The booking object includes the calendar event ID if successful.
    
    Example:
        ```python
        slot = slots[0]  # First proposed slot
        caller = Caller(name="John Doe", email="<EMAIL>")
        booking = await confirmSlot(slot, caller)
        
        if booking.status == "confirmed":
            print(f"Booking confirmed with ID: {booking.id}")
        else:
            print("Booking failed")
        ```
    """
    logger.info(f"Confirming slot for {caller.name}: {slot}")
    
    try:
        # Get the calendar provider
        provider = await get_calendar_provider()
        
        # Create the calendar event
        event_data = {
            "summary": f"Appointment with {caller.name}",
            "description": f"Phone call with {caller.name}",
            "start": {
                "dateTime": slot.start_time.isoformat(),
                "timeZone": slot.timezone
            },
            "end": {
                "dateTime": slot.end_time.isoformat(),
                "timeZone": slot.timezone
            },
            "attendees": []
        }
        
        # Add caller as attendee if email is provided
        if caller.email:
            event_data["attendees"].append({
                "email": caller.email,
                "displayName": caller.name
            })
        
        # Create the event in the calendar
        calendar_event = await provider.createEvent(event_data)
        calendar_event_id = calendar_event.get("id")
        
        # In a real implementation, this would use the Prisma client
        # to write the booking to the database
        booking_id = str(uuid.uuid4())
        
        # Create and return the booking
        booking = Booking(
            id=booking_id,
            slot=slot,
            caller=caller,
            calendar_event_id=calendar_event_id,
            status="confirmed"
        )
        
        logger.info(f"Booking confirmed: {booking_id}, calendar event: {calendar_event_id}")
        return booking
        
    except Exception as e:
        logger.error(f"Error confirming booking: {str(e)}")
        # Return a booking with error status
        return Booking(
            id=str(uuid.uuid4()),
            slot=slot,
            caller=caller,
            status="error"
        )
