import asyncio
import base64
import json
from dataclasses import dataclass
from typing import AsyncGenerator, Optional

import audioop

try:
    from websockets import connect, WebSocketException
except Exception:  # pragma: no cover - optional dependency
    connect = None  # type: ignore
    WebSocketException = Exception  # type: ignore


class VADBase:
    """Minimal voice activity detector interface."""

    async def is_speech(self, pcm: bytes) -> bool:  # pragma: no cover - stub
        return True


@dataclass
class TelnyxParams:
    api_key: str
    sip_username: str
    audio_out_enabled: bool
    vad_enabled: bool
    transcription_enabled: bool
    vad_analyzer: VADBase


class AudioTransport:
    """Base audio transport interface."""

    async def __aenter__(self):
        raise NotImplementedError

    async def __aexit__(self, exc_type, exc, tb):
        raise NotImplementedError

    def __aiter__(self) -> AsyncGenerator[bytes, None]:
        raise NotImplementedError

    async def write_pcm(self, pcm_frames: bytes):
        raise NotImplementedError


def _ulaw_to_pcm(ulaw: bytes) -> bytes:
    """Convert μ-law bytes to 16-kHz 16-bit PCM."""
    lin = audioop.ulaw2lin(ulaw, 2)
    pcm, _ = audioop.ratecv(lin, 2, 1, 8000, 16000, None)
    return pcm


def _pcm_to_ulaw(pcm: bytes) -> bytes:
    """Convert 16-kHz 16-bit PCM to μ-law bytes."""
    down, _ = audioop.ratecv(pcm, 2, 1, 16000, 8000, None)
    ulaw = audioop.lin2ulaw(down, 2)
    return ulaw


class TelnyxTransport(AudioTransport):
    """Simple Telnyx WebSocket transport."""

    def __init__(self, params: TelnyxParams, *, url: Optional[str] = None) -> None:
        self.params = params
        self.url = url
        self._ws = None
        self._queue: asyncio.Queue[Optional[bytes]] = asyncio.Queue()
        self.call_id: Optional[str] = None
        self._recv_task: Optional[asyncio.Task] = None

    async def _connect(self):
        if connect is None:
            raise RuntimeError("websockets package not installed")
        rtc_id = "local" if self.url else "session"
        url = self.url or f"wss://api.telnyx.com/v2/rtcsession/{rtc_id}"
        headers = {"Authorization": f"Bearer {self.params.api_key}"}
        self._ws = await connect(url, additional_headers=headers)
        self._recv_task = asyncio.create_task(self._reader())

    async def __aenter__(self):
        attempts = 0
        delay = 0.1
        while True:
            try:
                await self._connect()
                break
            except WebSocketException as e:  # pragma: no cover - network path
                code = getattr(e, "code", None)
                if code in (1006, 1011) and attempts < 3:
                    await asyncio.sleep(delay)
                    attempts += 1
                    delay *= 2
                    continue
                raise
        return self

    async def _reader(self):
        assert self._ws is not None
        try:
            async for message in self._ws:
                data = json.loads(message)
                event = data.get("event")
                if event == "start":
                    self.call_id = data.get("call_id") or data.get("stream_id")
                elif event == "media":
                    payload = data.get("payload")
                    if payload is None and "media" in data:
                        payload = data["media"].get("payload")
                    if payload:
                        pcm = _ulaw_to_pcm(base64.b64decode(payload))
                        await self._queue.put(pcm)
                elif event == "stop":
                    break
        finally:
            await self._queue.put(None)

    def __aiter__(self) -> AsyncGenerator[bytes, None]:
        async def gen():
            while True:
                chunk = await self._queue.get()
                if chunk is None:
                    break
                yield chunk
        return gen()

    async def write_pcm(self, pcm_frames: bytes):
        if not self.params.audio_out_enabled or self._ws is None:
            return
        ulaw = _pcm_to_ulaw(pcm_frames)
        payload = base64.b64encode(ulaw).decode()
        msg = json.dumps({"event": "media", "media": {"payload": payload}})
        await self._ws.send(msg)

    async def __aexit__(self, exc_type, exc, tb):
        if self._recv_task:
            await self._recv_task
        if self._ws:
            await self._ws.close()
