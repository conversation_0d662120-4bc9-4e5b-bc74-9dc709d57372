"""Voice agent booking functionality."""
from __future__ import annotations

import os
from typing import Optional

from packages.calendar_core import CalendarProvider


class MockCalendarProvider(CalendarProvider):
    """Mock calendar provider for development/testing."""

    def __init__(self, firm_id: str, provider_name: str, capability: Optional[dict] = None):
        # Skip the parent __init__ to avoid auth calls in mock
        self.firm_id = firm_id
        self.provider_name = provider_name
        self.capability = capability or {}
        self.token = "mock-token"

    async def list_calendars(self):
        return [{"id": "primary", "summary": "Mock Calendar"}]

    async def create_event(self, calendar_id: str, event: dict):
        return {"id": "mock-event-123", "htmlLink": "https://calendar.google.com/mock", **event}

    async def get_availability(self, calendar_ids, time_min, time_max):
        return [{"calendar_id": cid, "busy": []} for cid in calendar_ids]


def get_calendar_provider(firm_id: str, provider: str = "google") -> CalendarProvider:
    """
    Get a calendar provider instance for the voice agent.

    Returns real provider instances that use auth-service for token management.
    Falls back to MockCalendarProvider only in test environments.
    """
    # Check if we're explicitly in a test environment
    # Only use mock if TESTING is explicitly set to "true"
    if os.getenv("TESTING") == "true":
        return MockCalendarProvider(firm_id, provider)

    # Use real providers for all other environments (dev, staging, production)
    from packages.calendar_core import get_provider
    return get_provider(firm_id, provider)
