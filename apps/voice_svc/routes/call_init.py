import os
import sys
import hmac
import hashlib
import async<PERSON>
import json
from typing import Optional
from pathlib import Path
from fastapi import APIRouter, Request, HTTPException, BackgroundTasks, Header, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Add project root to sys.path to ensure imports work correctly
project_root = str(Path(__file__).parents[3])
if project_root not in sys.path:
    sys.path.append(project_root)

# Import the bot runner function
from apps.voice_agent.bot_runner import run_pipeline

router = APIRouter()

class TelnyxCallInitRequest(BaseModel):
    """Telnyx webhook payload for call initialization."""
    call_control_id: str
    telnyx_rtc_session_id: str
    lang: Optional[str] = Field(default="en")
    
async def verify_telnyx_signature(
    request: Request,
    signature: str = Header(..., alias="X-Telnyx-Webhook-Signature")
) -> bool:
    """
    Verify Telnyx webhook signature using HMAC-SHA256.
    
    Args:
        request: The FastAPI request object
        signature: The signature from X-Telnyx-Webhook-Signature header
        
    Returns:
        bool: True if signature is valid, False otherwise
    """
    # Get the raw request body
    request_body = await request.body()
    
    secret = os.getenv("TELNYX_SIGNING_SECRET")
    if not secret:
        raise RuntimeError("TELNYX_SIGNING_SECRET environment variable not set")
    
    computed_signature = hmac.new(
        secret.encode(),
        request_body,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(computed_signature, signature)

@router.post("/call-init")
async def call_init(
    request: Request,
    background_tasks: BackgroundTasks,
    is_valid: bool = Depends(verify_telnyx_signature)
):
    """
    Handle Telnyx call initialization webhook.
    
    This endpoint:
    1. Verifies the webhook signature
    2. Extracts call information
    3. Starts an asyncio task to run the voice pipeline
    
    Returns:
        202 Accepted if successful
        403 Forbidden if signature validation fails
    """
    if not is_valid:
        raise HTTPException(status_code=403, detail="Invalid webhook signature")
    
    # Parse the request body
    body = await request.body()
    data = await request.json()
    
    # Extract required fields
    call_data = TelnyxCallInitRequest(**data)
    
    # Start the pipeline in a background task
    background_tasks.add_task(
        run_pipeline,
        call_control_id=call_data.call_control_id,
        telnyx_rtc_session_id=call_data.telnyx_rtc_session_id,
        lang=call_data.lang
    )
    
    return JSONResponse(
        status_code=202,
        content={"message": "Call processing initiated"}
    )
