import asyncio
from typing import Iterable, Optional, Dict, Any


class ResendClient:
    """Lightweight Resend email client wrapper used in tests."""

    def __init__(self, delay: float = 0.05) -> None:
        self.delay = delay

    async def send_email(
        self,
        to: Iterable[str] | str,
        subject: str,
        html: str,
        *,
        from_email: Optional[str] = None,
    ) -> Dict[str, Any]:
        await asyncio.sleep(self.delay)
        payload = {
            "from": from_email or "<EMAIL>",
            "to": list(to) if not isinstance(to, str) else [to],
            "subject": subject,
            "html": html,
        }
        return {"id": "mock_email", "payload": payload}
