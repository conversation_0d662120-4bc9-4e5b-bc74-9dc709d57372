import asyncio
from typing import Optional, Dict, Any


class TelnyxSMS:
    """Lightweight Telnyx SMS client wrapper used in tests."""

    def __init__(self, delay: float = 0.05) -> None:
        self.delay = delay

    async def send_sms(self, to: str, text: str, *, from_number: str) -> Dict[str, Any]:
        await asyncio.sleep(self.delay)
        payload = {
            "from": from_number,
            "to": to,
            "text": text,
        }
        return {"id": "mock_sms", "payload": payload}
