import asyncio
from typing import AsyncGenerator, AsyncIterable

class DeepgramSTT:
    """Mocked Deepgram streaming transcription wrapper."""

    def __init__(self, delay: float = 0.05) -> None:
        self.delay = delay

    async def stream_transcribe(self, audio: AsyncIterable[bytes]) -> AsyncGenerator[str, None]:
        """Yield transcribed text chunks from an audio stream."""
        async for chunk in audio:
            await asyncio.sleep(self.delay)
            yield chunk.decode()
