import asyncio
from typing import AsyncGenerator

class SonicTTS:
    """Mocked Cartesia Sonic text-to-speech wrapper."""

    def __init__(self, delay: float = 0.05) -> None:
        self.delay = delay

    async def stream_synthesize(self, text: str) -> AsyncGenerator[bytes, None]:
        """Yield audio chunks for the given text."""
        for word in text.split():
            await asyncio.sleep(self.delay)
            yield word.encode()
