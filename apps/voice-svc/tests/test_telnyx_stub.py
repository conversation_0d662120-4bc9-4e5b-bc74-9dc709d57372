import asyncio
import json
import pytest

pytest.importorskip("websockets")
from websockets import connect
from tests.fixtures.telnyx_simulator import TelnyxWSSimulator


@pytest.mark.asyncio
async def test_telnyx_ws_fixture(telnyx_ws):
    received = []
    async with connect(telnyx_ws.url) as ws:
        for _ in range(len(telnyx_ws.frames)):
            msg = await ws.recv()
            received.append(json.loads(msg))
    assert received == telnyx_ws.frames
