import os
import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, <PERSON><PERSON>, HTTPEx<PERSON>, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select
import httpx

from packages.shared import models

logger = logging.getLogger(__name__)

DATABASE_URL_ASYNC = os.getenv("DATABASE_URL_ASYNC", "sqlite+aiosqlite:///./test.db")
engine = create_async_engine(DATABASE_URL_ASYNC, future=True)
AsyncSessionLocal = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)


async def get_async_db() -> AsyncSession:
    async with AsyncSessionLocal() as session:
        yield session


async def fetch_event(event_id: str) -> Dict[str, Any]:
    """Fetch event details from Google Calendar API."""
    google_client_id = os.getenv("GOOGLE_CLIENT_ID")
    google_client_secret = os.getenv("GOOGLE_CLIENT_SECRET")
    google_refresh_token = os.getenv("GOOGLE_REFRESH_TOKEN")
    
    # First, get an access token using the refresh token
    async with httpx.AsyncClient() as client:
        token_response = await client.post(
            "https://oauth2.googleapis.com/token",
            data={
                "client_id": google_client_id,
                "client_secret": google_client_secret,
                "refresh_token": google_refresh_token,
                "grant_type": "refresh_token"
            }
        )
        
        if token_response.status_code != 200:
            logger.error(f"Failed to get access token: {token_response.text}")
            return {"id": event_id, "status": "unknown", "updated": datetime.utcnow().isoformat()}
        
        token_data = token_response.json()
        access_token = token_data["access_token"]
        
        # Use the access token to get the event details
        event_response = await client.get(
            f"https://www.googleapis.com/calendar/v3/events/{event_id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        if event_response.status_code != 200:
            logger.error(f"Failed to get event: {event_response.text}")
            return {"id": event_id, "status": "unknown", "updated": datetime.utcnow().isoformat()}
        
        event_data = event_response.json()
        
        # Map Google Calendar status to our booking status
        status_mapping = {
            "confirmed": "confirmed",
            "tentative": "tentative",
            "cancelled": "cancelled"
        }
        
        # Extract relevant data
        event = {
            "id": event_data.get("id"),
            "status": status_mapping.get(event_data.get("status"), "unknown"),
            "updated": event_data.get("updated"),
            "start": event_data.get("start", {}).get("dateTime"),
            "end": event_data.get("end", {}).get("dateTime")
        }
        
        return event


router = APIRouter()


@router.post("/calendar/google/push")
async def google_push(
    request: Request,
    x_goog_channel_token: str = Header(..., alias="X-Goog-Channel-Token"),
    x_goog_resource_state: str = Header(..., alias="X-Goog-Resource-State"),
    x_goog_resource_uri: Optional[str] = Header(None, alias="X-Goog-Resource-URI"),
    x_goog_message_number: Optional[str] = Header(None, alias="X-Goog-Message-Number"),
    x_goog_channel_id: Optional[str] = Header(None, alias="X-Goog-Channel-ID"),
    db: AsyncSession = Depends(get_async_db),
):
    """
    Handle Google Calendar push notifications.
    This endpoint is called by Google when a watched resource changes.
    """
    # Verify the channel token matches our secret
    google_push_token = os.getenv("GOOGLE_PUSH_TOKEN")
    if not google_push_token or x_goog_channel_token != google_push_token:
        logger.warning(f"Invalid channel token: {x_goog_channel_token}")
        raise HTTPException(status_code=403, detail="Invalid channel token")

    # Log notification details
    logger.info(
        f"Received push notification: state={x_goog_resource_state}, "
        f"message={x_goog_message_number}, channel={x_goog_channel_id}"
    )
    
    # Process notification based on resource state
    if x_goog_resource_state in {"exists", "sync", "not_exists", "cancelled"} and x_goog_resource_uri:
        # Extract event ID from resource URI
        event_id = x_goog_resource_uri.rstrip("/").split("/")[-1]
        
        # Fetch the latest event data from Google Calendar
        event = await fetch_event(event_id)
        
        # Find the booking associated with this event
        result = await db.execute(
            select(models.Booking).where(models.Booking.calendar_event_id == event_id)
        )
        booking = result.scalar_one_or_none()
        
        if booking:
            # Update booking based on the event data
            if x_goog_resource_state == "not_exists" or x_goog_resource_state == "cancelled" or event["status"] == "cancelled":
                booking.status = "cancelled"
                logger.info(f"Booking {booking.id} marked as cancelled")
            elif event["status"] in ["confirmed", "tentative"]:
                booking.status = event["status"]
                
                # Update booking times if changed
                if event["start"] and event["end"]:
                    start_time = datetime.fromisoformat(event["start"].replace("Z", "+00:00"))
                    end_time = datetime.fromisoformat(event["end"].replace("Z", "+00:00"))
                    
                    # Only update if times have changed
                    if booking.start_at != start_time or booking.end_ts != end_time:
                        booking.start_at = start_time
                        booking.end_ts = end_time
                        logger.info(f"Booking {booking.id} rescheduled: {start_time} to {end_time}")
            
            # Always update the updated_at timestamp
            booking.updated_at = datetime.utcnow()
            await db.commit()
            
            logger.info(f"Booking {booking.id} updated with status: {booking.status}")
        else:
            logger.warning(f"No booking found for event ID: {event_id}")

    # Additional handling for synchronization messages
    body = await request.json() if request.headers.get("content-type") == "application/json" else {}
    if body:
        logger.debug(f"Notification body: {json.dumps(body)}")

    return {"status": "ok", "resource_state": x_goog_resource_state}
