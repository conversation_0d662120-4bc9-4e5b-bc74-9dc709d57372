"""create core tables"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'

    op.create_table(
        'calls',
        sa.Column('id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, primary_key=True, nullable=False),
        sa.Column('firm_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, nullable=False, index=True),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('ended_at', sa.DateTime(timezone=True)),
        sa.Column('from_number', sa.String()),
        sa.Column('to_number', sa.String()),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        schema=schema
    )

    op.create_table(
        'intake_responses',
        sa.Column('id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, primary_key=True, nullable=False),
        sa.Column('call_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, nullable=False, index=True),
        sa.Column('question', sa.String(), nullable=False),
        sa.Column('answer', sa.String()),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        schema=schema
    )

    op.create_table(
        'bookings',
        sa.Column('id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, primary_key=True, nullable=False),
        sa.Column('firm_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, nullable=False, index=True),
        sa.Column('provider', sa.Enum('google', 'calendly', name='booking_provider_enum', create_type=False), nullable=False),
        sa.Column('external_id', sa.String()),
        sa.Column('booked_at', sa.DateTime(timezone=True)),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        schema=schema
    )

    op.create_table(
        'callback_tasks',
        sa.Column('id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, primary_key=True, nullable=False),
        sa.Column('firm_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, nullable=False, index=True),
        sa.Column('call_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, index=True),
        sa.Column('status', sa.Enum('open', 'done', name='callback_status_enum'), nullable=False, server_default='open'),
        sa.Column('note', sa.String()),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('completed_at', sa.DateTime()),
        schema=schema
    )

    op.create_table(
        'state_meta',
        sa.Column('firm_id', postgresql.UUID(as_uuid=True) if not is_sqlite else sa.String, primary_key=True),
        sa.Column('state_code', sa.CHAR(length=2), nullable=False),
        schema=schema
    )

    if not is_sqlite:
        policy_expr = "((auth.jwt() ->> 'tenant_id'))::uuid"
        tables = ['calls', 'intake_responses', 'bookings', 'callback_tasks', 'state_meta']
        for tbl in tables:
            full = f"tenants.{tbl}"
            op.execute(f"ALTER TABLE {full} ENABLE ROW LEVEL SECURITY")
            op.execute(
                f"CREATE POLICY tenant_select ON {full} FOR SELECT USING (firm_id = {policy_expr})"
            )
            op.execute(
                f"CREATE POLICY tenant_insert ON {full} FOR INSERT WITH CHECK (firm_id = {policy_expr})"
            )


def downgrade():
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'

    op.drop_table('state_meta', schema=schema)
    op.drop_table('callback_tasks', schema=schema)
    op.drop_table('bookings', schema=schema)
    op.drop_table('intake_responses', schema=schema)
    op.drop_table('calls', schema=schema)
