"""add additional booking columns"""
from alembic import op
import sqlalchemy as sa

revision = '0003'
down_revision = '0002'
branch_labels = None
depends_on = None


def upgrade():
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    with op.batch_alter_table('bookings', schema=schema) as batch:
        batch.add_column(sa.Column('caller_name', sa.String()))
        batch.add_column(sa.Column('caller_email', sa.String()))
        batch.add_column(sa.Column('calendar_event_id', sa.String()))
        batch.add_column(sa.Column('end_ts', sa.DateTime(timezone=True)))
        batch.add_column(sa.Column('status', sa.String()))
        batch.add_column(sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')))


def downgrade():
    is_sqlite = op.get_bind().dialect.name == 'sqlite'
    schema = None if is_sqlite else 'tenants'
    with op.batch_alter_table('bookings', schema=schema) as batch:
        batch.drop_column('updated_at')
        batch.drop_column('status')
        batch.drop_column('end_ts')
        batch.drop_column('calendar_event_id')
        batch.drop_column('caller_email')
        batch.drop_column('caller_name')
