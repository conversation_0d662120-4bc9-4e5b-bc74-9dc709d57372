#!/usr/bin/env bash
set -e

# ---------------- Python deps ----------------
if [[ -f requirements.txt ]]; then
  pip install -r requirements.txt
fi
if [[ -f requirements-dev.txt ]]; then
  pip install -r requirements-dev.txt
fi

# ----------------  JS / TS deps  -------------
if [[ -f package-lock.json ]]; then
  npm ci --cache /tmp/npm-cache
elif [[ -f package.json ]]; then
  echo "package-lock.json missing – skipping npm install for Codex"
fi
