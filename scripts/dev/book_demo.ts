import { GoogleCalendarProvider } from '../../packages/calendar/src/google';

async function main() {
  const provider = new GoogleCalendarProvider({
    clientId: process.env.GOOGLE_CLIENT_ID!,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    refreshToken: process.env.GOOGLE_REFRESH_TOKEN!
  });

  const tomorrow = new Date(Date.now() + 24*60*60*1000);
  const start = new Date(tomorrow.setHours(10,0,0,0));
  const end = new Date(tomorrow.setHours(10,30,0,0));

  await provider.createEvent({
    calendarId: 'primary',
    summary: 'Demo Appointment',
    start,
    end
  });
}

main();
