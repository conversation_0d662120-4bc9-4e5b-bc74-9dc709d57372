#!/bin/bash

# CI/CD Validation Script
# This script validates the GitHub Actions workflow configuration

set -e

echo "🔍 Validating CI/CD Configuration..."

# Check if required files exist
echo "📁 Checking required files..."
if [ ! -f ".github/workflows/ci.yml" ]; then
    echo "❌ Missing .github/workflows/ci.yml"
    exit 1
fi
echo "✅ GitHub Actions workflow file exists"

# Validate YAML syntax
echo "📝 Validating YAML syntax..."
if command -v yq &> /dev/null; then
    yq eval '.jobs.deploy.strategy.matrix.include' .github/workflows/ci.yml > /dev/null
    echo "✅ YAML syntax is valid"
else
    echo "⚠️  yq not found, skipping YAML validation"
fi

# Check matrix configuration
echo "🔧 Checking deploy matrix configuration..."
if grep -q "service: ivr" .github/workflows/ci.yml && grep -q "service: calendar" .github/workflows/ci.yml; then
    echo "✅ Deploy matrix includes required services: ivr, calendar"
else
    echo "❌ Deploy matrix missing required services"
    exit 1
fi

# Check Fly app mappings
if grep -q "app: ailex-voice_agent" .github/workflows/ci.yml && grep -q "app: ailex-calendar-svc" .github/workflows/ci.yml; then
    echo "✅ Fly app mappings are configured"
else
    echo "❌ Fly app mappings are missing or incorrect"
    exit 1
fi

# Check docker/login-action version
if grep -q "docker/login-action@v3" .github/workflows/ci.yml; then
    echo "✅ Using docker/login-action@v3"
else
    echo "❌ Not using docker/login-action@v3"
    exit 1
fi

# Check flyctl deploy flags
if grep -q "flyctl deploy --image.*--remote-only" .github/workflows/ci.yml; then
    echo "✅ flyctl deploy uses --image and --remote-only flags"
else
    echo "❌ flyctl deploy missing required flags"
    exit 1
fi

# Check main branch gating
if grep -q "github.ref == 'refs/heads/main'" .github/workflows/ci.yml; then
    echo "✅ Deploy job is gated on main branch"
else
    echo "❌ Deploy job not properly gated on main branch"
    exit 1
fi

# Check FLY_API_TOKEN validation
if grep -q "FLY_API_TOKEN.*secret.*not configured" .github/workflows/ci.yml; then
    echo "✅ FLY_API_TOKEN validation is present"
else
    echo "❌ FLY_API_TOKEN validation is missing"
    exit 1
fi

echo ""
echo "🎉 All validations passed!"
echo ""
echo "📋 Summary of configuration:"
echo "  - Services: ivr → ailex-voice_agent, calendar → ailex-calendar-svc"
echo "  - Docker login: docker/login-action@v3"
echo "  - Deploy flags: --image and --remote-only"
echo "  - Branch gating: main branch only"
echo "  - Secret validation: FLY_API_TOKEN required"
echo ""
echo "🚀 To test with act (if installed):"
echo "  act push -j deploy --secret FLY_API_TOKEN=dummy -n"
