codex: Create core data models & migration for Voice Receptionist

## 1. Context
- DB = Supabase project new-texas-laws, schema = tenants.
- DO NOT modify existing tables:
  • tenants.firms (primary tenant table)
  • tenants.subscriptions
  • tenants.users
  • enum tenants.booking_provider_enum
  Row-level security uses request.jwt.claim.tenant_id.

## 2. New tables to create (depending on investigation in Supabase)
- calls (firm_id FK→firms, started_at, ended_at, …)
- intake_responses (call_id FK→calls, question, answer)
- bookings (firm_id FK→firms, provider ENUM('google','calendly'), …)
- callback_tasks (firm_id FK→firms, status ENUM('open','done'))
- state_meta (firm_id PK→firms, state_code CHAR(2))
Use SQLAlchemy models in packages/shared/models.py, alembic migration in tenants schema.

## 3. Acceptance criteria
- pytest passes with in-memory SQLite.
- alembic upgrade head runs without touching existing objects.
- RLS policy for each new table mirrors existing policy snippet.

## 4. Out-of-scope
- No REST endpoints, no Supabase CLI commands, no changes to existing tables.
