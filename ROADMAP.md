# AI Lex Receptionist - Implementation Roadmap

## Table of Contents
- [Project Overview](#project-overview)
- [Milestones](#milestones)
- [Detailed Tasks](#detailed-tasks)
  - [Week 1: Foundation & Voice Agent](#week-1-foundation--voice-agent)
  - [Week 2: Core Services](#week-2-core-services)
  - [Week 3: Dashboard & Integration](#week-3-dashboard--integration)
  - [Week 4: Polish & Compliance](#week-4-polish--compliance)
  - [Week 5: Beta Testing](#week-5-beta-testing)
  - [Week 6: Launch Preparation](#week-6-launch-preparation)
- [Success Metrics](#success-metrics)
- [Risk Management](#risk-management)
- [Team Responsibilities](#team-responsibilities)

## Project Overview

**Vision:** A 24/7 AI voice receptionist that never lets a call go to voicemail, captures qualified leads, and books meetings in attorneys' calendars, reducing call-handling costs by ≥80% versus human services.

**Target Launch:** 6 weeks from project start

## Milestones

| Week | Focus Area | Key Deliverables |
|------|------------|------------------|
| 1 | Foundation & Voice Agent | - Monorepo setup<br>- Basic voice agent with Telnyx integration<br>- Initial intake flow |
| 2 | Core Services | - Calendar service (Google + Calendly)<br>- Notification system<br>- Basic database schema |
| 3 | Dashboard & Integration | - Admin dashboard v1<br>- Multi-tenant auth<br>- Call logs & analytics |
| 4 | Polish & Shared-Services | - GDPR/HIPAA compliance<br>- Callback task creation<br>- Auth-Service integration<br>- Voicemail fallback system |
| 5 | Beta Testing | - Closed beta with 5 firms<br>- Performance optimization<br>- Bug fixes |
| 6 | Launch | - Production deployment<br>- Documentation<br>- Marketing materials |

## Detailed Tasks

### Week 1: Foundation & Voice Agent

#### 1.1 Project Setup (Day 1-2)
- [x] Initialize monorepo with Turborepo
- [x] Set up Docker and development environment
- [x] Configure CI/CD pipeline (GitHub Actions)
- [x] Add Pipecat Cloud Starter as submodule

#### 1.2 Voice Agent Core (Day 2-4)
- [x] Configure Telnyx WebSocket connection
  - [x] Implement `TelnyxTransport` and `TelnyxParams` classes
  - [x] Set up webhook signature verification
  - [x] Create comprehensive tests for webhook functionality
  - [x] Add documentation in `docs/TELNYX_INTEGRATION.md`
- [x] Implement booking helpers for voice agent
  - [x] Create `proposeSlot()` function to return first 3 free 30-min slots
  - [x] Create `confirmSlot()` function to write booking to DB and create calendar event
  - [x] Implement supporting classes (Slot, Caller, Booking)
  - [x] Add comprehensive unit tests (83% coverage)
  - [x] Add detailed documentation for all functions and classes
- [x] Implement basic call flow in Pipecat YAML
  - [x] Add `language_toggle` flag in intake YAML
  - [x] Add `translate_transcript` flag in intake YAML
  - [x] Implement Deepgram `language` param + OpenAI translation worker if flag true
- [x] Integrate Deepgram for STT
- [x] Set up Cartesia for TTS
- [x] Create initial intake conversation flow:
  - [x] Greeting
  - [x] Name collection
  - [x] Contact information
  - [x] Matter type
  - [x] Urgency assessment

#### 1.3 Initial Testing (Day 5)
- [x] Unit tests for voice agent
  - [x] Implement isolated and integrated tests for webhook functionality
  - [x] Add test coverage for signature verification
  - [x] Implement shared Telnyx WebSocket testing fixtures using aiohttp
  - [x] Achieve 83% test coverage for booking helpers
- [ ] End-to-end call flow test
- [ ] Latency measurement and optimization
  - [ ] Unit test validating P99 latency < 800 ms

### Week 2: Core Services

#### 2.1 Calendar Service (Day 6-10)
- [x] Set up FastAPI service
- [x] Replace Google Calendar OAuth with auth-service integration
   - [x] Use `from ailex_auth import get_access_token` for token retrieval
   - [x] Remove local token storage code
   - [x] Update documentation (see [GOOGLE_OAUTH.md](./docs/GOOGLE_OAUTH.md))
- [x] Replace Calendly OAuth with auth-service integration
   - [x] Use `from ailex_auth import get_access_token` for token retrieval
   - [x] Keep event-type mapping & webhook handling
   - [x] Build robust test infrastructure for integration
   - [x] Update documentation (see [CALENDLY_OAUTH.md](./docs/CALENDLY_OAUTH.md))
- [ ] Complete Calendly integration
   - [ ] Event Type Management
     - [ ] Implement API endpoints to retrieve user's Calendly event types
     - [ ] Add functionality for creating/updating scheduling links
     - [ ] Build system to map event types to specific attorneys or services
     - [ ] Implement filters for event duration, availability, and location types
   - [ ] Webhook Support
     - [ ] Create endpoint to receive Calendly webhook notifications
     - [ ] Implement handlers for `invitee.created` events (new bookings)
     - [ ] Implement handlers for `invitee.canceled` events (canceled bookings)
     - [ ] Implement handlers for `invitee.rescheduled` events (rescheduled bookings)
     - [ ] Update local database when webhook events are received
     - [ ] Send notifications to relevant parties on booking events
   - [ ] Scheduling Link Generation
     - [ ] Create functionality to generate one-time use scheduling links
     - [ ] Implement custom URL parameters for prefilling attendee information
     - [ ] Add support for UTM tracking parameters
- [x] Create unified calendar interface
   - [x] Provider Abstraction
      - [x] Create base `CalendarProvider` abstract class/interface
      - [x] Implement `GoogleCalendarProvider` adapter
      - [x] Add provider stubs for Cronofy and Nylas
      - [x] Create provider factory with `CAL_PROVIDER` env var support
      - [ ] Implement `CalendlyProvider` adapter
      - [ ] Create factory for provider instantiation based on user preferences
      - [ ] Add provider capability detection (features supported by each)
   - [ ] Availability Checking
     - [ ] Create common availability data structure
     - [ ] Implement timezone-aware availability queries
     - [ ] Build aggregation logic for multiple calendars
     - [ ] Add configurable buffer times between appointments
     - [ ] Implement caching strategy for availability data
   - [x] Event Management
      - [x] Create standardized event creation interface
      - [x] Implement event updates across providers
      - [x] Handle provider-specific fields and requirements
      - [x] Add Google Calendar push notification webhook
      - [x] Implement real-time booking status sync with calendar
      - [ ] Implement conflict detection and resolution
- [ ] Implement API Endpoints
   - [ ] Availability Endpoint (`/availability`)
     - [ ] Create query parameter schema for filtering options
     - [ ] Implement multi-provider availability aggregation
     - [ ] Add time slot generation with configurable duration
     - [ ] Incorporate business hours and holiday constraints
     - [ ] Implement response caching for performance
   - [x] Booking Endpoint (`/book`)
      - [x] Create standardized booking request schema
      - [x] Implement validation for required attendee information
      - [ ] Add support for custom intake questions
      - [ ] Create confirmation workflow with notifications
      - [ ] Implement idempotency for booking requests
   - [ ] Management Endpoints
     - [ ] Create endpoints for retrieving booked appointments
     - [ ] Implement cancellation and rescheduling endpoints
     - [ ] Add admin endpoints for bulk operations

#### 2.2 Notification System (Day 9-10)
- [x] Set up Resend for email (DKIM + domain)
- [x] Configure Telnyx for SMS
- [x] Create email templates using MJML or Handlebars supported by Resend
- [x] Implement SMS via Telnyx; trigger on booking & callback-task creation
- [x] Add webhook handlers for delivery status

### Week 3: Testing & Integration

#### 3.1 CI/CD Pipeline (Day 1-2)
- [x] GitHub Actions
  - [x] Set up CI workflow for all services with matrix support for Python and Node.js
  - [x] Configure linting and testing pipelines
  - [x] Set up test coverage reporting with Codecov
  - [x] Implement shared Telnyx WebSocket fixture for consistent testing
- [x] Configure deployment pipeline
  - [x] Create staging and production environments
  - [x] Add automatic deployment to staging
  - [x] Configure manual promotion to production

#### 3.2 Integration Testing (Day 3-4)
- [ ] Implement integration tests for voice agent and calendar service
- [ ] Test notification system
#### 3.2 Multi-tenant Architecture (Day 14-15)
- [x] Create all new tables (`calls`, `intake_responses`, `bookings`, `callback_tasks`, `state_meta`) in **`tenants`** schema
- [x] Apply Row Level Security (RLS) on `firm_id`
- [ ] Set up tenant isolation
- [ ] Configure usage tracking
- [ ] Implement billing webhooks

### Week 4: Polish & Shared-Services

#### 4.1 Compliance Features (Day 16-17)
- [ ] Implement 30-day data retention
- [ ] Add opt-out mechanism
- [ ] Create privacy policy
- [ ] Set up data export/delete flows

#### 4.2 Call Handling (Day 18-19)
- [x] Create `callback_tasks` table; after 'speak to human' intent store task & send SMS/email to attorney
- [ ] Set up voicemail fallback system (only if Pipecat & Telnyx both fail)
- [ ] Add call recording toggle
- [ ] Create call queuing system

#### 4.3 Auth-Service Integration (Day 19-20)
- [ ] Deploy shared FastAPI `auth-service` (Google & Calendly OAuth) on Fly.io
- [ ] Publish lightweight Python client to private PyPI
- [ ] Wire calendar-svc tests to use `ailex_auth` test-token fixture
- [ ] Add **Super-Admin Eval Console** (shared with Core AiLex) to backlog (FastAPI + simple UI)

### Week 5: Beta Testing

#### 5.1 Closed Beta (Day 20-22)
- [ ] Onboard 5 solo attorney firms
- [ ] Beta firms should test bilingual calls and transcript translation
- [ ] Monitor system performance
- [ ] Collect feedback
- [ ] Fix critical issues
- [ ] Implement **Super-Admin Eval Console** from backlog

#### 5.2 Optimization (Day 23-24)
- [ ] Optimize LLM prompts including bilingual prompt tests & translated transcript accuracy eval
- [ ] Reduce latency
- [ ] Improve speech recognition accuracy
- [ ] Enhance error handling

### Week 6: Launch Preparation

#### 6.1 Final Touches (Day 25-26)
- [ ] Complete documentation
- [ ] Add Fly.io blue-green deployment & health-check docs
- [ ] Create user guides
- [ ] Prepare marketing materials
- [ ] Set up analytics
- [ ] Verify KPI widget formulas pull Telnyx CDRs + plan prices to compute savings

#### 6.2 Launch (Day 27-28)
- [ ] Deploy to production
- [ ] Monitor system health
- [ ] Provide support for early adopters
- [ ] Collect initial metrics

## Success Metrics

| Metric | Target | Measurement |
|--------|--------|-------------|
| Call answer rate | >99% | Telnyx analytics |
| Booking conversion | ≥65% | Call to calendar event |
| Average handle time | <3 min | Call logs |
| User satisfaction | ≥4.5/5 | Post-call survey |
| System uptime | 99.9% | Monitoring |
| **Money saved widget accuracy** | ±5% of benchmark | Comparison with actual costs |
| **Translated transcript BLEU** | ≥0.30 | Evaluation against human translation |
| **Callback task SLA** | <2s after intent detected | Application logs |

## Risk Management

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| High call latency | High | Medium | Optimize LLM calls, use CDN |
| Auth-service downtime | High | Low | Implement local token caching fallback |
| Call quality issues | High | Low | Audio processing optimization |
| Data privacy concerns | High | Medium | Regular security audits |
| Integration failures | Medium | High | Circuit breakers, fallbacks |
| Shared Supabase schema conflicts | Medium | Medium | Strict schema versioning, migration testing |

## Dependency List

```bash
# Core
pip install pipecatcloud deepgram-sdk cartesia-sdk
pip install ailex_auth    # shared auth client
pip install resend-sdk    # email
pip install openai        # for translation worker
pip install sqlalchemy alembic pydantic
pip install aiohttp       # for testing WebSocket fixtures
pip install httpx         # for async HTTP requests
pip install pyyaml        # for YAML flow files
npm install @resend/react @radix-ui/react-icons  # dashboard extras
# DevOps
brew install flyctl
```

## Team Responsibilities

### LLM Developer
- Voice agent logic
- Prompt engineering
- Conversation design
- Performance optimization

### Backend Developer
- Calendar service
- Database design
- API development
- System integration

### Frontend Developer
- Admin dashboard
- User interface
- Analytics visualization
- Responsive design

### DevOps
- Infrastructure
- CI/CD pipeline
- Monitoring
- Security compliance

### Product Manager
- Feature prioritization
- User feedback
- Roadmap planning
- Stakeholder communication
