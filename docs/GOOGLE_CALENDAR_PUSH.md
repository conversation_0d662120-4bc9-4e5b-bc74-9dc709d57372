# Google Calendar Push Notifications

This document explains how to set up and use Google Calendar push notifications for real-time synchronization with the AI Lex Receptionist system.

## Overview

Google Calendar push notifications allow our system to receive real-time updates when calendar events are created, updated, or deleted in Google Calendar. This ensures that our booking database stays in sync with the actual calendar state, even when changes are made outside of our application.

## How It Works

1. The system registers a notification channel with Google Calendar API
2. Google sends a POST request to our webhook endpoint when calendar events change
3. Our system processes the notification and updates the booking status in the database
4. The voice agent uses this information to provide accurate booking information

## Setup Instructions

### 1. Configure Environment Variables

Add the following environment variable to your `.env` file:

```
GOOGLE_PUSH_SECRET=your-secure-random-string
```

This secret is used to validate that webhook requests are genuinely from Google.

### 2. Create a Domain Verification File

Google requires domain ownership verification to send push notifications to your webhook. Follow these steps:

1. Go to the [Google Search Console](https://search.console.google.com/)
2. Add your domain and verify ownership
3. Download the verification HTML file
4. Place it at the public root of your domain

### 3. Register a Notification Channel

Use the Google Calendar API to register a notification channel:

```python
from googleapiclient.discovery import build

def register_push_channel(calendar_id):
    service = build('calendar', 'v3', credentials=your_credentials)
    
    body = {
        'id': str(uuid.uuid4()),
        'type': 'web_hook',
        'address': 'https://your-domain.com/calendar/google/push',
        'token': os.getenv('GOOGLE_PUSH_SECRET'),
        'params': {'ttl': '86400'}  # 24 hours
    }
    
    return service.events().watch(calendarId=calendar_id, body=body).execute()
```

This registration expires after 24 hours by default, so you'll need to set up a background task to refresh it.

## Webhook Endpoint

Our system includes a webhook endpoint at `/calendar/google/push` that:

1. Validates the request using the `X-Goog-Channel-Token` header
2. Extracts the event ID from the `X-Goog-Resource-URI` header
3. Determines the action based on the `X-Goog-Resource-State` header
4. Updates the corresponding booking in the database

## Testing

You can test the push notification setup using the FastAPI TestClient:

```python
from fastapi.testclient import TestClient
from your_app import app

client = TestClient(app)

def test_google_push():
    headers = {
        "X-Goog-Channel-Token": "your-secret",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/event123"
    }
    
    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
```

## Integration with Booking Helpers

The push notification system works seamlessly with our booking helpers:

1. `proposeSlot()` - Finds available time slots
2. `confirmSlot()` - Creates bookings and calendar events
3. Push notifications - Keep bookings in sync when calendar events change

This creates a complete round-trip solution for booking management.

## Troubleshooting

### Common Issues

1. **Webhook not receiving notifications**
   - Verify domain ownership in Google Search Console
   - Check that your webhook URL is publicly accessible
   - Make sure your SSL certificate is valid

2. **Authentication failures**
   - Verify that the GOOGLE_PUSH_SECRET environment variable is set
   - Check that the same secret is used in channel registration and validation

3. **Database not updating**
   - Check logs for any errors in the webhook handler
   - Verify that the calendar_event_id in your bookings matches Google's event IDs

### Debugging

Enable debug logging to trace push notification issues:

```python
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('google_calendar_push')
```

## References

- [Google Calendar Push Notifications Documentation](https://developers.google.com/calendar/api/guides/push)
- [Google Domain Verification](https://developers.google.com/search/docs/appearance/site-owners)
