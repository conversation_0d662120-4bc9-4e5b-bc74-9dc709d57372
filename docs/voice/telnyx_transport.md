# Telnyx Transport

This module provides a lightweight Telnyx WebSocket transport used by the voice agent.
It expects the following environment variables:

```env
TELNYX_API_KEY=your-api-key
TELNYX_SIP_USERNAME=your-sip-username
```

## Local Simulator

For local development a simple WebSocket simulator is provided via the test
suite. Start it manually and connect using `curl`:

```bash
python -m tests.transports.conftest
```

Then in another terminal you can interact with it using `curl`:

```bash
curl -i -N "ws://localhost:5678" \
  -H 'Sec-WebSocket-Key: x' \
  -H 'Connection: Upgrade' \
  -H 'Upgrade: websocket'
```

The simulator will send a `start` event followed by a single `media` frame
containing the base64 string `SGVsbG8=` ("Hello") and finally a `stop` event.
