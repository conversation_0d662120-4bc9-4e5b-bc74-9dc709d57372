# Telnyx Voice Webhook Integration

This document describes how to set up and use the Telnyx webhook integration for the ailex-receptionist voice service.

## Overview

The ailex-receptionist voice service integrates with Telnyx for telephony features through WebSocket communication. When a call comes in, Telnyx sends a webhook notification that initiates our voice agent pipeline.

## Webhook Endpoint

The service exposes a single webhook endpoint at:

```
POST /voice/call-init
```

This endpoint:
1. Verifies the Telnyx webhook signature using HMAC-SHA256
2. Extracts call information from the webhook payload
3. Starts an asynchronous task to run the voice pipeline

## Request Format

The webhook expects a JSON payload with the following structure:

```json
{
  "call_control_id": "call_123456789",
  "telnyx_rtc_session_id": "session_987654321",
  "lang": "en" 
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `call_control_id` | string | Yes | The Telnyx Call Control ID |
| `telnyx_rtc_session_id` | string | Yes | The Telnyx RTC Session ID for WebSocket connection |
| `lang` | string | No | Language code for the conversation (default: "en") |

## Security

All webhooks are secured using Telnyx's signature validation:

1. Each webhook request includes an `X-Telnyx-Webhook-Signature` header
2. The signature is an HMAC-SHA256 hash of the request body using your signing secret
3. Requests with invalid signatures receive a 403 Forbidden response

## Configuration

Set the following environment variables:

```
TELNYX_API_KEY=your_telnyx_api_key
TELNYX_SIGNING_SECRET=your_telnyx_signing_secret
TELNYX_SIP_USERNAME=your_telnyx_sip_username
```

## Telnyx Configuration

To configure your Telnyx account:

1. Log in to the [Telnyx Portal](https://portal.telnyx.com/)
2. Navigate to "Messaging" > "Webhooks"
3. Create a new webhook:
   - Set the webhook URL to your deployed endpoint
   - Enable the appropriate events (typically "Call Initiated")
   - Copy the signing secret to your environment variables

## Testing

You can test the webhook locally using the included simulator:

```bash
# Run the tests
pytest -q tests/voice_svc/test_call_init.py
```

The simulator uses a mock WebSocket server to simulate Telnyx WebSocket connections without making actual API calls.

## WebSocket Communication

The voice service connects to Telnyx via WebSockets (using the `websockets` library ≥12.0) to maintain a bidirectional audio stream for the call. The transport handles:

- Media stream conversion (μ-law to PCM)
- Call control protocol
- Streaming audio to STT and from TTS services

## Performance Requirements

- The full STT/TTS pipeline must complete in under 800ms to maintain natural conversation
- Tests verify that end-to-end latency meets this requirement

## Multilingual Support

The webhook supports language selection via the `lang` parameter:

- "en" - English (default)
- "es" - Spanish

When a non-English language is selected, the voice flows automatically enable:
- `language_toggle` for appropriate voice model selection
- `translate_transcript` for transcript translation if needed

## Troubleshooting

Common issues:

1. **Invalid Signature (403)**: Ensure your TELNYX_SIGNING_SECRET matches the one in your Telnyx portal
2. **Connection Failures**: Check that your firewall allows WebSocket connections to Telnyx
3. **Audio Quality Issues**: Verify network bandwidth and latency between your server and Telnyx
