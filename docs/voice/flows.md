# Multilingual Voice Agent Flows

This document explains how to work with the multilingual conversation flows in the AI Lex Receptionist project.

## Overview

The AI Lex voice agent supports multiple languages through YAML-defined conversation flows. Each language has its own YAML file that defines the conversation structure, prompts, and logic. The system can dynamically switch between languages based on caller preferences and supports transcript translation.

## Flow Structure

Conversation flows are defined in YAML files located in `apps/voice_agent/flows/`. Each flow file follows this naming convention:

```
intake_{lang}.yaml
```

Where `{lang}` is the ISO 639-1 language code (e.g., `en` for English, `es` for Spanish).

### Key Components of a Flow File

Each flow YAML file contains:

1. **Metadata** - Information about the flow including language code
2. **Configuration** - Settings for the conversation pipeline
3. **System Prompt** - Instructions for the LLM handling the conversation
4. **Flow States** - The conversation flow states and transitions

Example structure:

```yaml
version: "1.0"
metadata:
  name: "AI Lex Intake Flow - English"
  description: "Handles initial intake conversation for law firm callers in English"
  language: "en"

config:
  allow_interruptions: true
  enable_metrics: true
  enable_usage_metrics: true

system_prompt: |
  You are <PERSON><PERSON><PERSON>, a professional and friendly AI receptionist for a law firm...

flow:
  greeting:
    message: |
      Hello, thank you for calling...
    next: collect_name
  
  # Additional states...
```

## Adding a New Language

To add a new language to the system, follow these steps:

1. **Create a New Flow File**:
   - Copy an existing flow file (e.g., `intake_en.yaml`) to `intake_{your_lang}.yaml`
   - Update the metadata section with the new language code
   - Translate all prompts and messages

2. **Validate the Flow Structure**:
   - Ensure the new flow has the same states as the other flows
   - Keep the same state names and flow logic
   - Only change the actual text/prompts

3. **Test the New Flow**:
   - Use the test suite to validate your flow
   - Check that the flow can be loaded
   - Verify that language selection works correctly

## Language Selection and Translation

The voice agent supports two key language features:

1. **Flow Language Selection**:
   - Controls which language flow is used for the conversation
   - Determined by the `flow_lang` parameter
   - Default is English (`en`)

2. **Transcript Translation**:
   - Translates caller's speech to English in real-time
   - Enabled by the `translate_transcript` flag
   - Useful for agents who understand English but need to communicate in other languages

### How to Use Language Features

#### API Usage

When calling the bot API, include language parameters:

```json
{
  "flow_lang": "es",
  "translate_transcript": true,
  "other_params": "..."
}
```

#### Local Testing

For local development, set environment variables:

```bash
FLOW_LANG=es TRANSLATE_TRANSCRIPT=true python -m apps.voice_agent.bot
```

## Language Support Details

### Currently Supported Languages

- English (`en`) - Default language
- Spanish (`es`) - Complete support with dedicated flow

### Language Implementation Notes

- **English**: Primary language with full feature support
- **Spanish**: Full support with translated prompts
- Additional languages can be added following the pattern

### Deepgram Language Support

The voice agent uses Deepgram for speech recognition. The following parameters are set automatically based on the flow language:

- For non-English flows: `language` parameter is set to the flow language code
- When translation is enabled: `translate` is set to `true` and `target_language` to `en`

## Technical Implementation

### Key Files

- `apps/voice_agent/bot.py` - Main bot implementation with language handling
- `apps/voice_agent/flows/*.yaml` - Language-specific flow definitions
- `tests/flows/test_switch.py` - Tests for language switching functionality

### Runtime Selection Logic

The language selection follows this process:

1. `flow_lang` parameter determines which YAML flow to load
2. If the requested language is not available, falls back to English
3. Based on language and translation settings, configures Deepgram STT parameters
4. Builds the appropriate pipeline for handling the selected language

## Troubleshooting

### Common Issues

1. **Flow File Not Found**:
   - Check that the flow file is in the correct location
   - Verify the file naming follows the `intake_{lang}.yaml` pattern

2. **Inconsistent Flow Structure**:
   - Ensure all language flows have the same states and logic
   - Only translate the text, not the state names or logic

3. **Deepgram Language Support**:
   - Verify that Deepgram supports the language you're adding
   - Check API documentation for language code requirements

4. **Translation Issues**:
   - Make sure `DEEPGRAM_API_KEY` is set in your environment
   - Verify that translation parameters are correctly configured

### Testing Your Configuration

Run the test suite to validate language support:

```bash
pytest tests/flows/test_switch.py -v
```

## Best Practices

1. **Maintain Flow Parity**:
   - Keep all language flows functionally identical
   - Ensure all states exist in all language versions

2. **Culturally Appropriate Translations**:
   - Adapt greetings and language to be culturally appropriate
   - Consider local conventions for formality and etiquette

3. **Regular Testing**:
   - Test all supported languages regularly
   - Verify translation quality and accuracy

4. **Documentation**:
   - Document language capabilities in user-facing documentation
   - Keep this guide updated with new language additions
