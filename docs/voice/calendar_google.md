# Google Calendar Integration

This package uses the `@googleapis/calendar` library to interact with Google Calendar.

## Setup

1. Create a Google Cloud project and enable the Calendar API.
2. Configure OAuth client credentials and obtain a refresh token.
3. Provide `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`, and `GOOGLE_REFRESH_TOKEN` as environment variables.

Required scopes:

- `https://www.googleapis.com/auth/calendar.events`
- `https://www.googleapis.com/auth/calendar.settings.readonly`
- `https://www.googleapis.com/auth/calendar`

## Usage

Run the demo script to create a test event:

```bash
pnpm ts-node scripts/dev/book_demo.ts
```

## Push Notifications

Google Calendar Push Notifications allow our application to receive real-time updates when calendar events change (creation, modification, cancellation).

### How It Works

1. We register a "watch" on the Google Calendar resource we want to monitor
2. Google sends HTTP notifications to our webhook endpoint when changes occur
3. Our application processes these notifications and updates bookings accordingly
4. Watches expire after 7 days and must be renewed

### Required Environment Variables

- `GOOG<PERSON>_PUSH_TOKEN`: Secret token for verifying Google's push notifications
- `WEBHOOK_URL`: Publicly accessible HTTPS URL for Google to send notifications to

### Implementation Details

1. **Webhook Handler**: Located at `/calendar/google/push`, this endpoint receives and processes notifications from Google
2. **Renewal Script**: `scheduler/renew_push.py` - Run this every 6 days to keep the watch active
3. **Synchronization**: Bookings are automatically updated when events are cancelled or rescheduled

### Setting Up Push Notifications

1. Configure your environment variables:

```bash
GOOGLE_PUSH_TOKEN=your-secret-token
WEBHOOK_URL=https://your-domain.com/calendar/google/push
```

2. Run the renewal script to create the initial watch:

```bash
python scheduler/renew_push.py
```

3. Set up a cron job to run the renewal script every 6 days

```bash
# Run at midnight every 6 days
0 0 */6 * * cd /path/to/ailex-receptionist && python scheduler/renew_push.py
```

### Event State Changes

The webhook handles these state changes:

- `cancelled`: Event was deleted or cancelled - updates booking status to "cancelled"
- `confirmed`/`tentative`: Event was created or updated - updates booking status and times
- `exists`: General update to the event - checks for time/status changes
