# AI Lex Receptionist - System Overview

## Introduction
The AI Lex Receptionist is a comprehensive system designed to automate reception tasks, including call handling, appointment booking, and client communication. It integrates with calendar services, provides voice interaction capabilities, manages bookings, and sends notifications through multiple channels.

## System Architecture

The system is organized into several microservices and packages:

```
ailex-receptionist/
├── apps/
│   ├── calendar-svc/      # Calendar service with booking API
│   ├── voice-svc/         # Voice service with telephony integration
│   └── notifier/          # Notification service for email and SMS
├── packages/
│   ├── shared/            # Shared models and utilities
│   └── calendar_core/     # Calendar provider interfaces and adapters
├── email_templates/       # Email templates for notifications
└── tests/                 # Test suites for all components
```

## Core Components

### 1. Calendar Integration

The calendar integration system provides a unified interface to multiple calendar providers.

#### Key Features
- **Abstract Calendar Provider Interface**: Common interface for all calendar providers
- **Provider Adapters**: 
  - Google Calendar integration
  - Calendly integration
- **Provider Factory**: `get_provider()` function for instantiating appropriate providers
- **Capability Configuration**: JSON-based capability matrix defining provider features

#### Implementation Details
- Location: `packages/calendar_core/`
- Design Pattern: Adapter pattern and factory pattern
- Authentication: Handled through a separate module (`ailex_auth`)
- Operations: `list_calendars()`, `create_event()`, `update_event()`, `get_event()`

### 2. Booking System

The booking system manages appointment data and provides API endpoints for booking operations.

#### Key Features
- **Booking Model**: ORM model for storing booking information
- **API Endpoints**:
  - `/booking/availability` - Check time slot availability
  - `/booking/firm/{firm_id}` - Get bookings for a specific firm
  - `/booking/provider/{provider}` - Get bookings by provider type
- **Webhooks**: Real-time updates from calendar providers
  - Calendly webhook endpoint with signature validation
  - Event handlers for `invitee.created` and `invitee.canceled` events

#### Implementation Details
- Location: `apps/calendar-svc/`
- Database: SQLAlchemy ORM with Alembic for migrations
- Framework: FastAPI
- Security: Rate limiting and authentication requirements
- Monitoring: Metrics tracking via statsd client

### 3. Voice Service

The voice service enables telephony integration with speech processing capabilities.

#### Key Features
- **Telephony Integration**: Telnyx WebSocket for real-time call handling
- **Speech Processing**:
  - Speech-to-Text (STT) via Deepgram
  - Text-to-Speech (TTS) via Sonic
- **Multi-language Support**:
  - Language toggle parameter (defaults to English)
  - Optional transcript translation
- **Conversation Flows**: Defined in YAML configuration files

#### Implementation Details
- Location: `apps/voice-svc/`
- Performance: End-to-end latency below 800ms for voice interactions
- Testing: WebSocket simulator for Telnyx testing
- Framework: Pipecat conversation flow system

### 4. Notification System

The notification system handles sending communications via multiple channels.

#### Key Features
- **Email Notifications**: 
  - Resend service client for delivery
  - Responsive email templates built with MJML
- **SMS Notifications**: 
  - Telnyx SMS service integration
  - Consistent interface with email client
- **Templates**: Pre-built templates for common notifications (intake, etc.)

#### Implementation Details
- Location: `apps/notifier/`
- Implementation: Asynchronous clients with proper error handling
- Testing: Snapshot testing for payload verification

## Database Models

### Core Models
- **Booking**: Stores appointment information
  - Calendar provider integration (Google, Calendly)
  - Includes fields for tracking external provider events
  - Indexed for efficient querying (`idx_bookings_firm_start`)
- **Call**: Tracks call information
  - Records call status and relevant metadata
  - Links to bookings when applicable

### Database Management
- SQLAlchemy ORM for model definitions
- Alembic for database migrations
- Sequential versioning (0001, 0002, etc.)

## Security Considerations

- **Authentication**: Required for API endpoints
- **Rate Limiting**: Prevents API abuse
- **Webhook Validation**: Signature verification for incoming webhooks
- **Environment Configuration**: Secure storage of API keys and secrets

## Testing Infrastructure

- **Unit Tests**: Comprehensive test coverage
- **Integration Tests**: End-to-end testing of components
- **Test Fixtures**:
  - Telnyx WebSocket simulator
  - Calendar provider mocks
  - Sample webhook payloads
  - Database fixtures

## Performance Considerations

- **Asynchronous Design**: Throughout all components
- **Low Latency**: Voice processing under 800ms
- **Metrics Tracking**: Via statsd for monitoring

## Future Expansion

The modular design allows for easy addition of:
- Additional calendar providers
- New notification channels
- Enhanced voice capabilities
- More sophisticated booking features
