# Shared Supabase Schema Facts

## Overview
This document outlines the structure of the shared Supabase `tenants` schema used by the AiLex platform, which our Voice Receptionist will integrate with.

## Existing Tables & Key Columns

### Core Tenant Tables
- **tenants.firms** - Primary tenant table
  - `id` uuid PRIMARY KEY (default: gen_random_uuid())
  - `tenant_id` uuid NOT NULL
  - `name` text NOT NULL
  - `primary_email` text NOT NULL
  - `phone` text NOT NULL
  - `address` jsonb NOT NULL
  - `status` text DEFAULT 'active'
  - `created_at` timestamptz DEFAULT now()
  - `updated_at` timestamptz DEFAULT now()
  - + additional fields (state_bar_number, firm_type, etc.)

- **tenants.users**
  - Referenced in RLS policies for tenant-specific actions

- **tenants.subscriptions** (referenced in task)
  - Contains plan pricing data needed for KPI widgets

### Other Notable Tables
The schema contains 50+ tables including:
- `tenants.subscription_plans`
- `tenants.tenant_quotas`
- `tenants.activities`
- `tenants.voice_agent_configs`

## Existing Enums
- `tenants.insight_status` ('new', 'resolved', 'snoozed')
- `tenants.insight_category` ('calendar', 'coaching', 'efficiency', 'risk', 'task')

Note: The `booking_provider_enum` mentioned in requirements was not found in the current schema and will need to be created.

## JWT Claim Used in RLS
Row-level security policies consistently use:
```sql
tenant_id = ((auth.jwt() ->> 'tenant_id'::text))::uuid
```

Alternative formats also appear:
```sql
tenant_id = (( SELECT ((current_setting('request.jwt.claims'::text, true))::jsonb ->> 'tenant_id'::text)))::uuid
```

## Naming Conventions
- Tables: snake_case, plural (e.g., `firms`, `users`, `activities`)
- Columns: snake_case (e.g., `tenant_id`, `created_at`)
- Primary keys: typically `id` (uuid)
- Foreign keys: typically `[entity]_id` (e.g., `tenant_id`, `firm_id`)
- Timestamps: `created_at`, `updated_at`
- Enums: snake_case (e.g., `insight_status`)
- All tables in the schema: prefixed with `tenants.`
- RLS policies: descriptive phrases (e.g., "Allow users to view activities in their tenant")
