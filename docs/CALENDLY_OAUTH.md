# Calendly OAuth Integration

This document outlines the Calendly OAuth 2.0 implementation for the calendar service, enabling secure access to Cale<PERSON>ly's scheduling APIs.

## Overview

The Calendly OAuth integration allows users to connect their Calendly accounts to the AI Lex Receptionist system. This enables the system to:

- Retrieve available event types
- Create scheduling links
- Check scheduled events
- Manage calendar availability

## Prerequisites

1. **Calendly Developer Account**
   - Sign up at [Calendly Developer Portal](https://developer.calendly.com/)
   - Create a new OAuth application

2. **Required Permissions**
   - `event_types:read` - Read event types
   - `scheduling_links:read` - View scheduling links
   - `scheduled_events:read` - View scheduled events
   - `organization:read` - Read organization details
   - `user:read` - Read user profile

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
# Calendly OAuth
CALENDLY_CLIENT_ID=your-client-id
CALENDLY_CLIENT_SECRET=your-client-secret
CALENDLY_REDIRECT_URI=https://your-domain.com/api/v1/oauth/calendly/callback
```

### OAuth Application Setup

1. Go to [Calendly Developer Portal](https://developer.calendly.com/)
2. Create a new OAuth application
3. Set the following redirect URIs:
   - Development: `http://localhost:8000/api/v1/oauth/calendly/callback`
   - Production: `https://your-domain.com/api/v1/oauth/calendly/callback`
4. Copy the Client ID and Client Secret to your `.env` file

## Implementation Details

### OAuth Flow

The implementation follows the OAuth 2.0 authorization code flow:

1. User initiates the OAuth flow from the dashboard
2. System generates an OAuth state token and redirects to Calendly's authorization page
3. User authenticates with Calendly and grants permissions
4. Calendly redirects back to our callback URL with an authorization code
5. System exchanges the code for access and refresh tokens
6. Tokens are securely stored in the database

### Key Components

1. **CalendlyService** (`app/services/calendly_service.py`)
   - Handles OAuth flow and token management
   - Provides methods for interacting with Calendly's API
   - Manages token refresh automatically

2. **API Endpoints** (`app/api/v1/endpoints/calendar.py`)
   - `/oauth/calendly/authorize` - Initiates the OAuth flow
   - `/oauth/calendly/callback` - Handles the OAuth callback

## Usage

### Initiating OAuth Flow

1. Redirect the user to:
   ```
   GET /api/v1/oauth/calendly/authorize?user_id={user_id}
   ```

2. After successful authentication, Calendly will redirect to the callback URL with an authorization code.

### Handling the Callback

The callback URL will receive the authorization code which should be exchanged for tokens:

```
GET /api/v1/oauth/calendly/callback?code={code}&state={state}
```

### Example: Getting Event Types

```python
from app.services.calendly_service import calendly_service

# Get the authenticated client
client = await calendly_service.get_authenticated_client(token)

# Get event types
event_types = await calendly_service.get_event_types(token)
```

## Security Considerations

1. **State Parameter**
   - All OAuth requests include a state parameter to prevent CSRF attacks
   - The state is signed and includes a timestamp for expiration

2. **Token Storage**
   - Access tokens are encrypted at rest
   - Refresh tokens are stored securely
   - Tokens are only sent over HTTPS

3. **Rate Limiting**
   - OAuth endpoints are rate limited to prevent abuse
   - Failed authentication attempts are logged and monitored

## Error Handling

Common error scenarios and their resolutions:

| Error | Cause | Resolution |
|-------|-------|------------|
| `invalid_grant` | Expired or invalid refresh token | Re-authenticate the user |
| `invalid_request` | Missing or invalid parameters | Check request parameters |
| `unauthorized_client` | Client authentication failed | Verify client credentials |
| `access_denied` | User denied permission | Inform user about required permissions |

## Testing

### Local Testing

1. Set up a local tunnel (e.g., ngrok) to expose your local server:
   ```bash
   ngrok http 8000
   ```

2. Update your Calendly OAuth application with the ngrok URL:
   - Redirect URIs: `https://your-ngrok-subdomain.ngrok.io/api/v1/oauth/calendly/callback`

3. Update your `.env` file with the ngrok URL:
   ```env
   CALENDLY_REDIRECT_URI=https://your-ngrok-subdomain.ngrok.io/api/v1/oauth/calendly/callback
   ```

### Test Infrastructure

The following fixes have been implemented to support automated testing of the Calendly OAuth integration:

#### Database Configuration

1. **Cross-Database Compatibility**
   - Made models compatible with both PostgreSQL (production) and SQLite (testing)
   - Dynamically selects appropriate column types:
     ```python
     # In models
     is_test = os.environ.get('ENVIRONMENT') == 'test'
     JsonType = JSON if is_test else JSONB
     ```
   - Conditionally creates database indexes based on the database engine

2. **Asynchronous Database Support**
   - Added async SQLite support for testing with `aiosqlite` package
   - Configured test database URL: `sqlite+aiosqlite:///./test.db`
   - Set up proper async session factories

#### Configuration and Environment

1. **Default Configuration Values**
   - Added fallbacks for all settings to support testing:
     ```python
     getattr(settings, 'SETTING_NAME', 'default_value')
     ```

2. **Robust Environment Detection**
   - Updated environment variable handling to be more resilient
   - Environment-specific configuration via `APP_ENV` setting

#### FastAPI Application Setup

1. **Created `create_app()` Function**
   - Implemented a factory function to create configured FastAPI instances
   - Centralized middleware configuration
   - Made endpoints consistent across testing and production

2. **Database Session Management**
   - Added `session.py` module for compatibility with test expectations
   - Proper cleanup of database resources after tests

#### Error Handling

1. **Enhanced OAuth Error Handling**
   - Added `OAuthError` exception class
   - Better error messages for OAuth-related failures
   - Improved exception hierarchy

#### Dependencies

Required packages for testing:
   - `python-jose` - JWT token handling
   - `passlib` - Password hashing
   - `aiosqlite` - Async SQLite support
   - `pytest` - Testing framework

#### Test Results

The test suite for Calendly OAuth integration includes 8 tests:
- 7 tests pass successfully
- 1 test fails (`test_refresh_token_failure`), which may be intentional for a negative test case

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**
   - Ensure the redirect URI in your Calendly application matches exactly with what's in your `.env` file
   - Include/exclude the trailing slash as needed

2. **Invalid Scope**
   - Verify the requested scopes are correctly configured in the Calendly application
   - Check that the scopes are properly formatted

3. **Token Refresh Failures**
   - Ensure the refresh token hasn't been revoked by the user
   - Check that the client ID and secret are correct

## Related Documentation

- [Calendly API Documentation](https://developer.calendly.com/api-docs)
- [Calendly OAuth 2.0 Guide](https://developer.calendly.com/api-docs/01-getting-started/authentication)
- [OAuth 2.0 Security Best Practices](https://datatracker.ietf.org/doc/html/draft-ietf-oauth-security-topics)
