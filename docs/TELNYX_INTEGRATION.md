# Telnyx Integration Guide

This document outlines how the AiLex Receptionist integrates with Telnyx for telephony features, including webhook handling, signature verification, and WebSocket connections.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Webhook Implementation](#webhook-implementation)
- [Signature Verification](#signature-verification)
- [Transport Implementation](#transport-implementation)
- [Configuration](#configuration)
- [Testing](#testing)

## Architecture Overview

The AiLex Receptionist uses Telnyx for telephony services with these key components:

1. **Webhook Endpoint**: Receives call initialization events from Telnyx.
2. **Signature Verification**: Validates webhook requests for security.
3. **WebSocket Transport**: Handles real-time audio streaming with Telnyx.
4. **Pipecat Integration**: Processes voice data through AI conversational pipelines.

## Webhook Implementation

The webhook endpoint (`/voice/call-init`) in `apps/voice_svc/routes/call_init.py` handles incoming call events:

```python
@router.post("/call-init")
async def call_init(
    request: Request,
    background_tasks: BackgroundTasks,
    is_valid: bool = Depends(verify_telnyx_signature)
):
    # Process call initialization and start background tasks
```

When Telnyx sends a webhook event to this endpoint:
1. The signature is verified using HMAC-SHA256
2. If valid, the call data is extracted
3. A background task starts the voice pipeline
4. A 202 Accepted response is returned

## Signature Verification

Webhook signatures are verified using HMAC-SHA256:

```python
async def verify_telnyx_signature(
    request: Request,
    signature: str = Header(..., alias="X-Telnyx-Webhook-Signature")
) -> bool:
    # Get the raw request body
    request_body = await request.body()
    
    secret = os.getenv("TELNYX_SIGNING_SECRET")
    if not secret:
        raise RuntimeError("TELNYX_SIGNING_SECRET environment variable not set")
    
    computed_signature = hmac.new(
        secret.encode(),
        request_body,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(computed_signature, signature)
```

This function:
1. Extracts the raw request body bytes
2. Computes an HMAC-SHA256 signature using your Telnyx signing secret
3. Uses constant-time comparison to validate against the signature provided in the header

## Transport Implementation

The `TelnyxTransport` class in `apps/voice_agent/transports/telnyx_transport.py` serves as a thin wrapper around Pipecat's WebSocket and serialization components:

```python
class TelnyxTransport(WebsocketServerTransport):
    """
    Start a local WS server that speaks the Telnyx Media-Streams protocol.
    """
    def __init__(
        self,
        params: TelnyxParams,
        *,
        host: str = "0.0.0.0",
        port: int = 8765,
    ):
        serializer = TelnyxFrameSerializer(
            stream_id=params.stream_id,
            outbound_encoding=params.outbound_encoding,
            inbound_encoding=params.inbound_encoding,
            call_control_id=params.call_control_id,
            api_key=params.api_key,
            params=TelnyxFrameSerializer.InputParams(
                sample_rate=params.sample_rate,
                inbound_encoding=params.inbound_encoding,
                outbound_encoding=params.outbound_encoding,
                auto_hang_up=params.auto_hang_up,
            ),
        )
        super().__init__(serializer=serializer, host=host, port=port)
```

This class:
1. Accepts parameters from the Telnyx webhook JSON
2. Creates a `TelnyxFrameSerializer` to translate between Telnyx's binary audio format and Pipecat frames
3. Sets up a WebSocket server to accept the connection from Telnyx

The companion `TelnyxParams` class models the expected parameters:

```python
class TelnyxParams(BaseModel):
    # Values that come from the Telnyx webhook JSON
    stream_id: str = Field(..., description="`stream_id` from media.stream_started")
    call_control_id: Optional[str] = Field(
        None, description="`call_control_id` (needed if you auto-hang-up)"
    )
    api_key: Optional[str] = Field(
        None,
        description="Telnyx secret key – only required if you let Pipecat hang up",
    )

    # Codec / sample-rate (override only if you know what you're doing)
    inbound_encoding: str = "PCMU"
    outbound_encoding: str = "PCMU"
    sample_rate: int = 8_000  # Telnyx always streams μ-law @ 8 kHz

    auto_hang_up: bool = True
```

## Configuration

The following environment variables are required:

```
TELNYX_SIGNING_SECRET=your_webhook_signing_secret
TELNYX_PUBLIC_KEY=optional_but_recommended
PORT=8765  # Default port for the WebSocket server
```

## Testing

Two test approaches are available:

1. **Complete Isolation Test**: `tests/voice_svc/test_complete_isolation.py`
   - Tests just the webhook and signature verification logic
   - Does not require the full application stack
   - Ideal for unit testing HTTP behavior

2. **Integrated Test**: `tests/voice_svc/test_working_webhook.py`
   - Tests the webhook with mocked pipeline components
   - Validates proper request handling and status codes
   - Exercises the webhook with valid and invalid signatures

To run the tests:

```bash
# Run isolated tests
TELNYX_SIGNING_SECRET=test-signing-secret python -m pytest -xvs tests/voice_svc/test_complete_isolation.py

# Run integrated tests
TELNYX_SIGNING_SECRET=test-signing-secret python -m pytest -xvs tests/voice_svc/test_working_webhook.py
```
