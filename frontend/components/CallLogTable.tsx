import React, { useEffect, useState } from 'react';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from 'shadcn/ui';

export interface CallLog {
  id: string;
  date: string;
  caller: string;
  duration: number;
  transcriptUrl: string;
  costSaved: number;
}

export const CallLogTable: React.FC = () => {
  const [logs, setLogs] = useState<CallLog[]>([]);

  useEffect(() => {
    const fetchLogs = async () => {
      try {
        const res = await fetch('/api/call-log');
        if (!res.ok) throw new Error('Failed to fetch');
        const data = await res.json();
        setLogs(data);
      } catch (err) {
        console.error(err);
      }
    };
    fetchLogs();
  }, []);

  const exportCsv = () => {
    const rows = [
      ['Date', 'Caller', 'Duration', 'Transcript', 'Cost Saved'],
      ...logs.map(l => [l.date, l.caller, l.duration.toString(), l.transcriptUrl, l.costSaved.toString()])
    ];
    const csv = rows.map(r => r.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'call-log.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div>
      <button onClick={exportCsv} data-testid="export-csv">Export CSV</button>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Caller</TableHead>
            <TableHead>Duration</TableHead>
            <TableHead>Transcript</TableHead>
            <TableHead>Cost Saved ($)</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logs.map(log => (
            <TableRow key={log.id} data-testid="call-row">
              <TableCell>{log.date}</TableCell>
              <TableCell>{log.caller}</TableCell>
              <TableCell>{log.duration}</TableCell>
              <TableCell>
                <a href={log.transcriptUrl}>View</a>
              </TableCell>
              <TableCell>{log.costSaved}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default CallLogTable;
