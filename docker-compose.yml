version: "3.8"

services:
  calendar-svc:
    build: ./apps/calendar-svc
    ports:
      - "8000:8000"
    env_file: .env
    environment:
      - DATABASE_URL=**************************************/calendar_db
    volumes:
      - ./apps/calendar-svc:/app
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: calendar_db
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes

  # Add other services here as they are developed
  # voice-agent:
  #   build: ./apps/voice-agent
  #   ports:
  #     - "3000:3000"
  #   env_file: .env
  #   volumes:
  #     - ./apps/voice-agent:/app
  #   depends_on:
  #     - db
  #     - redis
  #   restart: unless-stopped
  #   networks:
  #     - app-network

  # dashboard:
  #   build: ./apps/dashboard
  #   ports:
  #     - "3001:3000"
  #   env_file: .env
  #   volumes:
  #     - ./apps/dashboard:/app
  #   depends_on:
  #     - calendar-svc
  #   restart: unless-stopped
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
