import { CalendarProvider, CreateArgs, Slot, EventResult } from './base';
import { ProviderNotEnabledError } from './errors';

export class CronofyCalendarProvider implements CalendarProvider {
  async checkAvailability(_: { from: Date; to: Date; durationMin: number }): Promise<Slot[]> {
    throw new ProviderNotEnabledError('Cronofy');
  }

  async createEvent(_: CreateArgs): Promise<EventResult> {
    throw new ProviderNotEnabledError('Cronofy');
  }
}
