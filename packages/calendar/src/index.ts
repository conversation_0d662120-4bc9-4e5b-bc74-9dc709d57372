export * from './base';
export * from './google';
export * from './cronofy';
export * from './nylas';
export * from './errors';

import type { CalendarProvider } from './base';
import { GoogleCalendarProvider } from './google';
import { CronofyCalendarProvider } from './cronofy';
import { NylasCalendarProvider } from './nylas';

export function getProvider(config: any): CalendarProvider {
  const provider = (process.env.CAL_PROVIDER || 'google').toLowerCase();
  switch (provider) {
    case 'google':
      return new GoogleCalendarProvider(config);
    case 'cronofy':
      return new CronofyCalendarProvider();
    case 'nylas':
      return new NylasCalendarProvider();
    default:
      throw new Error(`Unsupported calendar provider: ${provider}`);
  }
}
