import { request } from 'https';
import { URL } from 'url';
import { CalendarProvider, CreateArgs, Slot, EventResult } from './base';

export class GoogleCalendarProvider implements CalendarProvider {
  private baseUrl = 'https://www.googleapis.com';
  constructor(private _auth: { clientId: string; clientSecret: string; refreshToken: string }) {}

  private async post<T>(path: string, body: any): Promise<T> {
    const url = new URL(path, this.baseUrl);
    const payload = JSON.stringify(body);
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload)
      }
    };
    return new Promise<T>((resolve, reject) => {
      const req = request(url, options, res => {
        let data = '';
        res.on('data', chunk => (data += chunk));
        res.on('end', () => {
          try { resolve(JSON.parse(data || '{}')); } catch (e) { reject(e); }
        });
      });
      req.on('error', reject);
      req.write(payload);
      req.end();
    });
  }

  async checkAvailability(args: { from: Date; to: Date; durationMin: number }): Promise<Slot[]> {
    const res = await this.post<{ calendars: { primary: { busy: { start: string; end: string }[] } } }>('/calendar/v3/freeBusy', {
      timeMin: args.from.toISOString(),
      timeMax: args.to.toISOString(),
      items: [{ id: 'primary' }]
    });
    const busy = res.calendars.primary.busy;
    const slots: Slot[] = [];
    let pointer = args.from;
    for (const b of busy) {
      const start = new Date(b.start);
      if (start.getTime() - pointer.getTime() >= args.durationMin * 60000) {
        slots.push({ start: new Date(pointer), end: new Date(pointer.getTime() + args.durationMin * 60000) });
      }
      pointer = new Date(b.end);
    }
    return slots;
  }

  async createEvent(args: CreateArgs): Promise<EventResult> {
    const res = await this.post<{ id: string; conferenceData?: { entryPoints?: { entryPointType: string; uri: string }[] } }>(
      `/calendar/v3/calendars/${encodeURIComponent(args.calendarId)}/events?conferenceDataVersion=1`,
      {
        summary: args.summary,
        description: args.description,
        start: { dateTime: args.start.toISOString() },
        end: { dateTime: args.end.toISOString() },
        attendees: args.attendees
      }
    );
    const joinUrl = res.conferenceData?.entryPoints?.find(p => p.entryPointType === 'video')?.uri;
    return { eventId: res.id, joinUrl };
  }
}
