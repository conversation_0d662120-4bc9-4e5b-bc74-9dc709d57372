export interface Slot {
  start: Date;
  end: Date;
}

export interface EventResult {
  eventId: string;
  joinUrl?: string;
}

export interface CreateArgs {
  calendarId: string;
  summary: string;
  description?: string;
  start: Date;
  end: Date;
  attendees?: { email: string }[];
}

export interface CalendarProvider {
  checkAvailability(args: { from: Date; to: Date; durationMin: number }): Promise<Slot[]>;
  createEvent(args: CreateArgs): Promise<EventResult>;
}
