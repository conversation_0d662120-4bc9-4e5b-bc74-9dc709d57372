import nock from 'nock';
import { GoogleCalendarProvider } from '../src/google';

delete (process as any).env.http_proxy;
delete (process as any).env.https_proxy;

const BASE_URL = 'https://www.googleapis.com';

const provider = new GoogleCalendarProvider({
  clientId: 'id',
  clientSecret: 'secret',
  refreshToken: 'refresh'
});

afterEach(() => {
  nock.cleanAll();
});

test('freebusy returns slot gap', async () => {
  nock(BASE_URL)
    .post('/calendar/v3/freeBusy')
    .reply(200, {
      calendars: {
        primary: {
          busy: [
            { start: '2024-01-01T10:00:00Z', end: '2024-01-01T11:00:00Z' }
          ]
        }
      }
    });

  const slots = await provider.checkAvailability({
    from: new Date('2024-01-01T09:00:00Z'),
    to: new Date('2024-01-01T12:00:00Z'),
    durationMin: 30
  });

  expect(slots[0].start.toISOString()).toBe('2024-01-01T09:00:00.000Z');
});

test('create event returns meet link', async () => {
  nock(BASE_URL)
    .post('/calendar/v3/calendars/primary/events')
    .query({ conferenceDataVersion: 1 })
    .reply(200, {
      id: 'evt1',
      conferenceData: {
        entryPoints: [
          { entryPointType: 'video', uri: 'https://meet.test/abc' }
        ]
      }
    });

  const result = await provider.createEvent({
    calendarId: 'primary',
    summary: 'Test',
    start: new Date('2024-01-01T10:00:00Z'),
    end: new Date('2024-01-01T11:00:00Z')
  });

  expect(result.joinUrl).toBe('https://meet.test/abc');
});
