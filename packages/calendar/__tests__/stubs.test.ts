import { CronofyCalendarProvider } from '../src/cronofy';
import { NylasCalendarProvider } from '../src/nylas';
import { ProviderNotEnabledError } from '../src/errors';

const cronofy = new CronofyCalendarProvider();
const nylas = new NylasCalendarProvider();

const args = { from: new Date(), to: new Date(), durationMin: 30 };

test('cronofy methods throw ProviderNotEnabledError', async () => {
  await expect(cronofy.checkAvailability(args)).rejects.toBeInstanceOf(ProviderNotEnabledError);
  await expect(cronofy.createEvent({
    calendarId: 'id',
    summary: 's',
    start: new Date(),
    end: new Date()
  })).rejects.toBeInstanceOf(ProviderNotEnabledError);
});

test('nylas methods throw ProviderNotEnabledError', async () => {
  await expect(nylas.checkAvailability(args)).rejects.toBeInstanceOf(ProviderNotEnabledError);
  await expect(nylas.createEvent({
    calendarId: 'id',
    summary: 's',
    start: new Date(),
    end: new Date()
  })).rejects.toBeInstanceOf(ProviderNotEnabledError);
});
