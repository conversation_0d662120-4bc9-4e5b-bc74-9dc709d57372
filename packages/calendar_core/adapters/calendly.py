from __future__ import annotations

from typing import Any, Dict, List

from ..interface import <PERSON><PERSON><PERSON>ider
from ailex_auth import get_access_token


class CalendlyProvider(CalendarProvider):
    """Minimal Calendly adapter."""

    def __init__(self, firm_id: str, provider_name: str, capability: Dict[str, Any] = None):
        self._firm_id = firm_id
        self.provider_name = provider_name
        self.capability = capability or {}
        # Don't call parent __init__ to avoid immediate token fetch
        # Token will be fetched on-demand via _get_token()

    async def _get_token(self) -> str:
        """Get access token from auth service."""
        return await get_access_token(self._firm_id, "calendly")

    async def list_calendars(self) -> List[Dict[str, Any]]:
        # Calendly exposes event types rather than calendars; this is a stub
        # In a real implementation, this would fetch event types from Calendly API
        await self._get_token()  # Ensure auth works
        return [{"id": "primary", "name": "Calendly"}]

    async def create_event(self, calendar_id: str, event: Dict[str, Any]) -> Dict[str, Any]:
        # In a real implementation, this would create a Calendly event
        await self._get_token()  # Ensure auth works
        return {"id": "c_evt_1", **event}
