from __future__ import annotations

import asyncio
import json
import os
from datetime import datetime
from typing import Any, Dict, List

import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ..interface import CalendarProvider
from ailex_auth import get_access_token


class ProviderAuthError(Exception):
    """Raised when calendar provider authentication fails."""
    pass


class GoogleCalendarProvider(CalendarProvider):
    """Google Calendar provider with real API integration."""

    def __init__(self, firm_id: str, provider_name: str, capability: Dict[str, Any] = None):
        self._firm_id = firm_id
        self.provider_name = provider_name
        self.capability = capability or {}
        # Don't call parent __init__ to avoid immediate token fetch
        # Token will be fetched on-demand via _get_token()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((aiohttp.ClientResponseError,))
    )
    async def _get_token(self) -> str:
        """Get access token from auth service with retry logic."""
        try:
            token = await get_access_token(self._firm_id, "google")
            return token
        except Exception as e:
            # Import here to avoid circular imports
            from ailex_auth import AuthServiceError

            if isinstance(e, AuthServiceError) or "404" in str(e) or "401" in str(e):
                raise ProviderAuthError(
                    "Calendar connection needs to be refreshed. Please check your email for instructions."
                ) from e
            raise

    async def _make_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """Make authenticated request to Google Calendar API."""
        token = await self._get_token()

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }

        async with aiohttp.ClientSession() as session:
            async with session.request(method, url, headers=headers, **kwargs) as response:
                if response.status == 429:
                    # Rate limited - let tenacity handle retry
                    response.raise_for_status()
                elif response.status >= 500:
                    # Server error - let tenacity handle retry
                    response.raise_for_status()
                elif response.status >= 400:
                    # Client error - don't retry
                    error_text = await response.text()
                    if response.status in (401, 403):
                        raise ProviderAuthError(
                            "Calendar connection needs to be refreshed. Please check your email for instructions."
                        )
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=error_text
                    )

                return await response.json()

    async def list_calendars(self) -> List[Dict[str, Any]]:
        """List calendars accessible by the firm."""
        url = "https://www.googleapis.com/calendar/v3/users/me/calendarList"
        response = await self._make_request("GET", url)
        return response.get("items", [])

    async def create_event(self, calendar_id: str, event: Dict[str, Any]) -> Dict[str, Any]:
        """Create a calendar event."""
        url = f"https://www.googleapis.com/calendar/v3/calendars/{calendar_id}/events"

        # Convert event format if needed
        google_event = {
            "summary": event.get("summary"),
            "description": event.get("description"),
            "start": {
                "dateTime": event.get("start"),
                "timeZone": "UTC"
            },
            "end": {
                "dateTime": event.get("end"),
                "timeZone": "UTC"
            },
            "conferenceData": {
                "createRequest": {
                    "requestId": f"meet-{calendar_id}-{event.get('summary', 'event')}",
                    "conferenceSolutionKey": {"type": "hangoutsMeet"}
                }
            }
        }

        # Add attendees if provided
        if "attendees" in event:
            google_event["attendees"] = event["attendees"]

        params = {"conferenceDataVersion": 1}
        response = await self._make_request(
            "POST",
            url,
            json=google_event,
            params=params
        )
        return response

    async def get_availability(
        self, calendar_ids: List[str], time_min: datetime, time_max: datetime
    ) -> List[Dict[str, Any]]:
        """Get availability for specified calendars."""
        url = "https://www.googleapis.com/calendar/v3/freeBusy"

        request_body = {
            "timeMin": time_min.isoformat(),
            "timeMax": time_max.isoformat(),
            "items": [{"id": cal_id} for cal_id in calendar_ids]
        }

        response = await self._make_request("POST", url, json=request_body)

        # Convert response format
        result = []
        calendars = response.get("calendars", {})
        for cal_id in calendar_ids:
            cal_data = calendars.get(cal_id, {})
            busy_times = cal_data.get("busy", [])
            result.append({
                "calendar_id": cal_id,
                "busy": busy_times
            })

        return result
