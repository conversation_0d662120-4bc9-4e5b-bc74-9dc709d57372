"""Unified calendar interface."""
from __future__ import annotations

import json
from pathlib import Path
from typing import Dict

from .interface import CalendarProvider
from .adapters.google import GoogleCalendarProvider
from .adapters.calendly import Calendly<PERSON>rovider

_CAP_MATRIX_PATH = Path(__file__).resolve().parent / "capability_matrix.json"


def _load_capabilities() -> Dict[str, Dict]:
    with _CAP_MATRIX_PATH.open() as f:
        return json.load(f)


def get_provider(firm_id: str, provider_name: str) -> CalendarProvider:
    """Factory to return a provider instance for the given firm."""
    provider = provider_name.lower()
    capabilities = _load_capabilities().get(provider, {})
    if provider == "google":
        return GoogleCalendarProvider(firm_id, provider, capabilities)
    if provider == "calendly":
        return CalendlyProvider(firm_id, provider, capabilities)
    raise ValueError(f"Unsupported provider '{provider_name}'")

__all__ = [
    "CalendarProvider",
    "GoogleCalendarProvider",
    "CalendlyProvider",
    "get_provider",
]
