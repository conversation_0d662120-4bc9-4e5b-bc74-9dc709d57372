import os
import sqlalchemy as sa
from alembic import config as alembic_config, command


def test_alembic_upgrade(tmp_path):
    os.environ["ENVIRONMENT"] = "test"
    db_path = tmp_path / "test.db"
    cfg = alembic_config.Config("alembic.ini")
    cfg.set_main_option("sqlalchemy.url", f"sqlite:///{db_path}")
    command.upgrade(cfg, "head")

    engine = sa.create_engine(f"sqlite:///{db_path}")
    insp = sa.inspect(engine)
    tables = insp.get_table_names()
    assert "calls" in tables
    assert "intake_responses" in tables
    assert "bookings" in tables
    assert "callback_tasks" in tables
    assert "state_meta" in tables
