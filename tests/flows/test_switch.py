import os
import pytest
import yaml
import json
from unittest.mock import patch, MagicMock, AsyncMock
from pathlib import Path

from apps.voice_agent import bot

@pytest.fixture
def en_flow_data():
    """Fixture to load the English flow data."""
    flow_path = Path(__file__).parent.parent.parent / "apps" / "voice_agent" / "flows" / "intake_en.yaml"
    with open(flow_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)

@pytest.fixture
def es_flow_data():
    """Fixture to load the Spanish flow data."""
    flow_path = Path(__file__).parent.parent.parent / "apps" / "voice_agent" / "flows" / "intake_es.yaml"
    with open(flow_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)

def test_flow_yaml_structure(en_flow_data, es_flow_data):
    """Test that both YAML files have the same structure."""
    # Check that both flows have the same top-level keys
    assert set(en_flow_data.keys()) == set(es_flow_data.keys())
    
    # Check that both flows have the same flow states
    assert set(en_flow_data.get("flow", {}).keys()) == set(es_flow_data.get("flow", {}).keys())
    
    # Validate metadata
    assert en_flow_data.get("metadata", {}).get("language") == "en"
    assert es_flow_data.get("metadata", {}).get("language") == "es"

def test_load_flow_yaml_english():
    """Test loading the English flow YAML."""
    flow_data = bot.load_flow_yaml("en")
    assert flow_data["metadata"]["language"] == "en"

def test_load_flow_yaml_spanish():
    """Test loading the Spanish flow YAML."""
    flow_data = bot.load_flow_yaml("es")
    assert flow_data["metadata"]["language"] == "es"

def test_load_flow_yaml_fallback():
    """Test fallback to English when an unsupported language is requested."""
    flow_data = bot.load_flow_yaml("fr")  # French is not supported
    assert flow_data["metadata"]["language"] == "en"  # Should fall back to English

@pytest.mark.asyncio
async def test_language_selection():
    """Test that language selection is passed correctly to main function."""
    # Mock the main function
    with patch("apps.voice_agent.bot.main", new_callable=AsyncMock) as mock_main:
        # Create mock arguments
        mock_args = MagicMock()
        mock_args.room_url = "https://example.daily.co/room"
        mock_args.token = "fake_token"
        mock_args.body = {"flow_lang": "es", "translate_transcript": True}
        
        # Call the bot function
        await bot.bot(mock_args)
        
        # Check that main was called with correct language parameters
        mock_main.assert_called_once_with(
            mock_args.room_url, 
            mock_args.token, 
            "es",  # Should use Spanish
            True   # Should enable translation
        )

@pytest.mark.asyncio
async def test_deepgram_params_with_translation():
    """Test that Deepgram parameters are correctly set with translation."""
    # Mock the necessary dependencies
    with patch("apps.voice_agent.bot.DailyTransport") as mock_transport, \
         patch("apps.voice_agent.bot.PipelineRunner") as mock_runner, \
         patch("apps.voice_agent.bot.Pipeline"), \
         patch("apps.voice_agent.bot.PipelineTask"), \
         patch("apps.voice_agent.bot.OpenAILLMService"), \
         patch("apps.voice_agent.bot.CartesiaTTSService"), \
         patch("apps.voice_agent.bot.DeepgramSTTService") as mock_deepgram, \
         patch("apps.voice_agent.bot.load_flow_yaml"):
        
        # Configure the mocks properly
        mock_runner_instance = mock_runner.return_value
        mock_runner_instance.run = AsyncMock()  # Make run an async mock
        
        # Run the main function with Spanish and translation
        await bot.main(
            "https://example.daily.co/room", 
            "fake_token", 
            "es",   # Spanish language
            True    # Enable translation
        )
        
        # Check that DailyTransport was called with correct params
        # First, verify the call was made
        assert mock_transport.called
        
        # Since we can't directly access the transcription_params (it's passed to the constructor),
        # let's check the call args passed to DailyParams
        args, kwargs = mock_transport.call_args
        
        # Verify that Deepgram service was created with correct parameters
        assert mock_deepgram.called
        deepgram_kwargs = mock_deepgram.call_args[1]
        assert deepgram_kwargs["language"] == "es"
        assert deepgram_kwargs["translate"] is True

@pytest.mark.asyncio
async def test_greeting_message_spanish():
    """Test that the correct Spanish greeting is used."""
    # Load the Spanish flow to get the expected greeting
    es_flow_path = Path(__file__).parent.parent.parent / "apps" / "voice_agent" / "flows" / "intake_es.yaml"
    with open(es_flow_path, "r", encoding="utf-8") as f:
        es_flow = yaml.safe_load(f)
    
    expected_greeting = es_flow["flow"]["greeting"]["message"].strip()
    assert "Hola" in expected_greeting
    
    # Create mock for testing
    mock_transport = MagicMock()
    mock_transport.event_handler = lambda event_name: lambda func: func
    mock_transport.capture_participant_transcription = AsyncMock()
    
    mock_task = MagicMock()
    mock_task.queue_frames = AsyncMock()
    
    # Create a context with fake flow data
    flow_data = {"flow": {"greeting": {"message": expected_greeting}}}
    
    # Mock load_flow_yaml to return our fake flow data
    with patch("apps.voice_agent.bot.load_flow_yaml", return_value=flow_data):
        # Simulate on_first_participant_joined handler
        with patch.object(bot, "load_flow_yaml", return_value=flow_data):
            # Create the handler function
            handler = mock_transport.event_handler("on_first_participant_joined")
            
            # Mock the necessary objects for the test
            participant = {"id": "participant1"}
            messages = [{"role": "system", "content": "Initial prompt"}]
            
            # Create a test function that simulates the handler
            async def test_handler():
                @handler
                async def on_first_participant_joined(transport, participant):
                    assert transport == mock_transport
                    # This would typically append to messages and call task.queue_frames
                    messages.append({
                        "role": "system",
                        "content": f"Start the conversation with: '{expected_greeting}'"
                    })
                
                await on_first_participant_joined(mock_transport, participant)
            
            # Run the test handler
            await test_handler()
            
            # Check that the greeting was added correctly
            assert len(messages) == 2
            assert messages[1]["role"] == "system"
            assert f"Start the conversation with: '{expected_greeting}'" in messages[1]["content"]
