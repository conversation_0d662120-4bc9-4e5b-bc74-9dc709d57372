import importlib.util
import asyncio
import time
from pathlib import Path

import pytest

ROOT = Path(__file__).resolve().parents[1]


def load_module(name: str, relative_path: Path):
    spec = importlib.util.spec_from_file_location(name, relative_path)
    module = importlib.util.module_from_spec(spec)
    assert spec.loader is not None
    spec.loader.exec_module(module)
    return module

stt = load_module(
    "stt", ROOT / "apps" / "voice-svc" / "lib" / "stt.py"
)
tts = load_module(
    "tts", ROOT / "apps" / "voice-svc" / "lib" / "tts.py"
)


@pytest.mark.asyncio
async def test_round_trip_latency_under_800ms():
    stt_client = stt.DeepgramSTT(delay=0.05)
    tts_client = tts.SonicTTS(delay=0.05)

    async def audio_source():
        for part in [b"hello", b" there", b" friend"]:
            await asyncio.sleep(0.01)
            yield part

    start = time.perf_counter()

    transcript_parts = []
    async for text in stt_client.stream_transcribe(audio_source()):
        transcript_parts.append(text)
    transcript = "".join(transcript_parts)

    async for _ in tts_client.stream_synthesize(transcript):
        pass

    elapsed = time.perf_counter() - start
    assert elapsed < 0.8
