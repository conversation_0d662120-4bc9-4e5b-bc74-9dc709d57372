import os
os.environ["ENVIRONMENT"] = "test"
import sqlalchemy as sa
import packages.shared.models as models
Base = models.Base


def test_models_create_sqlite():
    os.environ["ENVIRONMENT"] = "test"
    engine = sa.create_engine("sqlite:///:memory:")
    # create stub firms table for FK constraints
    sa.Table(
        "firms",
        Base.metadata,
        sa.Column("id", sa.String, primary_key=True),
    )
    Base.metadata.create_all(engine)
    inspector = sa.inspect(engine)
    expected = {"calls", "intake_responses", "bookings", "callback_tasks", "state_meta"}
    assert expected.issubset(set(inspector.get_table_names()))
