import asyncio
import json
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

import pytest
import pytest_asyncio

try:
    from aiohttp import web
except Exception:  # pragma: no cover - optional dependency
    web = None

DEFAULT_FRAMES: List[Dict[str, Any]] = [
    {"event": "start", "stream_id": "test_stream", "sequence_number": 1},
    {
        "event": "media",
        "stream_id": "test_stream",
        "sequence_number": 2,
        "track": "inbound",
        "payload": "SGVsbG8=",
    },
    {"event": "stop", "stream_id": "test_stream", "sequence_number": 3},
]


@dataclass
class TelnyxWSSimulator:
    """Simple Telnyx WebSocket simulator using aiohttp."""

    host: str = "localhost"
    port: int = 0
    frames: List[Dict[str, Any]] = field(default_factory=lambda: DEFAULT_FRAMES.copy())
    _runner: Optional[Any] = field(init=False, default=None)
    _site: Optional[Any] = field(init=False, default=None)
    url: str = field(init=False, default="")

    async def _handler(self, request):  # pragma: no cover - runtime path
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        for frame in self.frames:
            await ws.send_str(json.dumps(frame))
            await asyncio.sleep(0)
        await ws.close()
        return ws

    async def start(self) -> None:
        if web is None:
            raise RuntimeError("aiohttp package not installed")
        app = web.Application()
        app.router.add_get("/", self._handler)
        self._runner = web.AppRunner(app)
        await self._runner.setup()
        self._site = web.TCPSite(self._runner, self.host, self.port)
        await self._site.start()
        assert self._site._server is not None
        self.port = self._site._server.sockets[0].getsockname()[1]
        self.url = f"ws://{self.host}:{self.port}"

    async def stop(self) -> None:
        if self._runner:
            await self._runner.cleanup()

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.stop()


@pytest_asyncio.fixture
async def telnyx_ws():
    pytest.importorskip("aiohttp")
    sim = TelnyxWSSimulator()
    await sim.start()
    try:
        yield sim
    finally:
        await sim.stop()
