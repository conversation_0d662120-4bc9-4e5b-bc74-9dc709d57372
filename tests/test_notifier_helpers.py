import pytest

from apps.notifier.lib.email import ResendClient
from apps.notifier.lib.sms import TelnyxSMS


@pytest.mark.asyncio
async def test_resend_send_email_snapshot():
    client = ResendClient(delay=0)
    result = await client.send_email("<EMAIL>", "Subject", "<p>Hi</p>", from_email="<EMAIL>")
    assert result == {
        "id": "mock_email",
        "payload": {
            "from": "<EMAIL>",
            "to": ["<EMAIL>"],
            "subject": "Subject",
            "html": "<p>Hi</p>",
        },
    }


@pytest.mark.asyncio
async def test_telnyx_send_sms_snapshot():
    client = TelnyxSMS(delay=0)
    result = await client.send_sms("+15551234567", "hello", from_number="+12223334444")
    assert result == {
        "id": "mock_sms",
        "payload": {
            "from": "+12223334444",
            "to": "+15551234567",
            "text": "hello",
        },
    }
