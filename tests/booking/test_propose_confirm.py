"""
Tests for booking helper functions in voice_agent/booking.py
"""
import pytest
import pytz
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, MagicMock

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from apps.voice_agent.booking import proposeSlot, confirmSlot, Slot, Caller, Booking

# Test fixtures
@pytest.fixture
def mock_calendar_provider():
    """Mock calendar provider for testing."""
    provider = AsyncMock()
    
    # Configure the checkAvailability mock to return empty results (no busy times)
    provider.checkAvailability.return_value = []
    
    # Configure the createEvent mock to return a successful event creation
    provider.createEvent.return_value = {
        "id": "test_event_id_123",
        "status": "confirmed",
        "htmlLink": "https://calendar.google.com/calendar/event?id=test_event_id_123"
    }
    
    return provider

@pytest.fixture
def test_slot():
    """Create a test slot for booking."""
    now = datetime.now(pytz.timezone("America/Chicago"))
    start_time = now.replace(hour=10, minute=0, second=0, microsecond=0) + timedelta(days=1)
    end_time = start_time + timedelta(minutes=30)
    
    return Slot(
        start_time=start_time,
        end_time=end_time,
        timezone="America/Chicago"
    )

@pytest.fixture
def test_caller():
    """Create a test caller for booking."""
    return Caller(
        name="John Do<PERSON>",
        email="<EMAIL>",
        phone="+**********"
    )

# proposeSlot tests
@pytest.mark.asyncio
@patch("apps.voice_agent.booking.get_calendar_provider")
async def test_proposeSlot_returns_three_slots(mock_get_provider, mock_calendar_provider):
    """Test that proposeSlot returns 3 available slots when calendar is free."""
    mock_get_provider.return_value = mock_calendar_provider
    
    # Call the function
    slots = await proposeSlot(user_tz="America/Chicago")
    
    # Verify results
    assert len(slots) == 3
    assert all(isinstance(slot, Slot) for slot in slots)
    assert all(slot.timezone == "America/Chicago" for slot in slots)
    
    # Verify the durations are correct
    for slot in slots:
        assert (slot.end_time - slot.start_time) == timedelta(minutes=30)
    
    # Verify provider was called correctly
    mock_get_provider.assert_called_once()
    mock_calendar_provider.checkAvailability.assert_called_once()

@pytest.mark.asyncio
@patch("apps.voice_agent.booking.get_calendar_provider")
@patch("apps.voice_agent.booking.datetime")
async def test_proposeSlot_respects_busy_times(mock_datetime, mock_get_provider, mock_calendar_provider):
    """Test that proposeSlot skips busy times."""
    # Create a fixed current time to make the test deterministic
    # Set now to 8:00 AM
    fixed_now = datetime(2025, 5, 22, 8, 0, 0, tzinfo=pytz.timezone("America/Chicago"))
    mock_datetime.now.return_value = fixed_now
    # Pass through the fromisoformat method
    mock_datetime.fromisoformat = datetime.fromisoformat
    
    # Make 9 AM - 11 AM busy
    busy_start = datetime(2025, 5, 22, 9, 0, 0, tzinfo=pytz.timezone("America/Chicago"))
    busy_end = datetime(2025, 5, 22, 11, 0, 0, tzinfo=pytz.timezone("America/Chicago"))
    
    mock_calendar_provider.checkAvailability.return_value = [
        {
            "start": busy_start,
            "end": busy_end
        }
    ]
    
    mock_get_provider.return_value = mock_calendar_provider
    
    # Call the function
    slots = await proposeSlot(user_tz="America/Chicago")
    
    # Verify results
    assert len(slots) == 3
    
    # First slot should start after the busy period (at 11 AM)
    assert slots[0].start_time.hour == 11
    # Depending on when the busy period ends, the slot might start at either 11:00 or 11:30
    assert slots[0].start_time.minute in [0, 30]
    
    # Verify that none of the slots overlap with the busy time
    for slot in slots:
        # Slot should either end before busy_start or start after busy_end
        assert (slot.end_time <= busy_start) or (slot.start_time >= busy_end)
    
    # Verify provider was called correctly
    mock_get_provider.assert_called_once()
    mock_calendar_provider.checkAvailability.assert_called_once()

@pytest.mark.asyncio
@patch("apps.voice_agent.booking.get_calendar_provider")
async def test_proposeSlot_skips_weekends(mock_get_provider, mock_calendar_provider):
    """Test that proposeSlot skips weekend slots."""
    mock_get_provider.return_value = mock_calendar_provider
    
    # Create a custom slot on a Friday to test weekend handling
    friday = datetime(2025, 5, 23, 9, 0, 0, tzinfo=pytz.timezone("America/Chicago"))  # Friday May 23, 2025 at 9 AM
    
    # Call the function with a patch to make "now" be Friday
    with patch("apps.voice_agent.booking.datetime") as mock_datetime:
        mock_datetime.now.return_value = friday
        mock_datetime.fromisoformat = datetime.fromisoformat  # Pass through
        
        slots = await proposeSlot(user_tz="America/Chicago")
    
    # Verify results
    assert len(slots) == 3
    
    # Check that all slots are on weekdays
    for slot in slots:
        assert slot.start_time.weekday() < 5  # Mon-Fri are 0-4
    
    # Verify none of the slots are on weekends
    for slot in slots:
        assert slot.start_time.weekday() != 5  # Not Saturday
        assert slot.start_time.weekday() != 6  # Not Sunday
    
    # Verify first slot start time is at or after current time on Friday
    assert slots[0].start_time.day == friday.day
    assert slots[0].start_time.month == friday.month
    assert slots[0].start_time.year == friday.year
    assert slots[0].start_time.hour >= friday.hour

# confirmSlot tests
@pytest.mark.asyncio
@patch("apps.voice_agent.booking.get_calendar_provider")
async def test_confirmSlot_creates_calendar_event(mock_get_provider, mock_calendar_provider, test_slot, test_caller):
    """Test that confirmSlot creates a calendar event and returns a booking."""
    mock_get_provider.return_value = mock_calendar_provider
    
    # Call the function
    booking = await confirmSlot(test_slot, test_caller)
    
    # Verify results
    assert isinstance(booking, Booking)
    assert booking.status == "confirmed"
    assert booking.calendar_event_id == "test_event_id_123"
    assert booking.slot == test_slot
    assert booking.caller == test_caller
    
    # Verify the calendar provider was called correctly
    mock_get_provider.assert_called_once()
    mock_calendar_provider.createEvent.assert_called_once()
    
    # Verify the event data
    call_args = mock_calendar_provider.createEvent.call_args[0][0]
    assert call_args["summary"] == f"Appointment with {test_caller.name}"
    assert call_args["start"]["dateTime"] == test_slot.start_time.isoformat()
    assert call_args["end"]["dateTime"] == test_slot.end_time.isoformat()
    assert call_args["attendees"][0]["email"] == test_caller.email

@pytest.mark.asyncio
@patch("apps.voice_agent.booking.get_calendar_provider")
async def test_confirmSlot_handles_error(mock_get_provider, test_slot, test_caller):
    """Test that confirmSlot handles errors gracefully."""
    # Make the provider raise an exception
    provider = AsyncMock()
    provider.createEvent.side_effect = Exception("Test error")
    mock_get_provider.return_value = provider
    
    # Call the function
    booking = await confirmSlot(test_slot, test_caller)
    
    # Verify results
    assert isinstance(booking, Booking)
    assert booking.status == "error"
    assert booking.calendar_event_id is None
    assert booking.slot == test_slot
    assert booking.caller == test_caller

@pytest.mark.asyncio
@patch("apps.voice_agent.booking.get_calendar_provider")
async def test_confirmSlot_caller_without_email(mock_get_provider, mock_calendar_provider, test_slot):
    """Test that confirmSlot works with callers who don't have an email."""
    mock_get_provider.return_value = mock_calendar_provider
    
    # Create a caller without email
    caller = Caller(name="John Doe", phone="+**********")
    
    # Call the function
    booking = await confirmSlot(test_slot, caller)
    
    # Verify results
    assert isinstance(booking, Booking)
    assert booking.status == "confirmed"
    
    # Verify the event data
    call_args = mock_calendar_provider.createEvent.call_args[0][0]
    assert "attendees" in call_args
    assert len(call_args["attendees"]) == 0  # No attendees added
