import os
from datetime import datetime
import uuid

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select

from packages.shared import models


@pytest.fixture
async def client_and_session(monkeypatch):
    os.environ["GOOGLE_PUSH_SECRET"] = "secret"
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    SessionLocal = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)

    from apps.voice_svc.routes.api.calendar import google_push

    monkeypatch.setattr(google_push, "engine", engine, raising=False)
    monkeypatch.setattr(google_push, "AsyncSessionLocal", SessionLocal, raising=False)

    async with engine.begin() as conn:
        await conn.run_sync(models.Base.metadata.create_all)

    app = FastAPI()
    app.include_router(google_push.router)

    with TestClient(app) as client:
        yield client, SessionLocal

    await engine.dispose()


@pytest.mark.asyncio
async def test_google_push_updates_booking(client_and_session):
    client, SessionLocal = client_and_session

    async with SessionLocal() as session:
        booking = models.Booking(
            firm_id=str(uuid.uuid4()),
            provider=models.BookingProvider.GOOGLE,
            calendar_event_id="evt123",
            start_at=datetime.utcnow(),
            booked_at=datetime.utcnow(),
        )
        session.add(booking)
        await session.commit()

    headers = {
        "X-Goog-Channel-Token": "secret",
        "X-Goog-Resource-State": "cancelled",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/evt123",
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200

    async with SessionLocal() as session:
        result = await session.execute(select(models.Booking))
        updated = result.scalar_one()
        assert updated.status == "cancelled"
