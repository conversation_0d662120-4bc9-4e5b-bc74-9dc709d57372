"""
Tests for the Telnyx webhook endpoint.

Validating:
1. Signature verification (HMAC-SHA256)
2. Background task creation
3. Response status codes (202/403/422)
"""
import os
import json
import hmac
import hashlib
import pytest
from unittest.mock import AsyncMock
from fastapi import FastAP<PERSON>, Request, HTTPException, Depends, Header, BackgroundTasks
from fastapi.testclient import TestClient
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import Optional

# Model for webhook payload
class TelnyxCallInitRequest(BaseModel):
    call_control_id: str
    telnyx_rtc_session_id: str
    lang: Optional[str] = Field(default="en")

# Mock pipeline function
run_pipeline_mock = AsyncMock()

# Signature verification
def verify_signature(
    request_body: bytes,
    signature: str = Header(..., alias="X-Telnyx-Webhook-Signature")
) -> bool:
    """Verify Telnyx webhook signature using HMAC-SHA256."""
    secret = os.environ.get("TELNYX_SIGNING_SECRET", "test-secret")
    computed = hmac.new(
        secret.encode(),
        request_body,
        hashlib.sha256
    ).hexdigest()
    return hmac.compare_digest(computed, signature)

# Test app
app = FastAPI()

# Webhook endpoint
@app.post("/voice/call-init")
async def call_init(
    request: Request,
    background_tasks: BackgroundTasks,
    is_valid: bool = Depends(verify_signature)
):
    """Handle Telnyx call initialization webhook."""
    if not is_valid:
        raise HTTPException(status_code=403, detail="Invalid webhook signature")
    
    body_bytes = await request.body()
    data = json.loads(body_bytes.decode())
    payload = TelnyxCallInitRequest(**data)
    
    background_tasks.add_task(
        run_pipeline_mock,
        call_control_id=payload.call_control_id,
        telnyx_rtc_session_id=payload.telnyx_rtc_session_id,
        lang=payload.lang
    )
    
    return JSONResponse(
        status_code=202,
        content={"message": "Call processing initiated"}
    )

# Health check
@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

# Create test client
client = TestClient(app)

# Test data
SAMPLE_PAYLOAD = {
    "call_control_id": "test-call-id-123",
    "telnyx_rtc_session_id": "test-session-456",
    "lang": "en"
}

# Helper function
def create_signature(payload, secret="test-secret"):
    """Create valid HMAC-SHA256 signature for testing."""
    payload_bytes = json.dumps(payload).encode()
    return hmac.new(
        secret.encode(),
        payload_bytes,
        hashlib.sha256
    ).hexdigest()

# Tests
def test_health_check():
    """Test basic health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_valid_signature():
    """Test valid signature is accepted and starts pipeline."""
    # Prepare test data
    payload_str = json.dumps(SAMPLE_PAYLOAD)
    signature = create_signature(SAMPLE_PAYLOAD)
    
    # Reset mock
    run_pipeline_mock.reset_mock()
    
    # Send request
    response = client.post(
        "/voice/call-init",
        content=payload_str,
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Assert response
    assert response.status_code == 202
    assert response.json() == {"message": "Call processing initiated"}
    
    # Verify pipeline was called correctly
    run_pipeline_mock.assert_called_once_with(
        call_control_id=SAMPLE_PAYLOAD["call_control_id"],
        telnyx_rtc_session_id=SAMPLE_PAYLOAD["telnyx_rtc_session_id"],
        lang=SAMPLE_PAYLOAD["lang"]
    )

def test_invalid_signature():
    """Test invalid signature is rejected with 403."""
    # Prepare test data
    payload_str = json.dumps(SAMPLE_PAYLOAD)
    invalid_signature = "invalid-signature-123"
    
    # Reset mock
    run_pipeline_mock.reset_mock()
    
    # Send request
    response = client.post(
        "/voice/call-init",
        content=payload_str,
        headers={
            "X-Telnyx-Webhook-Signature": invalid_signature,
            "Content-Type": "application/json"
        }
    )
    
    # Assert response
    assert response.status_code == 403
    assert "Invalid webhook signature" in response.text
    
    # Verify pipeline was not called
    run_pipeline_mock.assert_not_called()

def test_missing_signature():
    """Test missing signature header returns 422."""
    # Prepare test data
    payload_str = json.dumps(SAMPLE_PAYLOAD)
    
    # Reset mock
    run_pipeline_mock.reset_mock()
    
    # Send request without signature header
    response = client.post(
        "/voice/call-init",
        content=payload_str,
        headers={"Content-Type": "application/json"}
    )
    
    # Assert response
    assert response.status_code == 422
    
    # Verify pipeline was not called
    run_pipeline_mock.assert_not_called()

def test_spanish_language():
    """Test webhook with Spanish language parameter."""
    # Prepare test data
    spanish_payload = {
        "call_control_id": "test-call-id-123",
        "telnyx_rtc_session_id": "test-session-456",
        "lang": "es"
    }
    payload_str = json.dumps(spanish_payload)
    signature = create_signature(spanish_payload)
    
    # Reset mock
    run_pipeline_mock.reset_mock()
    
    # Send request
    response = client.post(
        "/voice/call-init",
        content=payload_str,
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Assert response
    assert response.status_code == 202
    
    # Verify pipeline was called with Spanish language
    run_pipeline_mock.assert_called_once_with(
        call_control_id=spanish_payload["call_control_id"],
        telnyx_rtc_session_id=spanish_payload["telnyx_rtc_session_id"],
        lang="es"
    )
