"""
Isolated tests for the Telnyx webhook endpoint.

This approach uses more aggressive mocking to prevent module import issues
with Pipecat dependencies while allowing us to test the webhook signature
verification and endpoint behavior.
"""
import os
import sys
import hmac
import hashlib
import json
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import FastAPI, Request, Depends, Header, HTTPException, BackgroundTasks
from fastapi.testclient import TestClient
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import Optional

# Create isolated models and endpoints that mirror the real implementation
class TelnyxCallInitRequest(BaseModel):
    call_control_id: str
    telnyx_rtc_session_id: str
    lang: Optional[str] = Field(default="en")

# Create a mock for the run_pipeline function
mock_run_pipeline = AsyncMock()

# Create a test app
app = FastAPI()

# Signature verification function copied from the real implementation
def verify_telnyx_signature(
    request_body: bytes,
    signature: str = Header(..., alias="X-Telnyx-Webhook-Signature")
) -> bool:
    """Verify Telnyx webhook signature using HMAC-SHA256."""
    secret = os.getenv("TELNYX_SIGNING_SECRET")
    if not secret:
        raise RuntimeError("TELNYX_SIGNING_SECRET environment variable not set")
    
    computed_signature = hmac.new(
        secret.encode(),
        request_body,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(computed_signature, signature)

# Webhook endpoint that mirrors the real implementation
@app.post("/voice/call-init")
async def call_init(
    request: Request,
    background_tasks: BackgroundTasks,
    is_valid: bool = Depends(verify_telnyx_signature)
):
    """Handle Telnyx call initialization webhook."""
    if not is_valid:
        raise HTTPException(status_code=403, detail="Invalid webhook signature")
    
    # Parse the request body
    body = await request.body()
    data = json.loads(body.decode())
    
    # Extract required fields
    call_data = TelnyxCallInitRequest(**data)
    
    # Start the pipeline in a background task
    background_tasks.add_task(
        mock_run_pipeline,
        call_control_id=call_data.call_control_id,
        telnyx_rtc_session_id=call_data.telnyx_rtc_session_id,
        lang=call_data.lang
    )
    
    return JSONResponse(
        status_code=202,
        content={"message": "Call processing initiated"}
    )

# Health check endpoint
@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

# Create test client
client = TestClient(app)

# Test data
VALID_PAYLOAD = {
    "call_control_id": "test-call-123",
    "telnyx_rtc_session_id": "test-session-456",
    "lang": "en"
}

# Helper function to create a valid signature
def create_signature(payload, secret):
    """Create a valid Telnyx signature for testing."""
    payload_bytes = json.dumps(payload).encode()
    return hmac.new(
        secret.encode(),
        payload_bytes,
        hashlib.sha256
    ).hexdigest()

# Test fixtures
@pytest.fixture
def mock_env_signing_secret(monkeypatch):
    """Fixture to set a mock signing secret."""
    secret = "test-signing-secret"
    monkeypatch.setenv("TELNYX_SIGNING_SECRET", secret)
    return secret

# Tests
def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_call_init_valid_signature(mock_env_signing_secret):
    """Test call init endpoint with valid signature."""
    # Create payload and signature
    payload = VALID_PAYLOAD
    payload_json = json.dumps(payload)
    signature = create_signature(payload, mock_env_signing_secret)
    
    # Reset mock before test
    mock_run_pipeline.reset_mock()
    
    # Make request
    response = client.post(
        "/voice/call-init",
        data=payload_json,
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify response
    assert response.status_code == 202
    assert response.json() == {"message": "Call processing initiated"}
    
    # Verify mock was called with correct args
    mock_run_pipeline.assert_called_once_with(
        call_control_id=payload["call_control_id"],
        telnyx_rtc_session_id=payload["telnyx_rtc_session_id"],
        lang=payload["lang"]
    )

def test_call_init_invalid_signature(mock_env_signing_secret):
    """Test call init endpoint with invalid signature."""
    # Prepare payload with invalid signature
    payload_json = json.dumps(VALID_PAYLOAD)
    invalid_signature = "invalid-signature-123"
    
    # Reset mock before test
    mock_run_pipeline.reset_mock()
    
    # Make request
    response = client.post(
        "/voice/call-init",
        data=payload_json,
        headers={
            "X-Telnyx-Webhook-Signature": invalid_signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify response
    assert response.status_code == 403
    assert "Invalid webhook signature" in response.text
    
    # Verify pipeline was NOT called
    mock_run_pipeline.assert_not_called()

def test_call_init_missing_signature():
    """Test call init endpoint with missing signature header."""
    # Prepare payload without signature
    payload_json = json.dumps(VALID_PAYLOAD)
    
    # Reset mock before test
    mock_run_pipeline.reset_mock()
    
    # Make request without signature header
    response = client.post(
        "/voice/call-init",
        data=payload_json,
        headers={"Content-Type": "application/json"}
    )
    
    # Verify response is 422 Unprocessable Entity (missing required header)
    assert response.status_code == 422
    
    # Verify pipeline was NOT called
    mock_run_pipeline.assert_not_called()

def test_call_init_spanish_language(mock_env_signing_secret):
    """Test call init with Spanish language parameter."""
    # Prepare Spanish payload
    spanish_payload = {
        "call_control_id": "test-call-123",
        "telnyx_rtc_session_id": "test-session-456",
        "lang": "es"
    }
    payload_json = json.dumps(spanish_payload)
    signature = create_signature(spanish_payload, mock_env_signing_secret)
    
    # Reset mock before test
    mock_run_pipeline.reset_mock()
    
    # Make request
    response = client.post(
        "/voice/call-init",
        data=payload_json,
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify response
    assert response.status_code == 202
    
    # Verify pipeline was called with Spanish language
    mock_run_pipeline.assert_called_once_with(
        call_control_id=spanish_payload["call_control_id"],
        telnyx_rtc_session_id=spanish_payload["telnyx_rtc_session_id"],
        lang="es"
    )
