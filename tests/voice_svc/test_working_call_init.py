"""
Tests for the Telnyx webhook endpoint in the voice service.

This test file verifies:
1. HMAC-SHA256 signature validation
2. Background task creation
3. Proper 202/403 status code responses
"""

import os
import sys
import json
import hmac
import hashlib
import pytest
from unittest.mock import patch, AsyncMock
from pathlib import Path
from fastapi.testclient import TestClient

# Add project root to sys.path to ensure imports work correctly
project_root = str(Path(__file__).parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

# Create TestClient with app (using patch to avoid pipecat dependency issues)
with patch('apps.voice_agent.bot_runner.run_pipeline', new_callable=AsyncMock) as mock_run_pipeline:
    from apps.voice_svc.app import app
    client = TestClient(app)

# Sample test data
SAMPLE_PAYLOAD = {
    "call_control_id": "test-call-id-123",
    "telnyx_rtc_session_id": "test-session-456",
    "lang": "en"
}

# Test fixture for environment variables
@pytest.fixture
def mock_signing_secret(monkeypatch):
    """Set up test environment with signing secret."""
    test_secret = "test-signing-secret"
    monkeypatch.setenv("TELNYX_SIGNING_SECRET", test_secret)
    return test_secret

# Helper function for creating signatures
def create_telnyx_signature(payload_bytes, secret):
    """Create a valid Telnyx signature for testing."""
    return hmac.new(
        secret.encode(),
        payload_bytes,
        hashlib.sha256
    ).hexdigest()

# Test functions
def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

@patch('apps.voice_agent.bot_runner.run_pipeline')
def test_call_init_valid_signature(mock_pipeline, mock_signing_secret):
    """Test that valid signatures are accepted and start the pipeline."""
    # Prepare test data
    payload_bytes = json.dumps(SAMPLE_PAYLOAD).encode()
    signature = create_telnyx_signature(payload_bytes, mock_signing_secret)
    
    # Make request with valid signature
    response = client.post(
        "/voice/call-init",
        data=payload_bytes,  # Use raw bytes to match signature calculation
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify response
    assert response.status_code == 202
    assert response.json() == {"message": "Call processing initiated"}
    
    # Verify pipeline was called with correct args
    mock_pipeline.assert_called_once_with(
        call_control_id=SAMPLE_PAYLOAD["call_control_id"],
        telnyx_rtc_session_id=SAMPLE_PAYLOAD["telnyx_rtc_session_id"],
        lang=SAMPLE_PAYLOAD["lang"]
    )

@patch('apps.voice_agent.bot_runner.run_pipeline')
def test_call_init_invalid_signature(mock_pipeline, mock_signing_secret):
    """Test that invalid signatures are rejected with 403."""
    # Prepare test data
    payload_bytes = json.dumps(SAMPLE_PAYLOAD).encode()
    invalid_signature = "invalid-signature-does-not-match"
    
    # Make request with invalid signature
    response = client.post(
        "/voice/call-init",
        data=payload_bytes,
        headers={
            "X-Telnyx-Webhook-Signature": invalid_signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify forbidden response
    assert response.status_code == 403
    assert "Invalid webhook signature" in response.text
    
    # Verify pipeline was NOT called
    mock_pipeline.assert_not_called()

@patch('apps.voice_agent.bot_runner.run_pipeline')
def test_call_init_missing_signature(mock_pipeline):
    """Test that missing signature header returns 422."""
    # Prepare test data
    payload_bytes = json.dumps(SAMPLE_PAYLOAD).encode()
    
    # Make request without signature header
    response = client.post(
        "/voice/call-init",
        data=payload_bytes,
        headers={"Content-Type": "application/json"}
    )
    
    # Verify unprocessable entity response
    assert response.status_code == 422
    
    # Verify pipeline was NOT called
    mock_pipeline.assert_not_called()

@patch('apps.voice_agent.bot_runner.run_pipeline')
def test_call_init_spanish_language(mock_pipeline, mock_signing_secret):
    """Test webhook with Spanish language parameter."""
    # Prepare test data with Spanish language
    spanish_payload = {
        "call_control_id": "test-call-id-123",
        "telnyx_rtc_session_id": "test-session-456",
        "lang": "es"
    }
    payload_bytes = json.dumps(spanish_payload).encode()
    signature = create_telnyx_signature(payload_bytes, mock_signing_secret)
    
    # Make request with Spanish language
    response = client.post(
        "/voice/call-init",
        data=payload_bytes,
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify response
    assert response.status_code == 202
    
    # Verify pipeline was called with Spanish language
    mock_pipeline.assert_called_once_with(
        call_control_id=spanish_payload["call_control_id"],
        telnyx_rtc_session_id=spanish_payload["telnyx_rtc_session_id"],
        lang="es"
    )
