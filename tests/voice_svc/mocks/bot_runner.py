"""
Mock bot_runner module for testing the webhook endpoint.
"""

import asyncio

async def run_pipeline(call_control_id: str, telnyx_rtc_session_id: str, lang: str = "en"):
    """
    Mock implementation of run_pipeline for testing.
    Simply logs the call and returns after a short delay.
    
    Args:
        call_control_id: The Telnyx call control ID
        telnyx_rtc_session_id: The Telnyx RTC session ID for WebSocket connection
        lang: The language code for the conversation flow (default: "en")
    
    Returns:
        None
    """
    await asyncio.sleep(0.1)  # Simulate minimal processing time
    return None
