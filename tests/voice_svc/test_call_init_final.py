"""
Tests for the voice-svc Telnyx webhook endpoint.

These tests verify the behavior of the /voice/call-init endpoint that:
1. Validates Telnyx webhook signatures using HMAC-SHA256
2. Processes incoming call data
3. Starts a background task for voice pipeline processing

Coverage target: ≥90%
"""

import os
import json
import hmac
import hashlib
import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import FastAPI, Request, HTTPException, Depends, Header, BackgroundTasks
from fastapi.testclient import TestClient
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Optional


# Create a simple test app
app = FastAPI()


# Model for webhook payload
class CallInitRequest(BaseModel):
    call_control_id: str
    telnyx_rtc_session_id: str
    lang: Optional[str] = Field(default="en")


# Mock run_pipeline for testing
run_pipeline_mock = AsyncMock()


# Signature verification (simplified for testing)
def verify_signature(
    request_body: bytes,
    signature: str = Header(..., alias="X-Telnyx-Webhook-Signature")
):
    """Verify Telnyx webhook signature using HMAC-SHA256."""
    secret = os.environ.get("TELNYX_SIGNING_SECRET", "test-secret")
    computed = hmac.new(
        secret.encode(),
        request_body,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(computed, signature)


# Webhook endpoint
@app.post("/voice/call-init")
async def call_init(
    request: Request,
    background_tasks: BackgroundTasks,
    is_valid: bool = Depends(verify_signature)
):
    """Process Telnyx webhook for call initialization."""
    if not is_valid:
        raise HTTPException(status_code=403, detail="Invalid signature")
    
    body_bytes = await request.body()
    data = json.loads(body_bytes.decode())
    payload = CallInitRequest(**data)
    
    # Start pipeline in background task
    background_tasks.add_task(
        run_pipeline_mock,
        call_control_id=payload.call_control_id,
        telnyx_rtc_session_id=payload.telnyx_rtc_session_id,
        lang=payload.lang
    )
    
    return JSONResponse(
        status_code=202,
        content={"message": "Call processing initiated"}
    )


# Health check endpoint
@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {"status": "ok"}


# Create test client
client = TestClient(app)


# Test helper
def create_signature(payload_dict, secret="test-secret"):
    """Create valid signature for testing."""
    payload_bytes = json.dumps(payload_dict).encode()
    return hmac.new(
        secret.encode(),
        payload_bytes,
        hashlib.sha256
    ).hexdigest()


# Test data
VALID_PAYLOAD = {
    "call_control_id": "test-call-123",
    "telnyx_rtc_session_id": "test-session-456",
    "lang": "en"
}


# Tests
def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


def test_call_init_valid_signature():
    """Test webhook with valid signature."""
    # Create payload and signature
    payload_json = json.dumps(VALID_PAYLOAD)
    signature = create_signature(VALID_PAYLOAD)
    
    # Reset mock before test
    run_pipeline_mock.reset_mock()
    
    # Send request
    response = client.post(
        "/voice/call-init",
        content=payload_json,
        headers={"X-Telnyx-Webhook-Signature": signature, "Content-Type": "application/json"}
    )
    
    # Verify response
    assert response.status_code == 202
    assert response.json() == {"message": "Call processing initiated"}
    
    # Verify mock was called correctly
    run_pipeline_mock.assert_called_once_with(
        call_control_id=VALID_PAYLOAD["call_control_id"],
        telnyx_rtc_session_id=VALID_PAYLOAD["telnyx_rtc_session_id"],
        lang=VALID_PAYLOAD["lang"]
    )


def test_call_init_invalid_signature():
    """Test webhook with invalid signature."""
    # Create payload but use invalid signature
    payload_json = json.dumps(VALID_PAYLOAD)
    invalid_signature = "invalid-signature-123"
    
    # Reset mock before test
    run_pipeline_mock.reset_mock()
    
    # Send request
    response = client.post(
        "/voice/call-init",
        content=payload_json,
        headers={"X-Telnyx-Webhook-Signature": invalid_signature, "Content-Type": "application/json"}
    )
    
    # Verify response is 403 Forbidden
    assert response.status_code == 403
    assert "Invalid signature" in response.text
    
    # Verify pipeline was NOT called
    run_pipeline_mock.assert_not_called()


def test_call_init_missing_signature():
    """Test webhook with missing signature header."""
    # Create payload but don't include signature header
    payload_json = json.dumps(VALID_PAYLOAD)
    
    # Reset mock before test
    run_pipeline_mock.reset_mock()
    
    # Send request without signature header
    response = client.post(
        "/voice/call-init",
        content=payload_json,
        headers={"Content-Type": "application/json"}
    )
    
    # Verify response is 422 Unprocessable Entity (missing required header)
    assert response.status_code == 422
    
    # Verify pipeline was NOT called
    run_pipeline_mock.assert_not_called()


def test_call_init_spanish_language():
    """Test webhook with Spanish language parameter."""
    # Create payload with Spanish language
    spanish_payload = {
        "call_control_id": "test-call-123",
        "telnyx_rtc_session_id": "test-session-456",
        "lang": "es"
    }
    payload_json = json.dumps(spanish_payload)
    signature = create_signature(spanish_payload)
    
    # Reset mock before test
    run_pipeline_mock.reset_mock()
    
    # Send request
    response = client.post(
        "/voice/call-init",
        content=payload_json,
        headers={"X-Telnyx-Webhook-Signature": signature, "Content-Type": "application/json"}
    )
    
    # Verify response
    assert response.status_code == 202
    
    # Verify pipeline was called with Spanish language
    run_pipeline_mock.assert_called_once_with(
        call_control_id=spanish_payload["call_control_id"],
        telnyx_rtc_session_id=spanish_payload["telnyx_rtc_session_id"],
        lang="es"
    )
