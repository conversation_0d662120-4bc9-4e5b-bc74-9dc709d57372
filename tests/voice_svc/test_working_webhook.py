"""
Tests for the Telnyx webhook endpoint in voice-svc.

Tests verify:
1. Signature validation with HMAC-SHA256
2. Background task creation for call processing
3. Proper HTTP status codes based on signature validation
"""

import os
import sys
import json
import hmac
import hashlib
import pytest
from unittest.mock import patch, AsyncMock
from pathlib import Path
from fastapi.testclient import TestClient

# Add project root to sys.path to ensure imports work correctly
project_root = str(Path(__file__).parents[2])
if project_root not in sys.path:
    sys.path.append(project_root)

# Mock the voice pipeline module to avoid external dependencies
with patch('apps.voice_agent.bot_runner.run_pipeline', new_callable=AsyncMock) as mock_run_pipeline:
    # Now import the app with the pipeline mocked
    from apps.voice_svc.app import app
    
    # Create test client for making requests
    client = TestClient(app)

# Test data
SAMPLE_PAYLOAD = {
    "call_control_id": "test-call-id-123",
    "telnyx_rtc_session_id": "test-session-456",
    "lang": "en"
}

# Helper functions
def create_signature(payload, secret):
    """Create valid HMAC-SHA256 signature for testing."""
    payload_bytes = json.dumps(payload).encode()
    return hmac.new(
        secret.encode(),
        payload_bytes,
        hashlib.sha256
    ).hexdigest()

# Fixtures
@pytest.fixture
def mock_env_vars(monkeypatch):
    """Set up test environment variables."""
    test_secret = "test-signing-secret"
    monkeypatch.setenv("TELNYX_SIGNING_SECRET", test_secret)
    return test_secret

# Tests
def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_call_init_valid_signature(mock_env_vars):
    """Test webhook with valid signature."""
    # Create payload and signature
    payload_json = json.dumps(SAMPLE_PAYLOAD)
    signature = create_signature(SAMPLE_PAYLOAD, mock_env_vars)
    
    # Reset the mock
    mock_run_pipeline.reset_mock()
    
    # Make request with valid signature
    response = client.post(
        "/voice/call-init",
        content=payload_json,  # Use content instead of json to match signature calculation
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify response
    assert response.status_code == 202, f"Expected 202, got {response.status_code}: {response.text}"
    assert response.json() == {"message": "Call processing initiated"}
    
    # Verify pipeline was called with correct arguments
    mock_run_pipeline.assert_called_once_with(
        call_control_id=SAMPLE_PAYLOAD["call_control_id"],
        telnyx_rtc_session_id=SAMPLE_PAYLOAD["telnyx_rtc_session_id"],
        lang=SAMPLE_PAYLOAD["lang"]
    )

def test_call_init_invalid_signature(mock_env_vars):
    """Test webhook with invalid signature."""
    # Create payload but use invalid signature
    payload_json = json.dumps(SAMPLE_PAYLOAD)
    invalid_signature = "invalid-signature-123"
    
    # Reset the mock
    mock_run_pipeline.reset_mock()
    
    # Make request with invalid signature
    response = client.post(
        "/voice/call-init",
        content=payload_json,
        headers={
            "X-Telnyx-Webhook-Signature": invalid_signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify forbidden response
    assert response.status_code == 403, f"Expected 403, got {response.status_code}: {response.text}"
    assert "Invalid webhook signature" in response.text
    
    # Verify pipeline was NOT called
    mock_run_pipeline.assert_not_called()

def test_call_init_missing_signature():
    """Test webhook with missing signature header."""
    # Create payload without signature
    payload_json = json.dumps(SAMPLE_PAYLOAD)
    
    # Reset the mock
    mock_run_pipeline.reset_mock()
    
    # Make request without signature header
    response = client.post(
        "/voice/call-init",
        content=payload_json,
        headers={"Content-Type": "application/json"}
    )
    
    # Verify unprocessable entity response
    assert response.status_code == 422
    
    # Verify pipeline was NOT called
    mock_run_pipeline.assert_not_called()

def test_call_init_spanish_language(mock_env_vars):
    """Test webhook with Spanish language parameter."""
    # Create Spanish payload
    spanish_payload = {
        "call_control_id": "test-call-123",
        "telnyx_rtc_session_id": "test-session-456",
        "lang": "es"
    }
    payload_json = json.dumps(spanish_payload)
    signature = create_signature(spanish_payload, mock_env_vars)
    
    # Reset the mock
    mock_run_pipeline.reset_mock()
    
    # Make request with Spanish language
    response = client.post(
        "/voice/call-init",
        content=payload_json,
        headers={
            "X-Telnyx-Webhook-Signature": signature,
            "Content-Type": "application/json"
        }
    )
    
    # Verify response
    assert response.status_code == 202
    
    # Verify pipeline was called with Spanish language
    mock_run_pipeline.assert_called_once_with(
        call_control_id=spanish_payload["call_control_id"],
        telnyx_rtc_session_id=spanish_payload["telnyx_rtc_session_id"],
        lang="es"
    )
