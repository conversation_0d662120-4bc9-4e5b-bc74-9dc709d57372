"""
Tests for the Telnyx webhook integration in the voice service.

This test suite verifies the /voice/call-init endpoint that:
1. Validates Telnyx webhook signatures with HMAC-SHA256
2. Processes incoming call data
3. Starts a background task for the voice pipeline

Coverage target: ≥90%
"""

import os
import sys
import hmac
import hashlib
import json
import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import FastAP<PERSON>, Request, HTTPException, BackgroundTasks, Header, Depends
from fastapi.testclient import TestClient
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import Optional

# Set up mocking for the voice agent dependencies
# This prevents the tests from requiring Pipecat and other external dependencies
sys.modules['apps.voice_agent'] = MagicMock()
sys.modules['apps.voice_agent.bot_runner'] = MagicMock()
run_pipeline_mock = AsyncMock()
sys.modules['apps.voice_agent.bot_runner'].run_pipeline = run_pipeline_mock

# Create a simple FastAPI app for testing
app = FastAPI()

# Define the model for webhook payloads
class TelnyxCallInitRequest(BaseModel):
    """Telnyx webhook payload for call initialization."""
    call_control_id: str
    telnyx_rtc_session_id: str
    lang: Optional[str] = Field(default="en")

# Signature verification helper function
def verify_telnyx_signature(
    request_body: bytes,
    signature: str = Header(..., alias="X-Telnyx-Webhook-Signature")
) -> bool:
    """Verify Telnyx webhook signature using HMAC-SHA256."""
    secret = os.getenv("TELNYX_SIGNING_SECRET")
    if not secret:
        raise RuntimeError("TELNYX_SIGNING_SECRET environment variable not set")
    
    # Calculate HMAC-SHA256 signature
    computed_signature = hmac.new(
        secret.encode(),
        request_body,
        hashlib.sha256
    ).hexdigest()
    
    # Compare signatures using constant-time comparison
    return hmac.compare_digest(computed_signature, signature)

# Call initialization endpoint
@app.post("/voice/call-init")
async def call_init(
    request: Request,
    background_tasks: BackgroundTasks,
    is_valid: bool = Depends(verify_telnyx_signature)
):
    """
    Handle Telnyx call initialization webhook.
    
    This endpoint:
    1. Verifies the webhook signature
    2. Extracts call information
    3. Starts an asyncio task to run the voice pipeline
    """
    if not is_valid:
        raise HTTPException(status_code=403, detail="Invalid webhook signature")
    
    # Parse the request body
    body = await request.body()
    data = await request.json()
    
    # Extract required fields
    call_data = TelnyxCallInitRequest(**data)
    
    # Start the pipeline in a background task
    background_tasks.add_task(
        run_pipeline_mock,
        call_control_id=call_data.call_control_id,
        telnyx_rtc_session_id=call_data.telnyx_rtc_session_id,
        lang=call_data.lang
    )
    
    return JSONResponse(
        status_code=202,
        content={"message": "Call processing initiated"}
    )

# Add health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

# Create test client
client = TestClient(app)

# Sample payload for tests
SAMPLE_PAYLOAD = {
    "call_control_id": "test-call-123",
    "telnyx_rtc_session_id": "test-session-456",
    "lang": "en"
}

# Test helpers
def create_signature(payload: dict, secret: str) -> str:
    """Create a valid Telnyx signature for testing."""
    payload_bytes = json.dumps(payload).encode()
    return hmac.new(
        secret.encode(),
        payload_bytes,
        hashlib.sha256
    ).hexdigest()

# Test fixtures
@pytest.fixture
def mock_env_signing_secret(monkeypatch):
    """Fixture to set a mock signing secret."""
    monkeypatch.setenv("TELNYX_SIGNING_SECRET", "test-signing-secret")
    return "test-signing-secret"

# Test functions
def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_call_init_valid_signature(mock_env_signing_secret):
    """Test call init endpoint with valid signature."""
    payload = SAMPLE_PAYLOAD
    
    # Create valid signature
    signature = create_signature(payload, mock_env_signing_secret)
    
    # Make request with valid signature
    response = client.post(
        "/voice/call-init",
        json=payload,
        headers={"X-Telnyx-Webhook-Signature": signature}
    )
    
    # Assert response
    assert response.status_code == 202
    assert response.json() == {"message": "Call processing initiated"}
    
    # Verify run_pipeline was called with correct args
    run_pipeline_mock.assert_called_once_with(
        call_control_id=payload["call_control_id"],
        telnyx_rtc_session_id=payload["telnyx_rtc_session_id"],
        lang=payload["lang"]
    )

def test_call_init_invalid_signature(mock_env_signing_secret):
    """Test call init endpoint with invalid signature."""
    payload = SAMPLE_PAYLOAD
    
    # Use an invalid signature
    invalid_signature = "invalid-signature-123"
    
    # Make request with invalid signature
    response = client.post(
        "/voice/call-init",
        json=payload,
        headers={"X-Telnyx-Webhook-Signature": invalid_signature}
    )
    
    # Assert response
    assert response.status_code == 403
    assert "Invalid webhook signature" in response.text
    
    # Verify run_pipeline was NOT called
    run_pipeline_mock.assert_not_called()

def test_call_init_missing_signature(mock_env_signing_secret):
    """Test call init endpoint with missing signature header."""
    payload = SAMPLE_PAYLOAD
    
    # Make request without signature header
    response = client.post("/voice/call-init", json=payload)
    
    # Assert response - should fail with 422 Unprocessable Entity
    assert response.status_code == 422
    
    # Verify run_pipeline was NOT called
    run_pipeline_mock.assert_not_called()

def test_call_init_spanish_lang(mock_env_signing_secret):
    """Test call init with Spanish language parameter."""
    payload = {
        "call_control_id": "test-call-123",
        "telnyx_rtc_session_id": "test-session-456",
        "lang": "es"
    }
    
    # Create valid signature
    signature = create_signature(payload, mock_env_signing_secret)
    
    # Reset mock to clear previous calls
    run_pipeline_mock.reset_mock()
    
    # Make request with valid signature
    response = client.post(
        "/voice/call-init",
        json=payload,
        headers={"X-Telnyx-Webhook-Signature": signature}
    )
    
    # Assert response
    assert response.status_code == 202
    
    # Verify run_pipeline was called with Spanish language
    run_pipeline_mock.assert_called_once_with(
        call_control_id=payload["call_control_id"],
        telnyx_rtc_session_id=payload["telnyx_rtc_session_id"],
        lang="es"
    )


