import importlib
import sys
import types
from unittest.mock import patch, AsyncMock

import pytest


@pytest.fixture(autouse=True)
def auth_stub(monkeypatch):
    calls = []

    async def fake_get_access_token(firm_id: str, provider: str) -> str:
        calls.append((firm_id, provider))
        return f"token-{provider}"

    module = types.SimpleNamespace(get_access_token=fake_get_access_token)
    sys.modules['ailex_auth'] = module
    yield calls
    sys.modules.pop('ailex_auth')


@pytest.mark.asyncio
async def test_get_provider_google(auth_stub):
    from packages.calendar_core import get_provider
    from packages.calendar_core.adapters.google import GoogleCalendarProvider
    from unittest.mock import patch

    with patch('packages.calendar_core.adapters.google.get_access_token') as mock_auth:
        mock_auth.return_value = "token-google"

        provider = get_provider("firm1", "google")
        assert isinstance(provider, GoogleCalendarProvider)

        # Token is now fetched on-demand, so we need to trigger it
        token = await provider._get_token()
        assert token == "token-google"
        mock_auth.assert_called_with("firm1", "google")


@pytest.mark.asyncio
async def test_provider_methods(auth_stub):
    from packages.calendar_core import get_provider
    from unittest.mock import patch

    with patch('packages.calendar_core.adapters.calendly.get_access_token') as mock_auth:
        mock_auth.return_value = "token-calendly"

        provider = get_provider("firm1", "calendly")
        calendars = await provider.list_calendars()
        assert calendars

        event = await provider.create_event("primary", {"summary": "Test"})
        assert event["id"]

        # Verify auth was called for calendly (should be called twice - once for list_calendars, once for create_event)
        assert mock_auth.call_count >= 2, f"Expected at least 2 calendly auth calls, got: {mock_auth.call_count}"
