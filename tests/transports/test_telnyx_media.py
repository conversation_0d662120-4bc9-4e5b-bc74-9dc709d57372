import importlib.util
import math
import struct
from pathlib import Path
import audioop

ROOT = Path(__file__).resolve().parents[2]

spec = importlib.util.spec_from_file_location(
    "telnyx_transport", ROOT / "apps" / "voice-agent" / "transports" / "telnyx_transport.py"
)
module = importlib.util.module_from_spec(spec)
assert spec.loader is not None
spec.loader.exec_module(module)

_pcm_to_ulaw = module._pcm_to_ulaw
_ulaw_to_pcm = module._ulaw_to_pcm


def _rms(pcm: bytes) -> float:
    return audioop.rms(pcm, 2)


def test_ulaw_round_trip_rms():
    freq = 440
    duration = 0.1
    pcm_parts = []
    for i in range(int(16000 * duration)):
        val = int(32767 * 0.5 * math.sin(2 * math.pi * freq * i / 16000))
        pcm_parts.append(struct.pack("<h", val))
    pcm = b"".join(pcm_parts)
    ulaw = _pcm_to_ulaw(pcm)
    pcm_back = _ulaw_to_pcm(ulaw)
    rms_orig = _rms(pcm)
    rms_back = _rms(pcm_back)
    diff = abs(rms_orig - rms_back) / rms_orig
    assert diff <= 0.02
