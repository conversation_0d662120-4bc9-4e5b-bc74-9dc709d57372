import importlib.util
import time
from pathlib import Path

import pytest

ROOT = Path(__file__).resolve().parents[2]

spec = importlib.util.spec_from_file_location(
    "telnyx_transport", ROOT / "apps" / "voice-agent" / "transports" / "telnyx_transport.py"
)
module = importlib.util.module_from_spec(spec)
assert spec.loader is not None
spec.loader.exec_module(module)
TelnyxTransport = module.TelnyxTransport
TelnyxParams = module.TelnyxParams
VADBase = module.VADBase


@pytest.mark.asyncio
async def test_telnyx_greeting_within_800ms(telnyx_ws):
    params = TelnyxParams(
        api_key="key",
        sip_username="user",
        audio_out_enabled=True,
        vad_enabled=False,
        transcription_enabled=False,
        vad_analyzer=VADBase(),
    )
    transport = TelnyxTransport(params, url=telnyx_ws.url)
    start = time.perf_counter()
    async with transport:
        async for _ in transport:
            break
    elapsed = time.perf_counter() - start
    assert elapsed < 0.8
