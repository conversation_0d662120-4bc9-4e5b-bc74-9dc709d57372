"""Integration tests for voice booking with auth-service tokens."""
from __future__ import annotations

import pytest
from unittest.mock import patch, AsyncMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# Import using importlib since the directory has a hyphen
import importlib.util
spec = importlib.util.spec_from_file_location("voice_agent_booking", "apps/voice-agent/booking.py")
voice_agent_booking = importlib.util.module_from_spec(spec)
spec.loader.exec_module(voice_agent_booking)
get_calendar_provider = voice_agent_booking.get_calendar_provider
from packages.calendar_core.adapters.google import ProviderAuthError


@pytest.mark.asyncio
async def test_mock_provider_in_test_environment():
    """Test that mock provider is used when TESTING=true."""
    # Explicitly set test environment
    with patch.dict('os.environ', {'TESTING': 'true'}):
        provider = get_calendar_provider("firm123", "google")

        # Should be mock provider
        assert provider.__class__.__name__ == "MockCalendarProvider"

        # Mock provider should work without auth service
        calendars = await provider.list_calendars()
        assert len(calendars) == 1
        assert calendars[0]["id"] == "primary"
        assert calendars[0]["summary"] == "Mock Calendar"


@pytest.mark.asyncio
async def test_real_provider_uses_auth_service():
    """Test that real provider uses auth-service for token retrieval."""
    with patch('packages.calendar_core.adapters.google.get_access_token') as mock_auth:
        mock_auth.return_value = "test-access-token-123"

        # Set environment to use real provider
        with patch.dict('os.environ', {'TESTING': 'false'}):
            provider = get_calendar_provider("firm123", "google")

            # Should be real provider
            assert provider.__class__.__name__ == "GoogleCalendarProvider"

            # Calling _get_token should use auth service
            token = await provider._get_token()
            assert token == "test-access-token-123"

            # Verify auth was called with correct parameters
            mock_auth.assert_called_with("firm123", "google")


@pytest.mark.asyncio
async def test_auth_service_404_error_handling():
    """Test graceful handling of auth service 404 errors."""
    from ailex_auth import AuthServiceError

    with patch('packages.calendar_core.adapters.google.get_access_token') as mock_auth:
        # Mock 404 error from auth service
        mock_auth.side_effect = AuthServiceError("Token not found - calendar connection may need to be refreshed")

        with patch.dict('os.environ', {'TESTING': 'false'}):
            provider = get_calendar_provider("firm123", "google")

            with pytest.raises(ProviderAuthError) as exc_info:
                await provider._get_token()

            assert "calendar connection needs to be refreshed" in str(exc_info.value).lower()


@pytest.mark.asyncio
async def test_provider_selection_based_on_environment():
    """Test that provider selection works based on environment variables."""
    # Test environment uses mock
    with patch.dict('os.environ', {'TESTING': 'true'}):
        provider = get_calendar_provider("firm123", "google")
        assert provider.__class__.__name__ == "MockCalendarProvider"

    # Non-test environment uses real provider
    with patch.dict('os.environ', {'TESTING': 'false'}):
        provider = get_calendar_provider("firm123", "google")
        assert provider.__class__.__name__ == "GoogleCalendarProvider"

    # Default (no TESTING env var) uses real provider
    with patch.dict('os.environ', {}, clear=True):
        provider = get_calendar_provider("firm123", "google")
        assert provider.__class__.__name__ == "GoogleCalendarProvider"
