"""Test to ensure no hard-coded refresh tokens remain in the codebase."""
import os
import re
from pathlib import Path


def test_no_hardcoded_refresh_tokens():
    """Verify no hard-coded refresh tokens exist in the codebase."""
    # Patterns that might indicate hard-coded tokens
    token_patterns = [
        r'refresh_token\s*=\s*["\'][^"\']{20,}["\']',  # refresh_token = "long_string"
        r'refreshToken\s*:\s*["\'][^"\']{20,}["\']',   # refreshToken: "long_string"
        r'REFRESH_TOKEN\s*=\s*["\'][^"\']{20,}["\']',  # REFRESH_TOKEN = "long_string"
        r'1//[A-Za-z0-9_-]{40,}',                      # Google refresh token pattern
    ]
    
    # Directories to check
    check_dirs = [
        "apps/voice-agent",
        "packages/calendar_core",
        "packages/calendar",
    ]
    
    # Files to exclude (test files, examples, etc.)
    exclude_patterns = [
        r'test_.*\.py$',
        r'.*_test\.py$',
        r'.*\.test\.ts$',
        r'.*\.example$',
        r'.*\.md$',
        r'__pycache__',
        r'node_modules',
        r'\.git',
    ]
    
    violations = []
    
    for check_dir in check_dirs:
        if not os.path.exists(check_dir):
            continue
            
        for root, dirs, files in os.walk(check_dir):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not any(re.search(pattern, d) for pattern in exclude_patterns)]
            
            for file in files:
                # Skip excluded files
                if any(re.search(pattern, file) for pattern in exclude_patterns):
                    continue
                
                file_path = os.path.join(root, file)
                
                # Only check text files
                if not file.endswith(('.py', '.ts', '.js', '.json', '.yaml', '.yml')):
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for pattern in token_patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            violations.append(f"{file_path}:{line_num} - {match.group()}")
                            
                except (UnicodeDecodeError, PermissionError):
                    # Skip binary files or files we can't read
                    continue
    
    if violations:
        violation_msg = "\n".join(violations)
        raise AssertionError(f"Hard-coded tokens found:\n{violation_msg}")


def test_auth_service_integration_used():
    """Verify that auth service integration is properly used."""
    # Check that ailex_auth is imported in the right places
    google_provider_path = "packages/calendar_core/adapters/google.py"
    
    if os.path.exists(google_provider_path):
        with open(google_provider_path, 'r') as f:
            content = f.read()
        
        # Should import from ailex_auth
        assert "from ailex_auth import get_access_token" in content, \
            "GoogleCalendarProvider should import from ailex_auth"
        
        # Should have _get_token method
        assert "async def _get_token" in content, \
            "GoogleCalendarProvider should have _get_token method"
        
        # Should not have hard-coded tokens
        assert "refresh_token" not in content.lower() or "get_access_token" in content, \
            "Should use auth service instead of hard-coded tokens"


def test_voice_agent_booking_exists():
    """Verify voice agent booking module exists and has correct structure."""
    booking_path = "apps/voice-agent/booking.py"
    
    assert os.path.exists(booking_path), "Voice agent booking module should exist"
    
    with open(booking_path, 'r') as f:
        content = f.read()
    
    # Should have get_calendar_provider function
    assert "def get_calendar_provider" in content, \
        "Should have get_calendar_provider function"
    
    # Should import from packages.calendar_core
    assert "from packages.calendar_core" in content, \
        "Should import from calendar_core package"
    
    # Should have MockCalendarProvider for testing
    assert "class MockCalendarProvider" in content, \
        "Should have MockCalendarProvider for testing"
