import re
from pathlib import Path


def _get_value(text: str, key: str):
    pattern = rf"{key}:\s*(.+)"
    match = re.search(pattern, text)
    if not match:
        return None
    value = match.group(1).split('#')[0].strip()
    if value.lower() in {"true", "false"}:
        return value.lower() == "true"
    return value.strip("\"'")


def test_en_only_defaults():
    text = Path("apps/voice-svc/flows/intake.yaml").read_text()
    assert _get_value(text, "language_toggle") == "en"
    assert _get_value(text, "translate_transcript") is False


def test_en_to_es_translate_true():
    text = Path("apps/voice-svc/flows/intake.yaml").read_text()
    text = re.sub(r"language_toggle:\s*.+", "language_toggle: es", text)
    text = re.sub(r"translate_transcript:\s*.+", "translate_transcript: true", text)
    assert _get_value(text, "language_toggle") == "es"
    assert _get_value(text, "translate_transcript") is True
