"""
Tests for Google Calendar Push Notification webhook handler.

These tests verify that the webhook handler correctly processes push notifications
from Google Calendar and updates booking records accordingly.
"""

import os
import json
import uuid
from typing import Dict, Any
from datetime import datetime, timedelta
from unittest.mock import MagicMock
from pathlib import Path

import pytest
import respx
from httpx import Response
import pytest_asyncio
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select
from httpx import Response

from packages.shared import models
from apps.voice_svc.routes.api.calendar import google_push


# Load test fixtures
FIXTURES_DIR = Path(__file__).parent / "fixtures"

# Create a confirmed event for testing
CONFIRMED_EVENT = {
    "kind": "calendar#event",
    "etag": "3455543545347",
    "id": "test_event_123",
    "status": "confirmed",
    "htmlLink": "https://www.google.com/calendar/event?eid=test_event_123",
    "created": "2025-05-20T10:30:00.000Z",
    "updated": "2025-05-22T08:45:00.000Z",
    "summary": "Confirmed appointment",
    "creator": {
        "email": "<EMAIL>",
        "self": True
    },
    "organizer": {
        "email": "<EMAIL>",
        "self": True
    },
    "start": {
        "dateTime": "2025-05-23T15:00:00+02:00"
    },
    "end": {
        "dateTime": "2025-05-23T15:30:00+02:00"
    },
    "iCalUID": "<EMAIL>",
    "sequence": 1,
    "reminders": {
        "useDefault": True
    }
}

# Define cancelled event as a Python dictionary
CANCELLED_EVENT = {
    "kind": "calendar#event",
    "etag": "3455543545345",
    "id": "test_event_123",
    "status": "cancelled",
    "htmlLink": "https://www.google.com/calendar/event?eid=test_event_123",
    "created": "2025-05-20T10:30:00.000Z",
    "updated": "2025-05-22T08:45:00.000Z",
    "summary": "Cancelled appointment",
    "creator": {
        "email": "<EMAIL>",
        "self": True
    },
    "organizer": {
        "email": "<EMAIL>",
        "self": True
    },
    "start": {
        "dateTime": "2025-05-23T15:00:00+02:00"
    },
    "end": {
        "dateTime": "2025-05-23T15:30:00+02:00"
    },
    "iCalUID": "<EMAIL>",
    "sequence": 1,
    "reminders": {
        "useDefault": True
    }
}

# Define rescheduled event as a Python dictionary
RESCHEDULED_EVENT = {
    "kind": "calendar#event",
    "etag": "3455543545346",
    "id": "test_event_123",
    "status": "confirmed",
    "htmlLink": "https://www.google.com/calendar/event?eid=test_event_123",
    "created": "2025-05-20T10:30:00.000Z",
    "updated": "2025-05-22T08:45:00.000Z",
    "summary": "Rescheduled appointment",
    "creator": {
        "email": "<EMAIL>",
        "self": True
    },
    "organizer": {
        "email": "<EMAIL>",
        "self": True
    },
    "start": {
        "dateTime": "2025-05-24T16:00:00+02:00"
    },
    "end": {
        "dateTime": "2025-05-24T16:30:00+02:00"
    },
    "iCalUID": "<EMAIL>",
    "sequence": 2,
    "reminders": {
        "useDefault": True
    }
}


@pytest.fixture
def mock_env(monkeypatch):
    """Set environment variables for testing."""
    monkeypatch.setenv("GOOGLE_PUSH_TOKEN", "test_push_token")
    monkeypatch.setenv("GOOGLE_CLIENT_ID", "test_client_id")
    monkeypatch.setenv("GOOGLE_CLIENT_SECRET", "test_client_secret")
    monkeypatch.setenv("GOOGLE_REFRESH_TOKEN", "test_refresh_token")


@pytest_asyncio.fixture
async def client_and_session(mock_env, monkeypatch):
    """Setup test client and database session."""
    # Setup in-memory SQLite database with foreign key support disabled for testing
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    SessionLocal = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)

    # Mock database configuration
    monkeypatch.setattr(google_push, "engine", engine, raising=False)
    monkeypatch.setattr(google_push, "AsyncSessionLocal", SessionLocal, raising=False)
    
    # Create a simplified version of the Booking model for testing
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy import Column, String, DateTime, Enum as SAEnum
    from datetime import datetime
    
    TestBase = declarative_base()
    
    class TestBooking(TestBase):
        __tablename__ = "bookings"
        
        id = Column(String, primary_key=True)
        firm_id = Column(String, nullable=False)
        provider = Column(String, nullable=False)
        calendar_event_id = Column(String)
        start_at = Column(DateTime)
        end_ts = Column(DateTime)
        booked_at = Column(DateTime)
        status = Column(String)
        caller_name = Column(String)
        caller_email = Column(String)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        created_at = Column(DateTime, default=datetime.utcnow)
    
    # Override the Booking model with our test version
    monkeypatch.setattr(models, "Booking", TestBooking)
    
    # Create test tables
    async with engine.begin() as conn:
        await conn.run_sync(TestBase.metadata.create_all)

    # Create FastAPI test client
    app = FastAPI()
    app.include_router(google_push.router)

    # Yield client and session
    with TestClient(app) as client:
        yield client, SessionLocal

    # Cleanup
    await engine.dispose()


@pytest_asyncio.fixture
async def test_booking(client_and_session):
    """Create a test booking in the database."""
    _, SessionLocal = client_and_session

    # Create booking record
    booking_id = str(uuid.uuid4())
    firm_id = str(uuid.uuid4())
    start_time = datetime.utcnow() + timedelta(days=1)
    end_time = start_time + timedelta(minutes=30)

    async with SessionLocal() as session:
        booking = models.Booking(
            id=booking_id,
            firm_id=firm_id,
            provider="google",  # Using string since we're using a simplified model
            calendar_event_id="test_event_123",
            start_at=start_time,
            end_ts=end_time,
            booked_at=datetime.utcnow(),
            status="confirmed",
            caller_name="Test Caller",
            caller_email="<EMAIL>"
        )
        session.add(booking)
        await session.commit()

    return booking_id, firm_id, start_time, end_time


@pytest.mark.asyncio
@respx.mock
async def test_cancel_event_updates_booking_status(client_and_session, test_booking):
    """Test that cancelled events update booking status."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking

    # Mock Google OAuth token endpoint
    respx.post("https://oauth2.googleapis.com/token").mock(
        return_value=Response(
            200,
            json={"access_token": "mock_access_token", "expires_in": 3600}
        )
    )

    # Mock Google Calendar API event endpoint
    respx.get("https://www.googleapis.com/calendar/v3/events/test_event_123").mock(
        return_value=Response(200, json=CANCELLED_EVENT)
    )

    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123",
        "X-Goog-Channel-ID": "test_channel_id",
        "X-Goog-Message-Number": "1"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"

    # Verify booking was updated
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "cancelled"


@pytest.mark.asyncio
@respx.mock
async def test_reschedule_event_updates_booking_times(client_and_session, test_booking):
    """Test that rescheduled events update booking times."""
    client, SessionLocal = client_and_session
    booking_id, _, original_start, original_end = test_booking

    # Mock Google OAuth token endpoint
    respx.post("https://oauth2.googleapis.com/token").mock(
        return_value=Response(
            200,
            json={"access_token": "mock_access_token", "expires_in": 3600}
        )
    )

    # Mock Google Calendar API event endpoint
    respx.get("https://www.googleapis.com/calendar/v3/events/test_event_123").mock(
        return_value=Response(200, json=RESCHEDULED_EVENT)
    )

    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123",
        "X-Goog-Channel-ID": "test_channel_id",
        "X-Goog-Message-Number": "1"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"

    # Verify booking was updated with new times
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        
        # Confirm status is still confirmed
        assert booking.status == "confirmed"
        
        # Confirm times were updated (will be different from original)
        assert booking.start_at != original_start
        assert booking.end_ts != original_end
        
        # New times should match the rescheduled event - but we need to handle timezone conversions
        # Convert string dates from the fixture to datetime objects
        new_start_str = RESCHEDULED_EVENT["start"]["dateTime"]
        new_end_str = RESCHEDULED_EVENT["end"]["dateTime"]
        
        # Extract just the date and time part without timezone info
        # 2025-05-24T16:00:00+02:00 -> 2025-05-24T16:00:00
        if "+" in new_start_str:
            new_start_str = new_start_str.split("+")[0]
        if "+" in new_end_str:
            new_end_str = new_end_str.split("+")[0]
        
        # Parse into datetime objects (now both are offset-naive)
        expected_start = datetime.fromisoformat(new_start_str)
        expected_end = datetime.fromisoformat(new_end_str)
        
        # Format the booking datetimes for comparison, ignoring milliseconds
        booking_start_formatted = booking.start_at.strftime("%Y-%m-%d %H:%M:%S")
        expected_start_formatted = expected_start.strftime("%Y-%m-%d %H:%M:%S")
        booking_end_formatted = booking.end_ts.strftime("%Y-%m-%d %H:%M:%S")
        expected_end_formatted = expected_end.strftime("%Y-%m-%d %H:%M:%S")
        
        # Assert the formatted strings match instead of comparing datetime objects directly
        assert booking_start_formatted == expected_start_formatted
        assert booking_end_formatted == expected_end_formatted


@pytest.mark.asyncio
@respx.mock
async def test_invalid_token_returns_403(client_and_session):
    """Test that invalid tokens are rejected with 403 Forbidden."""
    client, _ = client_and_session

    # Send webhook notification with invalid token
    headers = {
        "X-Goog-Channel-Token": "invalid_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 403
    assert "Invalid channel token" in response.json()["detail"]


@pytest.mark.asyncio
@respx.mock
async def test_status_confirmed_updates_booking(client_and_session, test_booking):
    """Test that confirmed events update booking status."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Set initial booking status to something other than confirmed
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        booking.status = "tentative"
        await session.commit()

    # Mock Google OAuth token endpoint
    respx.post("https://oauth2.googleapis.com/token").mock(
        return_value=Response(
            200,
            json={"access_token": "mock_access_token", "expires_in": 3600}
        )
    )

    # Mock Google Calendar API event endpoint
    respx.get("https://www.googleapis.com/calendar/v3/events/test_event_123").mock(
        return_value=Response(200, json=CONFIRMED_EVENT)
    )

    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123",
        "X-Goog-Channel-ID": "test_channel_id",
        "X-Goog-Message-Number": "1"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"

    # Verify booking was updated
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "confirmed"


@pytest.mark.asyncio
@respx.mock
async def test_sync_state_updates_booking(client_and_session, test_booking):
    """Test that sync state updates booking status."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking

    # Mock Google OAuth token endpoint
    respx.post("https://oauth2.googleapis.com/token").mock(
        return_value=Response(
            200,
            json={"access_token": "mock_access_token", "expires_in": 3600}
        )
    )

    # Mock Google Calendar API event endpoint
    respx.get("https://www.googleapis.com/calendar/v3/events/test_event_123").mock(
        return_value=Response(200, json=CONFIRMED_EVENT)
    )

    # Send webhook notification with sync state
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "sync",  # Using sync state
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123",
        "X-Goog-Channel-ID": "test_channel_id",
        "X-Goog-Message-Number": "1"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
@respx.mock
async def test_missing_booking_handled_gracefully(client_and_session):
    """Test that notifications for unknown events are handled gracefully."""
    client, _ = client_and_session

    # Mock Google OAuth token endpoint
    respx.post("https://oauth2.googleapis.com/token").mock(
        return_value=Response(
            200,
            json={"access_token": "mock_access_token", "expires_in": 3600}
        )
    )

    # Mock Google Calendar API event endpoint
    respx.get("https://www.googleapis.com/calendar/v3/events/unknown_event_123").mock(
        return_value=Response(200, json=CONFIRMED_EVENT)
    )

    # Send webhook notification for an event that doesn't match any booking
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/unknown_event_123",
        "X-Goog-Channel-ID": "test_channel_id",
        "X-Goog-Message-Number": "1"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
@respx.mock
async def test_nonexistent_event_handled_gracefully(client_and_session):
    """Test that nonexistent events are handled gracefully."""
    client, _ = client_and_session

    # Mock Google OAuth token endpoint
    respx.post("https://oauth2.googleapis.com/token").mock(
        return_value=Response(
            200,
            json={"access_token": "mock_access_token", "expires_in": 3600}
        )
    )

    # Mock Google Calendar API event endpoint with 404
    respx.get("https://www.googleapis.com/calendar/v3/events/nonexistent_event").mock(
        return_value=Response(404, json={"error": "Not Found"})
    )

    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/nonexistent_event"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
@respx.mock
async def test_oauth_token_failure_handled(client_and_session, test_booking):
    """Test that OAuth token failures are handled gracefully."""
    client, _ = client_and_session
    
    # Mock Google OAuth token endpoint with failure
    respx.post("https://oauth2.googleapis.com/token").mock(
        return_value=Response(
            401,
            json={"error": "invalid_client", "error_description": "Invalid client credentials"}
        )
    )

    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
async def test_api_error_handled(client_and_session, monkeypatch):
    """Test that Google Calendar API errors are handled gracefully."""
    client, _ = client_and_session
    
    # Mock the fetch_event function to avoid API calls
    async def mock_fetch_event(event_id):
        # Return a minimal event with unknown status
        return {"id": event_id, "status": "unknown", "updated": datetime.utcnow().isoformat()}
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
@respx.mock(assert_all_mocked=False)  # Disable assert_all_mocked to avoid @pytest.mark.asyncio
async def test_not_exists_state_handled(client_and_session, monkeypatch):
    """Test that not_exists resource state is handled properly."""
    client, _ = client_and_session
    
    # Mock the fetch_event function to avoid actual API calls
    async def mock_fetch_event(event_id):
        return None  # Simulate no event found
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification with not_exists state
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "not_exists",  # Event doesn't exist anymore
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
async def test_webhook_with_json_body(client_and_session, monkeypatch):
    """Test webhook handling with a JSON body."""
    client, _ = client_and_session
    
    # Mock the fetch_event function to avoid API calls
    async def mock_fetch_event(event_id):
        return None
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification with a JSON body
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "sync",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
        "Content-Type": "application/json"
    }
    
    body = {
        "kind": "api#notification",
        "id": "test_notification_id",
        "resourceId": "test_resource_id",
        "resourceUri": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
        "resourceState": "sync"
    }
    
    response = client.post("/calendar/google/push", headers=headers, json=body)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
@respx.mock(assert_all_mocked=False)
async def test_missing_resource_uri_handled(client_and_session):
    """Test that a missing resource URI is handled gracefully."""
    client, _ = client_and_session
    
    # Send webhook notification without a resource URI
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists"
        # Intentionally omitting X-Goog-Resource-URI
    }
    
    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
async def test_invalid_uri_format_handled(client_and_session, monkeypatch):
    """Test that an invalid resource URI format is handled gracefully."""
    client, _ = client_and_session
    
    # Mock the fetch_event function to avoid API calls
    async def mock_fetch_event(event_id):
        return None
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification with invalid URI format
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/invalid/format"
    }
    
    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
async def test_unknown_resource_state_handled(client_and_session):
    """Test that an unknown resource state is handled gracefully."""
    client, _ = client_and_session
    
    # Send webhook notification with unknown resource state
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "unknown_state",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }
    
    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
async def test_tentative_status_updates_booking(client_and_session, monkeypatch, test_booking):
    """Test that tentative event status updates booking accordingly."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Mock the fetch_event function
    async def mock_fetch_event(event_id):
        return {
            "id": event_id,
            "status": "tentative",  # Tentative status
            "updated": datetime.utcnow().isoformat(),
            "start": None,
            "end": None
        }
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking status was updated to tentative
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "tentative"


@pytest.mark.asyncio
async def test_webhook_with_channel_id(client_and_session):
    """Test webhook handling with channel ID information."""
    client, _ = client_and_session
    
    # Send webhook notification with channel ID
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "sync",
        "X-Goog-Channel-ID": "test-channel-id-123",
        "X-Goog-Message-Number": "12345"
    }
    
    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
async def test_time_format_handling(client_and_session, monkeypatch, test_booking):
    """Test that different time formats in events are properly handled."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Mock the fetch_event function with Z-format dates
    async def mock_fetch_event(event_id):
        return {
            "id": event_id,
            "status": "confirmed",
            "updated": datetime.utcnow().isoformat(),
            "start": "2025-06-01T14:00:00Z",  # Z format instead of +00:00
            "end": "2025-06-01T14:30:00Z"
        }
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking times were updated correctly
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "confirmed"
        assert booking.start_at.strftime("%Y-%m-%d %H:%M") == "2025-06-01 14:00"
        assert booking.end_ts.strftime("%Y-%m-%d %H:%M") == "2025-06-01 14:30"


@pytest.mark.asyncio
async def test_cancelled_resource_state(client_and_session, monkeypatch, test_booking):
    """Test that cancelled resource state updates booking to cancelled."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Send webhook notification with cancelled resource state
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "cancelled",  # Explicitly testing 'cancelled' state
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking was marked as cancelled
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "cancelled"


@pytest.mark.asyncio
async def test_event_with_missing_times(client_and_session, monkeypatch, test_booking):
    """Test that events with missing start/end times are handled gracefully."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Store original times for comparison
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        original_start = booking.start_at
        original_end = booking.end_ts
    
    # Mock the fetch_event function with missing times
    async def mock_fetch_event(event_id):
        return {
            "id": event_id,
            "status": "confirmed",
            "updated": datetime.utcnow().isoformat(),
            "start": None,  # Missing start time
            "end": None    # Missing end time
        }
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking times were not updated when times are missing
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "confirmed"
        assert booking.start_at == original_start
        assert booking.end_ts == original_end


@pytest.mark.asyncio
async def test_json_body_handling(client_and_session):
    """Test webhook handling with a JSON body and content-type header."""
    client, _ = client_and_session
    
    # Send webhook notification with a JSON body
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "sync",
        "Content-Type": "application/json"
    }
    
    body = {
        "kind": "api#notification",
        "id": "test_notification_id",
        "resourceId": "test_resource_id",
        "resourceUri": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
        "resourceState": "sync"
    }
    
    response = client.post("/calendar/google/push", headers=headers, json=body)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"


@pytest.mark.asyncio
async def test_token_change_detection(client_and_session):
    """Test detection of token changes in Google webhook notifications."""
    client, _ = client_and_session
    
    # Send webhook notification with incorrect token
    headers = {
        "X-Goog-Channel-Token": "wrong_token",  # Wrong token should be detected
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    # Should get a 403 Forbidden response
    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 403
    assert "Invalid channel token" in response.json()["detail"]


@pytest.mark.asyncio
async def test_multiple_booking_updates(client_and_session, monkeypatch, test_booking):
    """Test that processing multiple notifications correctly updates the same booking."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Set calendar_event_id for our test booking
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        booking.calendar_event_id = "test_multiple_updates"
        await session.commit()
    
    # First mock: Change to tentative status
    async def mock_fetch_event_tentative(event_id):
        return {
            "id": event_id,
            "status": "tentative",
            "updated": datetime.utcnow().isoformat(),
            "start": datetime.now().isoformat(),
            "end": (datetime.now() + timedelta(minutes=30)).isoformat()
        }
    
    # Apply first monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event_tentative)
    
    # Send first webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_multiple_updates"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking was updated to tentative
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "tentative"
        tentative_updated_at = booking.updated_at
    
    # Second mock: Cancel the event
    async def mock_fetch_event_cancelled(event_id):
        return {
            "id": event_id,
            "status": "cancelled",
            "updated": datetime.utcnow().isoformat(),
            "start": datetime.now().isoformat(),
            "end": (datetime.now() + timedelta(minutes=30)).isoformat()
        }
    
    # Apply second monkeypatch
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event_cancelled)
    
    # Send second webhook notification
    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking was updated to cancelled and updated_at changed
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "cancelled"
        assert booking.updated_at > tentative_updated_at


@pytest.mark.asyncio
async def test_direct_event_status_cancellation(client_and_session, monkeypatch, test_booking):
    """Test that event status cancellation directly updates booking."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Mock the fetch_event function to return a cancelled event
    async def mock_fetch_event(event_id):
        return {
            "id": event_id,
            "status": "cancelled",  # Cancelled via event status
            "updated": datetime.utcnow().isoformat(),
            "start": "2025-06-01T14:00:00+02:00",
            "end": "2025-06-01T14:30:00+02:00"
        }
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification with exists state but cancelled event
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",  # Not cancelled state, but event itself is cancelled
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking was marked as cancelled
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "cancelled"


@pytest.mark.asyncio
async def test_comprehensive_booking_update(client_and_session, monkeypatch, test_booking):
    """Test a comprehensive booking update with time changes to reach all code paths."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # First, ensure the booking has a calendar_event_id
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        booking.calendar_event_id = "comprehensive_test_event"
        await session.commit()
    
    # Mock fetch_event to return an event with different times
    async def mock_fetch_event(event_id):
        if event_id == "comprehensive_test_event":
            # Return an event with different times than the original booking
            tomorrow = datetime.now() + timedelta(days=1)
            tomorrow_str = tomorrow.strftime("%Y-%m-%dT12:00:00")
            tomorrow_end_str = tomorrow.strftime("%Y-%m-%dT13:00:00")
            return {
                "id": event_id,
                "status": "confirmed",
                "updated": datetime.utcnow().isoformat(),
                "start": f"{tomorrow_str}+02:00",  # Different time than the booking
                "end": f"{tomorrow_end_str}+02:00"  # Different end time
            }
        return None  # For other event IDs
    
    # Apply monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/comprehensive_test_event"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking was updated with new times
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "confirmed"
        
        # Verify that the dates were updated to the new times
        tomorrow = datetime.now() + timedelta(days=1)
        booking_date = booking.start_at.date()
        assert booking_date == tomorrow.date()


@pytest.mark.asyncio
async def test_booking_status_with_unusual_values(client_and_session, monkeypatch, test_booking):
    """Test handling of unusual event status values."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Set calendar_event_id for our test booking
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        booking.calendar_event_id = "unusual_status_event"
        await session.commit()
    
    # Mock fetch_event to return an event with unusual status
    async def mock_fetch_event(event_id):
        if event_id == "unusual_status_event":
            return {
                "id": event_id,
                "status": "unknown_status",  # Unusual status
                "updated": datetime.utcnow().isoformat(),
                "start": datetime.now().isoformat(),
                "end": (datetime.now() + timedelta(minutes=30)).isoformat()
            }
        return None
    
    # Apply monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/unusual_status_event"
    }

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Since the status is not in [confirmed, tentative, cancelled], it should
    # keep its previous status but still update the updated_at timestamp
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        # Status should remain unchanged with an unusual event status
        assert booking.status == "confirmed"  # Original status from test_booking fixture


@pytest.mark.asyncio
async def test_confirmed_event_with_same_times(client_and_session, monkeypatch, test_booking):
    """Test that confirmed event without time changes doesn't update times."""
    client, SessionLocal = client_and_session
    booking_id, _, _, _ = test_booking
    
    # Get current booking times
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        original_start = booking.start_at
        original_end = booking.end_ts
    
    # Format the times to match what the API would return (removing microseconds)
    start_str = original_start.replace(microsecond=0).isoformat()
    end_str = original_end.replace(microsecond=0).isoformat()
    
    # Mock the fetch_event function with the same times
    async def mock_fetch_event(event_id):
        return {
            "id": event_id,
            "status": "confirmed",
            "updated": datetime.utcnow().isoformat(),
            "start": start_str,
            "end": end_str
        }
    
    # Apply the monkeypatch
    import sys
    from apps.voice_svc.routes.api.calendar.google_push import fetch_event
    monkeypatch.setattr(sys.modules[fetch_event.__module__], "fetch_event", mock_fetch_event)
    
    # Send webhook notification
    headers = {
        "X-Goog-Channel-Token": "test_push_token",
        "X-Goog-Resource-State": "exists",
        "X-Goog-Resource-URI": "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123"
    }

    # Store current booking timestamps for later comparison
    original_updated_at = None
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        original_updated_at = booking.updated_at

    response = client.post("/calendar/google/push", headers=headers)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    
    # Verify booking times remain unchanged but updated_at is changed
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        assert booking.status == "confirmed"
        # Check that start/end times remain the same (ignoring microseconds)
        assert booking.start_at.replace(microsecond=0) == original_start.replace(microsecond=0)
        assert booking.end_ts.replace(microsecond=0) == original_end.replace(microsecond=0)
        # The updated_at field should be updated
        assert booking.updated_at != original_updated_at
