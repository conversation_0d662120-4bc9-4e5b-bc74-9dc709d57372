"""
Tests for internal functions of the google_push module.
These tests are designed to target specific internal code paths to improve coverage.
"""
import asyncio
import os
import uuid
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
from fastapi import Request, Header, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from apps.voice_svc.routes.api.calendar.google_push import google_push, fetch_event
from packages.shared import models
from tests.calendar.test_push import client_and_session, test_booking


@pytest.mark.asyncio
async def test_fetch_event_function():
    """Test the fetch_event function directly."""
    # Mock the httpx.AsyncClient
    with patch('httpx.AsyncClient') as mock_client_class:
        # Set up the mock client
        mock_client = AsyncMock()
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        # Mock the token response
        mock_token_response = MagicMock()
        mock_token_response.status_code = 200
        mock_token_response.json.return_value = {
            "access_token": "mock_token",
            "expires_in": 3600
        }
        
        # Mock the event response
        mock_event_response = MagicMock()
        mock_event_response.status_code = 200
        mock_event_response.json.return_value = {
            "id": "test_event_123",
            "status": "confirmed",
            "updated": datetime.utcnow().isoformat(),
            "start": {"dateTime": "2025-06-01T14:00:00Z"},
            "end": {"dateTime": "2025-06-01T14:30:00Z"}
        }
        
        # Set up the client post/get method returns
        mock_client.post.return_value = mock_token_response
        mock_client.get.return_value = mock_event_response
        
        # Set environment variables
        os.environ["GOOGLE_CLIENT_ID"] = "test_client_id"
        os.environ["GOOGLE_CLIENT_SECRET"] = "test_client_secret"
        os.environ["GOOGLE_REFRESH_TOKEN"] = "test_refresh_token"
        
        # Call the function
        result = await fetch_event("test_event_123")
        
        # Verify results
        assert result["id"] == "test_event_123"
        assert result["status"] == "confirmed"
        assert "start" in result
        assert "end" in result
        
        # Verify calls
        mock_client.post.assert_called_once()
        mock_client.get.assert_called_once()


@pytest.mark.asyncio
async def test_google_push_function_direct(test_booking):
    """Test the google_push function directly."""
    booking_id, _, _, _ = test_booking
    
    # Create mock request and db
    mock_request = MagicMock(spec=Request)
    mock_request.json = AsyncMock(return_value={})
    mock_request.headers = {"content-type": "application/json"}
    
    # Create a real database session
    from tests.calendar.test_push import client_and_session
    _, SessionLocal = await client_and_session.__aenter__()
    
    # Get our test booking and set calendar_event_id
    async with SessionLocal() as session:
        result = await session.execute(
            select(models.Booking).where(models.Booking.id == booking_id)
        )
        booking = result.scalar_one()
        booking.calendar_event_id = "direct_test_event"
        await session.commit()
    
    # Mock fetch_event to ensure we hit all code paths
    async def mock_fetch_event(event_id):
        # First call returns updated time (for rescheduling)
        return {
            "id": event_id,
            "status": "confirmed",
            "updated": datetime.utcnow().isoformat(),
            "start": "2025-06-01T14:00:00Z",
            "end": "2025-06-01T14:30:00Z"
        }
    
    # Test with a direct call to google_push
    with patch('apps.voice_svc.routes.api.calendar.google_push.fetch_event', new=mock_fetch_event):
        async with SessionLocal() as db:
            # Call the function directly
            result = await google_push(
                request=mock_request,
                x_goog_channel_token="test_push_token",
                x_goog_resource_state="exists",
                x_goog_resource_uri=f"https://www.googleapis.com/calendar/v3/calendars/primary/events/direct_test_event",
                db=db
            )
            
            assert result["status"] == "ok"
            
            # Verify the booking was updated
            query_result = await db.execute(
                select(models.Booking).where(models.Booking.id == booking_id)
            )
            updated_booking = query_result.scalar_one()
            assert updated_booking.status == "confirmed"
            
            # Booking times should have been updated
            assert updated_booking.start_at is not None
            assert updated_booking.end_ts is not None
            
            # Test the date is as expected - June 1, 2025
            assert updated_booking.start_at.year == 2025
            assert updated_booking.start_at.month == 6
            assert updated_booking.start_at.day == 1


@pytest.mark.asyncio
async def test_event_id_extraction(test_booking):
    """Test extraction of event ID from various resource URIs."""
    booking_id, _, _, _ = test_booking
    
    # Create mock request and db
    mock_request = MagicMock(spec=Request)
    mock_request.json = AsyncMock(return_value={})
    
    # Test with different URI formats
    uri_test_cases = [
        # Standard format
        "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123",
        # With trailing slash
        "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123/",
        # With query parameters
        "https://www.googleapis.com/calendar/v3/calendars/primary/events/test_event_123?param=value",
        # With a complex ID containing special characters
        "https://www.googleapis.com/calendar/v3/calendars/primary/events/abc_123-456.789",
    ]
    
    # Create a real database session
    from tests.calendar.test_push import client_and_session
    _, SessionLocal = await client_and_session.__aenter__()
    
    # Mock fetch_event to return a cancelled event
    async def mock_fetch_event(event_id):
        return {
            "id": event_id,
            "status": "cancelled",
            "updated": datetime.utcnow().isoformat(),
            "start": None,
            "end": None
        }
    
    with patch('apps.voice_svc.routes.api.calendar.google_push.fetch_event', new=mock_fetch_event):
        for uri in uri_test_cases:
            async with SessionLocal() as db:
                # Set booking calendar_event_id to match the event ID in the URI
                event_id = uri.rstrip("/").split("/")[-1].split("?")[0]
                
                # Update the test booking to use this event ID
                result = await db.execute(
                    select(models.Booking).where(models.Booking.id == booking_id)
                )
                booking = result.scalar_one()
                booking.calendar_event_id = event_id
                booking.status = "confirmed"  # Reset status
                await db.commit()
                
                # Call google_push
                result = await google_push(
                    request=mock_request,
                    x_goog_channel_token="test_push_token",
                    x_goog_resource_state="exists",
                    x_goog_resource_uri=uri,
                    db=db
                )
                
                assert result["status"] == "ok"
                
                # Verify the booking was cancelled
                query_result = await db.execute(
                    select(models.Booking).where(models.Booking.id == booking_id)
                )
                updated_booking = query_result.scalar_one()
                assert updated_booking.status == "cancelled"
