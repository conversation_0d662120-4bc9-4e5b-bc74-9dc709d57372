import os
import sqlalchemy as sa
import pytest
from alembic import config as alembic_config, command

os.environ["ENVIRONMENT"] = "test"
from packages.shared import models


def test_booking_columns_exist():
    cols = models.Booking.__table__.columns.keys()
    assert "provider_event_link" in cols
    assert "recep_webhook_status" in cols
    assert "caller_name" in cols
    assert "caller_email" in cols
    assert "calendar_event_id" in cols
    assert "end_ts" in cols
    assert "status" in cols
    assert "updated_at" in cols


def test_booking_index_postgres_reflection():
    pytest.importorskip("psycopg2")
    testing_pg = pytest.importorskip("testing.postgresql")
    with testing_pg.Postgresql() as pg:
        engine = sa.create_engine(pg.url())
        firm_table = sa.Table(
            "firms",
            models.Base.metadata,
            sa.Column("id", sa.String, primary_key=True),
        )
        try:
            models.Base.metadata.create_all(engine)
            insp = sa.inspect(engine)
            indexes = insp.get_indexes("bookings")
            names = {idx["name"] for idx in indexes}
            assert "idx_bookings_firm_start" in names
        finally:
            models.Base.metadata.remove(firm_table)


def test_alembic_no_schema_changes(tmp_path):
    db_path = tmp_path / "alembic.db"
    engine = sa.create_engine(f"sqlite:///{db_path}")
    firm_table = sa.Table(
        "firms", models.Base.metadata, sa.Column("id", sa.String, primary_key=True)
    )
    models.Base.metadata.create_all(engine)
    cfg = alembic_config.Config("alembic.ini")
    cfg.set_main_option("sqlalchemy.url", f"sqlite:///{db_path}")
    command.stamp(cfg, "head")
    out = []
    cfg.print_stdout = lambda msg: out.append(msg)
    command.check(cfg)
    assert any("No new upgrade operations detected" in msg for msg in out)
    models.Base.metadata.remove(firm_table)
