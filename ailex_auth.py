"""
Auth service client for AiLex Receptionist.

This module provides integration with the auth-service for retrieving
access tokens for calendar providers.
"""
from __future__ import annotations

import os
from typing import Optional

import aiohttp


class AuthServiceError(Exception):
    """Raised when auth service requests fail."""
    pass


async def get_access_token(firm_id: str, provider: str) -> str:
    """
    Get access token from auth service.
    
    Args:
        firm_id: The firm identifier
        provider: The calendar provider (e.g., 'google', 'calendly')
        
    Returns:
        Access token string
        
    Raises:
        AuthServiceError: When token retrieval fails
    """
    auth_service_base = os.getenv("AUTH_SERVICE_BASE", "https://ailex-auth.fly.dev")
    url = f"{auth_service_base}/tokens/{firm_id}/{provider}"
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url) as response:
                if response.status == 404:
                    raise AuthServiceError("Token not found - calendar connection may need to be refreshed")
                elif response.status == 401:
                    raise AuthServiceError("Unauthorized - invalid firm credentials")
                elif response.status >= 400:
                    error_text = await response.text()
                    raise AuthServiceError(f"Auth service error: {error_text}")
                
                data = await response.json()
                return data.get("token", data.get("access_token", ""))
                
        except aiohttp.ClientError as e:
            raise AuthServiceError(f"Failed to connect to auth service: {e}") from e
